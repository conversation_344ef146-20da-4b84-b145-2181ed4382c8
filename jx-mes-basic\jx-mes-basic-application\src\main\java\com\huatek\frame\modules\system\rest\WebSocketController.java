package com.huatek.frame.modules.system.rest;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

@Controller
public class WebSocketController {

    @MessageMapping("/hello") // 客户端发送消息的路径
    @SendTo("/topic/greetings") // 广播到订阅的客户端
    public String greeting(String message) {
        return "Hello, " + message + "!";
    }
}

