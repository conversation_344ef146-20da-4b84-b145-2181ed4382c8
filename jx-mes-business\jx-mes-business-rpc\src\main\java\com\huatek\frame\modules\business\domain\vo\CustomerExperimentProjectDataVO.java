package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
* @description 试验项目数据
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("试验项目数据DTO实体类")
public class CustomerExperimentProjectDataVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private String id;

    
    /**
	 * 工序编码
     **/
    @ApiModelProperty("工序编码")
    @Excel(name = "工序编码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String standardProcessCode;

    
    /**
	 * 参数名称
     **/
    @ApiModelProperty("参数名称")
    @Excel(name = "参数名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String parameterName;

    
    /**
	 * 参数单位
     **/
    @ApiModelProperty("参数单位")
    @Excel(name = "参数单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String parameterUnit;

    
    /**
	 * 规范值
     **/
    @ApiModelProperty("规范值")
    @Excel(name = "规范值",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String specificationValue;

    /**
     * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;

    /**
     * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String codexTorchUpdater;

    /**
     * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
     * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
     * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;

}