package com.huatek.frame.modules.business.utils.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.utils.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

//避免小数被舍去
public class ExcelUtilTwo {
    //数据行序号
    private static Integer dataRowIndex;

    /**
     * 多sheet页写入数据
     *
     * @param templatePath
     * @param targetPath
     * @param sheets
     */
    public static void writeExcel(String templatePath, String targetPath, List<SheetInfo> sheets) {
        Workbook templateWorkBook = readExcel(templatePath);
        for (SheetInfo sheet : sheets) {
            writeExcel(templateWorkBook, templatePath, sheet);
        }
        writeExcel(templateWorkBook, targetPath);
    }

    /**
     * 写入数据
     *
     * @param templatePath
     * @param targetPath
     * @param datas
     * @return
     */
    public static Workbook writeExcel(String templatePath, String targetPath, String datas) {
        Workbook templateWorkBook = readExcel(templatePath);
        SheetInfo sheet = new SheetInfo();
        sheet.setIndex(0);
        sheet.setDatas(datas);
        sheet.setName("");
        writeExcel(templateWorkBook, templatePath, sheet);
        writeExcel(templateWorkBook, targetPath);
        return templateWorkBook;
    }

    /**
     * 写入数据
     *
     * @param templateWorkBook
     * @param templatePath
     * @param datas
     * @param sheetName
     */
    private static void writeExcel(Workbook templateWorkBook, String templatePath, SheetInfo sheet) {
        JSONObject dataObj = JSONObject.parseObject(sheet.getDatas());
        Pattern pattern = Pattern.compile("\\{\\{([a-zA-Z0-9\\(\\)\\.\\s]+)\\}\\}");
        List<Short> foot = new ArrayList<Short>();
        Sheet templateSheet = null;
        if (templateWorkBook != null) {
            templateSheet = templateWorkBook.getSheetAt(sheet.getIndex());
            if (!StringUtils.isBlank(sheet.getName())) {
                templateWorkBook.setSheetName(sheet.getIndex(), sheet.getName());
            }
            // 获取最大行数
            int rownum = templateSheet.getPhysicalNumberOfRows();
            DataModel dm = null;
            List<String> fields = new ArrayList<String>();
            int startRowsNum = 0;
            int endRowsNum = 0;
            short rowHeight = 0;
            boolean loop = false;
            CellStyle cs = null;
            for (int r = 0; r < rownum; r++) {
                Row row = templateSheet.getRow(r);
                if (null == row) continue;//空白行跳过不处理
                int colnum = row.getLastCellNum();
                if (endRowsNum > 0 && r > endRowsNum) {
                    foot.add(row.getHeight());
                }
                for (int c = 0; c < colnum; c++) {
                    String str = (String) getCellFormatValue(row.getCell(c));
                    Matcher matcher = pattern.matcher(str);
                    while (matcher.find()) {
                        String key = matcher.group(1);
                        if (key.startsWith("forEach")) {
                            Pattern kp = Pattern.compile("forEach\\(([a-zA-Z0-9\\.]+)\\)\\s+as\\s+([a-zA-Z0-9]+)");
                            Matcher km = kp.matcher(key);
                            while (km.find()) {
                                String ds = km.group(1);
                                String alias = km.group(2);
                                if (dm == null) {
                                    loop = true;
                                    startRowsNum = r;
                                    dm = new DataModel();
                                    dm.setName(alias);
                                    dm.setDataSet(ds);
                                } else {
                                    DataModel cdm = dm;
                                    while (cdm.getChild() != null) {
                                        cdm = cdm.getChild();
                                    }
                                    DataModel child = new DataModel();
                                    child.setName(alias);
                                    child.setDataSet(ds);
                                    cdm.setChild(child);
                                }
                            }
                        } else if ("endEach".equals(key)) {
                            if (c == 0) {
                                cs = row.getCell((short) c).getCellStyle();
                                rowHeight = row.getHeight();
                                loop = false;
                                endRowsNum = r;
                            }
                        } else if (loop) {
                            fields.add(key);
                            if ("index".equals(key)) {
                                dataRowIndex = 0;
                            }
                        } else {//非循环体数据填充
                            Cell cell = row.getCell((short) c);
                            cell.setCellValue(cell.getStringCellValue().replaceAll("\\{\\{" + key + "\\}\\}", dataObj.getString(key) == null ? "" : dataObj.getString(key)));
                        }
                    }
                }
            }
            //将自定义模板解析为数据模型
            DataModel dt = dm;
            while (dt != null) {
                List<String> fs = new ArrayList<String>();
                for (String f : fields) {
                    if (f.startsWith(dt.getName() + ".")) {
                        fs.add(f);
                    }
                }
                dt.setFields(fs);
                dt = dt.getChild();
            }
            List<Integer> amendRows = new ArrayList<Integer>();
            List<DataObj> rowDatas = getModelDatas(dm, fields, sheet.getDatas());
            deleteRows(templateSheet, startRowsNum, endRowsNum - startRowsNum + 1);
            createDataRows(templateSheet, startRowsNum, rowDatas, cs, rowHeight, fields, true, amendRows);
            //excel底部样式还原修正
            int num = cellRangeAddress(templateSheet, startRowsNum, rowDatas, fields);
            for (int i = 0; i < foot.size(); i++) {
                Row row = templateSheet.getRow(num + i);
                row.setHeight(foot.get(i));
            }

            if (amendRows.size() > 0) {
                for (Integer i : amendRows) {
                    Row r = templateSheet.getRow(i);
                    r.setHeight((short) 1000);
                }
            }
        }
    }

    /**
     * 写入循环数据行
     *
     * @param templateSheet
     * @param loopStartRowNum
     * @param rowDatas
     * @param cs
     * @param rowHeight
     * @param fields
     * @param start
     * @return
     */
    private static int createDataRows(Sheet templateSheet, int loopStartRowNum, List<DataObj> rowDatas, CellStyle cs, short rowHeight, List<String> fields, boolean start, List<Integer> amendRows) {
        for (int i = 0; i < rowDatas.size(); i++) {
            Row insertRow = null;
            if (start || i > 0) {
                if (templateSheet.getLastRowNum() > loopStartRowNum) {
                    templateSheet.shiftRows(loopStartRowNum, templateSheet.getLastRowNum(), 1);
                }
                insertRow = templateSheet.createRow(loopStartRowNum);
                insertRow.setHeight(rowHeight);
                loopStartRowNum++;
                if (dataRowIndex != null) {
                    dataRowIndex++;
                }
            } else {
                insertRow = templateSheet.getRow(loopStartRowNum - 1);
            }
            JSONObject d = JSONObject.parseObject(rowDatas.get(i).getDataJson());
            for (int j = 0; j < fields.size(); j++) {
                Cell cell = null;
                if (start || i > 0) {
                    cell = insertRow.createCell(j);
                    cell.setCellStyle(cs);
                } else {
                    cell = insertRow.getCell(j);
                }
                String key = fields.get(j).substring(fields.get(j).indexOf(".") + 1);
                if (d.containsKey(key)) {
                    cell.setCellValue(d.getString(key));
                } else if ("index".equals(key)) {
                    cell.setCellValue(dataRowIndex);
                }
            }
            if (rowDatas.get(i).getChildren() != null) {
                loopStartRowNum = createDataRows(templateSheet, loopStartRowNum, rowDatas.get(i).getChildren(), cs, rowHeight, fields, false, amendRows);
            }
        }
        return loopStartRowNum;
    }

    /**
     * 合并单元格
     */
    private static int cellRangeAddress(Sheet templateSheet, int startRow, List<DataObj> rowDatas, List<String> fields) {
        for (DataObj data : rowDatas) {
            if (data.getChildren() != null) {
                startRow += cellRangeAddress(templateSheet, data, startRow, fields);
            }
        }
        return startRow;
    }

    private static int cellRangeAddress(Sheet templateSheet, DataObj rowData, int startRow, List<String> fields) {
        int srt = startRow;
        int total = 0;
        if (rowData.getChildren() != null) {
            for (DataObj data : rowData.getChildren()) {
                total += cellRangeAddress(templateSheet, data, startRow + total, fields);
            }
            JSONObject d = JSONObject.parseObject(rowData.getDataJson());
            for (int i = 0; i < fields.size(); i++) {
                String key = fields.get(i).substring(fields.get(i).indexOf(".") + 1);
                if (d.containsKey(key)) {
                    CellRangeAddress region = new CellRangeAddress(srt, (srt + total - 1), i, i);
                    templateSheet.addMergedRegion(region);
                }
            }
            return total;
        } else {
            return 1;
        }
    }

    /**
     * 删除行
     *
     * @param templateSheet
     * @param startRowNum   删除起始行索引
     * @param deleteRows    删除的行数
     */
    private static void deleteRows(Sheet templateSheet, int startRowNum, int deleteRows) {
        for (int i = startRowNum; i < startRowNum + deleteRows; i++) {
            templateSheet.removeRow(templateSheet.getRow(i));
        }
        if (templateSheet.getLastRowNum() > startRowNum + deleteRows) {
            templateSheet.shiftRows(startRowNum + deleteRows, templateSheet.getLastRowNum(), -1 * deleteRows);
        }
    }

    /**
     * 数据模型化处理：将原始数据转化为默认数据模型
     *
     * @param dm
     * @param fields
     * @param datas
     * @return
     */
    private static List<DataObj> getModelDatas(DataModel dm, List<String> fields, String datas) {
        JSONObject dataObj = JSONObject.parseObject(datas);
        JSONArray dataSet = dataObj.getJSONArray(dm.getDataSet().substring(dm.getDataSet().indexOf(".") + 1));
        List<DataObj> res = new ArrayList<DataObj>();
        DataModel child = dm.getChild();
        for (int i = 0; i < dataSet.size(); i++) {
            DataObj doj = new DataObj();
            JSONObject d = new JSONObject();
            for (String f : fields) {
                if (dm.getFields().contains(f)) {
                    String key = f.substring(f.indexOf(".") + 1);
                    d.put(key, dataSet.getJSONObject(i).get(key));
                }
            }
            doj.setDataJson(d.toString());
            if (child != null) {
                JSONObject sub = new JSONObject();
                String sk = child.getDataSet().substring(child.getDataSet().indexOf(".") + 1);
                sub.put(sk, dataSet.getJSONObject(i).getString(sk));
                List<DataObj> children = getModelDatas(child, fields, sub.toString());
                doj.setChildren(children);
            }
            res.add(doj);
        }
        return res;
    }

    /**
     * 读取excel
     *
     * @param filePath
     * @return
     */
    private static Workbook readExcel(String filePath) {
        Workbook wb = null;
        if (filePath == null) {
            return null;
        }
        String extString = filePath.substring(filePath.lastIndexOf("."));
        InputStream is = null;
        try {
            is = new FileInputStream(filePath);
            if (".xls".equals(extString)) {
                wb = new HSSFWorkbook(is);
            } else if (".xlsx".equals(extString)) {
                wb = new XSSFWorkbook(is);
            } else {
                wb = null;
            }
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return wb;
    }

    /**
     * 读取单元格内容
     *
     * @param cell
     * @return
     */
    @SuppressWarnings("deprecation")
    private static Object getCellFormatValue(Cell cell) {
        Object cellValue = null;
        if (cell != null) {
            // 判断cell类型
            switch (cell.getCellType()) {
                case NUMERIC: {
                    DecimalFormat df = new DecimalFormat("0.00");
                    cellValue = df.format(cell.getNumericCellValue());
                    String temp = cellValue.toString();
                    if (temp.endsWith("0")) {
                        cellValue = temp.replaceAll("\\.0+$", "");
                    }
                    break;
                }
                case  FORMULA: {
                    // 判断cell是否为日期格式
                    if (DateUtil.isCellDateFormatted(cell)) {
                        // 转换为日期格式YYYY-mm-dd
                        cellValue = cell.getDateCellValue();
                    } else {
                        // 数字
                        cellValue = String.valueOf(cell.getNumericCellValue());
                    }
                    break;
                }
                case STRING: {
                    cellValue = cell.getRichStringCellValue().getString();
                    break;
                }
                default:
                    cellValue = "";
            }
        } else {
            cellValue = "";
        }
        return cellValue;
    }

    /**
     * 写入excel
     *
     * @param targetPath
     * @return
     */
    private static void writeExcel(Workbook wb, String targetPath) {
        try {
            File file = new File(targetPath);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();

            FileOutputStream out = new FileOutputStream(file);
            wb.write(out);
            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 读取复杂Excel文件数据
     *
     * @param is
     * @param fields        表身每一列对应类的属性集合，空表示所在列不与类属性做映射
     * @param clazz
     * @param prefix
     * @param headRows      表头所占行数
     * @param bodyStartRows 表身数据起始行数（行数从0开始）
     * @param footRows      底部非表身部分所占行数
     * @return
     */
    @SuppressWarnings("resource")
    public static Map<String, Object> readExcel(InputStream is, List<String> fields, Class<?> clazz, String prefix, int headRows, int bodyStartRows, int footRows) {
        Map<String, Object> map = new HashMap<String, Object>();
        try {
            Workbook wb = null;
            if ("xls".equals(prefix)) {
                wb = new HSSFWorkbook(is);
            } else if ("xlsx".equals(prefix)) {
                wb = new XSSFWorkbook(is);
            } else {
                return null;
            }
            Sheet sheet = wb.getSheetAt(0);
            int rownum = sheet.getPhysicalNumberOfRows();
            int maxRowNum = rownum;
            for (int r = rownum - 1; r >= 0; r--) {
                Row row = sheet.getRow(r);
                if (null != row) {
                    maxRowNum = r;
                    break;
                }
            }
            List<?> body = readExcel(sheet, fields, clazz, bodyStartRows, maxRowNum - footRows);
            map.put("body", body);
            if (headRows > 0) {
                List<List<String>> head = new ArrayList<List<String>>();
                for (int r = 0; r < headRows; r++) {
                    Row row = sheet.getRow(r);
                    if (null == row) continue;//空白行跳过不处理
                    int colnum = row.getLastCellNum();
                    List<String> rows = new ArrayList<String>();
                    for (int c = 0; c < colnum; c++) {
                        String str = (String) getCellFormatValue(row.getCell(c));
                        rows.add(str);
                    }
                    head.add(rows);
                }
                map.put("head", head);
            }

            if (footRows > 0) {
                List<List<String>> foot = new ArrayList<List<String>>();
                for (int r = 0; r < footRows; r++) {
                    Row row = sheet.getRow(maxRowNum - r);
                    if (null == row) continue;//空白行跳过不处理
                    int colnum = row.getLastCellNum();
                    List<String> rows = new ArrayList<String>();
                    for (int c = 0; c < colnum; c++) {
                        String str = (String) getCellFormatValue(row.getCell(c));
                        rows.add(str);
                    }
                    foot.add(rows);
                }
                map.put("foot", foot);
            }
        } catch (Exception e) {
            throw new RuntimeException("请选择正确模板导入");
        }
        return map;
    }

    /**
     * 读取表身数据集合
     *
     * @param is
     * @param fields
     * @param clazz
     * @param prefix
     * @param startRow
     * @return
     * @throws Exception
     */
    private static <T> List<T> readExcel(Sheet dataSheet, List<String> fields, Class<T> clazz, int startRow, int endRows) throws Exception {
        List<T> list = new ArrayList<T>();
        Map<String, String> values = new HashMap<String, String>();
        for (int r = startRow; r <= endRows; r++) {
            Row row = dataSheet.getRow(r);
            if (null == row) continue;//空白行跳过不处理
            int colnum = row.getLastCellNum();
            T t = null;
            for (int c = 0; c < colnum; c++) {
                if (c >= fields.size()) continue;
                String s = isMergedRow(dataSheet, r, c);
                String str = null;
                if ("pt".equals(s)) {
                    str = (String) getCellFormatValue(row.getCell(c));
                } else if ("value".equals(s)) {
                    str = (String) getCellFormatValue(row.getCell(c));
                    values.put(r + "-" + c, str);
                } else {
                    str = values.get(s);
                }
                if (StringUtils.isBlank(str)) continue;
                if (t == null) {
                    t = clazz.newInstance();
                }
                String fieldName = fields.get(c);
                Method getMethod = clazz.getDeclaredMethod("get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1));
                Type type = getMethod.getGenericReturnType();
                if ("long".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), long.class);
                    sm.invoke(t, Long.valueOf(str));
                } else if ("int".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), int.class);
                    sm.invoke(t, Integer.valueOf(str));
                } else if ("short".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), short.class);
                    sm.invoke(t, Short.valueOf(str));
                } else if ("float".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), float.class);
                    sm.invoke(t, Float.valueOf(str));
                } else if ("double".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), double.class);
                    sm.invoke(t, Double.valueOf(str));
                } else if ("boolean".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), boolean.class);
                    sm.invoke(t, Boolean.valueOf(str));
                } else if ("byte".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), byte.class);
                    sm.invoke(t, Byte.valueOf(str));
                } else if ("char".equals(type.toString())) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), char.class);
                    sm.invoke(t, Character.valueOf(str.charAt(0)));
                } else if ("Date".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Date.class);
                    sm.invoke(t, stringToDate(str));
                } else if ("String".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), String.class);
                    sm.invoke(t, str);
                } else if ("Long".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Long.class);
                    sm.invoke(t, Long.valueOf(str));
                } else if ("Integer".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Integer.class);
                    sm.invoke(t, Integer.valueOf(str));
                } else if ("Short".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Short.class);
                    sm.invoke(t, Short.valueOf(str));
                } else if ("Float".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Float.class);
                    sm.invoke(t, Float.valueOf(str));
                } else if ("Double".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Double.class);
                    sm.invoke(t, Double.valueOf(str));
                } else if ("Boolean".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Boolean.class);
                    sm.invoke(t, Boolean.valueOf(str));
                } else if ("Byte".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Byte.class);
                    sm.invoke(t, Byte.valueOf(str));
                } else if ("Character".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), Character.class);
                    sm.invoke(t, Character.valueOf(str.charAt(0)));
                } else if ("BigDecimal".equals(type.toString().substring(type.toString().lastIndexOf(".") + 1))) {
                    Method sm = clazz.getDeclaredMethod("set" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1), BigDecimal.class);
                    sm.invoke(t, BigDecimal.valueOf(str.charAt(0)));
                }
            }
            if (t != null) {
                list.add(t);
            }
        }
        return list;
    }

    private static Date stringToDate(String str) throws ParseException {
        if (StringUtils.isBlank(str)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String regex = "^(\\d{4})[-/]{1}(\\d{1,2})[-/]{1}(\\d{1,2})(\\s+(\\d{1,2}):(\\d{1,2}):(\\d{1,2}))?$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(str);
        String dt = null;
        while (matcher.find()) {
            String year = matcher.group(1);
            String month = matcher.group(2).length() == 1 ? "0" + matcher.group(2) : matcher.group(2);
            String day = matcher.group(3).length() == 1 ? "0" + matcher.group(3) : matcher.group(3);
            String h = matcher.group(5) == null ? "00" : (matcher.group(5).length() == 1 ? "0" + matcher.group(5) : matcher.group(5));
            String m = matcher.group(6) == null ? "00" : (matcher.group(5).length() == 1 ? "0" + matcher.group(6) : matcher.group(6));
            String s = matcher.group(7) == null ? "00" : (matcher.group(5).length() == 1 ? "0" + matcher.group(7) : matcher.group(7));
            dt = year + "-" + month + "-" + day + " " + h + ":" + m + ":" + s;
        }
        return sdf.parse(dt);
    }

    private static String isMergedRow(Sheet sheet, int row, int column) {
        int sheetMergeCount = sheet.getNumMergedRegions();
        for (int i = 0; i < sheetMergeCount; i++) {
            CellRangeAddress range = sheet.getMergedRegion(i);
            int firstColumn = range.getFirstColumn();
            int lastColumn = range.getLastColumn();
            int firstRow = range.getFirstRow();
            int lastRow = range.getLastRow();
            if (firstColumn <= column && column <= lastColumn && firstRow <= row && row <= lastRow) {
                if (column == firstColumn && row == firstRow) {
                    return "value";//合并单元格
                } else {
                    return firstRow + "-" + firstColumn;//被合并单元格返回取值单元格坐标
                }
            }
        }
        return "pt";//普通单元格
    }

    /**
     * 获取被合并单元格所在合并区域范围
     * @param sheet
     * @param row
     * @param column
     * @return
     */
//	private static String mergedRange(Sheet sheet, int row, int column) {
//		int sheetMergeCount = sheet.getNumMergedRegions();
//		for(int i = 0; i < sheetMergeCount; i++) {
//			CellRangeAddress range = sheet.getMergedRegion(i);
//			int firstColumn = range.getFirstColumn();
//			int lastColumn = range.getLastColumn();
//			int firstRow = range.getFirstRow();
//			int lastRow = range.getLastRow();
//            if(firstColumn <= column && column <= lastColumn && firstRow <= row && row <= lastRow) {
//            	return firstRow + "-" + firstColumn + "-" + lastRow + "-" + lastColumn;
//            }
//		}
//		return null;
//	}

    /**
     * 将指定区域的单元格合并并设置图片
     *
     * @param wb
     * @param sheet
     * @param startRow
     * @param startColumn
     * @param endRow
     * @param endColumn
     * @param img
     */
    public static void insertImage(Workbook wb, Sheet sheet, int startRow, int startColumn, int endRow, int endColumn, BufferedImage img) {
        //createRangeAddress(sheet, startRow, startColumn, endRow, endColumn);
//        try {
//        	ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//            ImageIO.write(img, "jpg", byteArrayOut);
//        	if(sheet instanceof HSSFSheet) {
//    			HSSFPatriarch patriarch = (HSSFPatriarch) sheet.createDrawingPatriarch();
//    			//HSSFClientAnchor参数依次：起始单元格的X坐标值, 起始单元格的Y坐标值, 结束单元格的X坐标值, 结束单元格的Y坐标值, 起始单元格的列索引, 起始单元格的行索引, 结束单元格的列索引, 结束单元格的行索引
//    			HSSFClientAnchor anchor = new HSSFClientAnchor(8, 3, 1022, 254,(short) startColumn, startRow, (short) endColumn, endRow);
//    			anchor.setAnchorType(4);
//    			patriarch.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), HSSFWorkbook.PICTURE_TYPE_JPEG));
//    		} else if(sheet instanceof XSSFSheet) {
//    			int inx = wb.addPicture(byteArrayOut.toByteArray(), XSSFWorkbook.PICTURE_TYPE_JPEG);
//    			XSSFDrawing drawing = (XSSFDrawing)sheet.createDrawingPatriarch();
//    			XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 255, 255,(short) startColumn, startRow, (short) endColumn, endRow);
//    			//anchor.setAnchorType(4);
//    			XSSFPicture picture = drawing.createPicture(anchor, inx);
//    			picture.resize();
//    		}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
    }

    /**
     * 将指定区域的单元格合并并设置图片
     *
     * @param wb
     * @param sheet
     * @param startRow
     * @param startColumn
     * @param endRow
     * @param endColumn
     * @param image
     */
    public static void insertImage(Workbook wb, Sheet sheet, int startRow, int startColumn, int endRow, int endColumn, File image) {
        //createRangeAddress(sheet, startRow, startColumn, endRow, endColumn);
//        try {
//        	ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
//			BufferedImage img = ImageIO.read(image);
//            ImageIO.write(img, "jpg", byteArrayOut);
//        	if(sheet instanceof HSSFSheet) {
//    			HSSFPatriarch patriarch = (HSSFPatriarch) sheet.createDrawingPatriarch();
//    			//HSSFClientAnchor参数依次：起始单元格的X坐标值, 起始单元格的Y坐标值, 结束单元格的X坐标值, 结束单元格的Y坐标值, 起始单元格的列索引, 起始单元格的行索引, 结束单元格的列索引, 结束单元格的行索引
//    			HSSFClientAnchor anchor = new HSSFClientAnchor(8, 3, 1022, 254,(short) startColumn, startRow, (short) endColumn, endRow);
//    			anchor.setAnchorType(3);
//    			patriarch.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), HSSFWorkbook.PICTURE_TYPE_JPEG));
//    		} else if(sheet instanceof XSSFSheet) {
//    			int inx = wb.addPicture(byteArrayOut.toByteArray(), XSSFWorkbook.PICTURE_TYPE_JPEG);
//    			XSSFDrawing drawing = (XSSFDrawing)sheet.createDrawingPatriarch();
//    			XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 255, 255,(short) startColumn, startRow, (short) endColumn, endRow);
//    			XSSFPicture picture = drawing.createPicture(anchor, inx);
//    			picture.resize();
//    		}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
    }

    /**
     * 合并单元格
     *
     * @param sheet
     * @param startRow    起始单元格的行索引
     * @param startColumn 起始单元格的列索引
     * @param endRow      结束单元格的行索引
     * @param endColumn   结束单元格的列索引
     */
    public static void createRangeAddress(Sheet sheet, int startRow, int startColumn, int endRow, int endColumn) {
        CellRangeAddress region = new CellRangeAddress(startRow, endRow, startColumn, endColumn);
        sheet.addMergedRegion(region);
    }

    /**
     *
     */
    public static void exportTemplate(String fileName, String fileTemplatePath, String datas, HttpServletRequest request, HttpServletResponse response) {
        String templateRoot = request.getSession().getServletContext().getRealPath("/") + "template\\";
        OutputStream os = null;
        InputStream in = null;
        File file = null;
        try {
            String agent = request.getHeader("USER-AGENT").toLowerCase();
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            String fileNamePath = templateRoot + fileName;
            ExcelUtil.writeExcel(templateRoot + fileTemplatePath, fileNamePath, datas);
            String codedFileName = URLEncoder.encode(".xlsx", "UTF-8");
            if (agent.contains("firefox")) {
                response.setHeader("Content-Disposition", "attachment; filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
            } else {
                response.setHeader("content-disposition", "attachment;filename=" + codedFileName);
            }
            file = new File(fileNamePath);
            os = response.getOutputStream();
            in = new FileInputStream(file);
            byte[] buffer = new byte[(int) file.length()];
            while (in.read(buffer) > 0) {
                os.write(buffer);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                os.flush();
                in.close();
                os.close();
                file.delete();
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
    }

    public static void exportExcel(HttpServletResponse response, Class clzz, List datas,String fileName,String sheetNama){
        if(StringUtils.isEmpty(sheetNama)){
            sheetNama = fileName;
        }
        ServletOutputStream outputStream;
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.ms-excel");
            String fileNameStr = URLEncoder.encode(fileName,"UTF-8") ;
            response.setHeader("Content-Disposition", "attachment;filename="+fileNameStr);
            response.setHeader("filename", fileNameStr);
            outputStream = response.getOutputStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        // CustomCellWriteWeightConfig 设置列宽度自适应
        EasyExcel.write(outputStream, clzz).sheet(1,sheetNama).registerWriteHandler(new CustomCellWriteWeightConfig()).doWrite(datas);
    }
}
