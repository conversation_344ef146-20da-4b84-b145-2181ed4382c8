package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 核算标准VO实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("核算标准DTO实体类")
public class AccountingStandardVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 收费标准编号
     **/
    @ApiModelProperty("收费标准编号")
    @Excel(name = "收费标准编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargeStandardNumber;

    /**
	 * 收费标准名称
     **/
    @ApiModelProperty("收费标准名称")
    @Excel(name = "收费标准名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargingStandardName;

    /**
	 * 收费标准类型
     **/
    @ApiModelProperty("收费标准类型")
    @Excel(name = "收费标准类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String chargeStandardType;

    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    @Excel(name = "试验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String testType;

    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    @Excel(name = "结算单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String settlementUnit;
    private String settlementUnitName;


    /**
	 * 核算方式
     **/
    @ApiModelProperty("核算方式")
    @Excel(name = "核算方式",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String accountingMethod;

    /**
	 * 折扣
     **/
    @ApiModelProperty("折扣")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "折扣",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal discount;

    /**
	 * 子表明细项ID
     **/
    @ApiModelProperty("子表明细项ID")
    private String codexTorchDetailItemIds;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    private String codexTorchCreatorName;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}