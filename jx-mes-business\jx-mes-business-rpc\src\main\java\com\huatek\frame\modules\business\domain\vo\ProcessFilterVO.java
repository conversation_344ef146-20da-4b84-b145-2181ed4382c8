package com.huatek.frame.modules.business.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 生产工单下载工单报告-筛选流程实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProcessFilterVO {
    /**
     * 序号
     */
    private Integer displayNumber;

    /**
     * 试验项目
     */
    private String experimentProject;

    /**
     * 试验条件
     */
    private String testConditions;

    /**
     * 设备编号
     */
    private String deviceSerialNumber;

    /**
     * 合格数量
     */
    private Integer qualifiedQuantity;

    /**
     * 不合格数量
     */
    private Integer unqualifiedQuantity;

    /**
     * 失效模式
     */
    private String failureMode;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 技术员
     */
    private String reporter4;

    /**
     * 时间(实际开始时间)
     */
    private String actualStartTime;





}
