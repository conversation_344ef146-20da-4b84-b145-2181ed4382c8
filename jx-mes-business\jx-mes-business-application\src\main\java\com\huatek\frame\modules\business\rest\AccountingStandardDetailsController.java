package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO;
import com.huatek.frame.modules.business.service.AccountingStandardDetailsService;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDetailsDTO;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-22
**/
@Api(tags = "核算标准明细管理")
@RestController
@RequestMapping("/api/accountingStandardDetails")
public class AccountingStandardDetailsController {

	@Autowired
    private AccountingStandardDetailsService accountingStandardDetailsService;

	/**
	 * 核算标准明细列表
	 * 
	 * @param dto 核算标准明细DTO 实体对象
	 * @return
	 */
    @Log("核算标准明细列表")
    @ApiOperation(value = "核算标准明细列表查询")
    @PostMapping(value = "/accountingStandardDetailsList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandardDetails:list")
    public TorchResponse<List<AccountingStandardDetailsVO>> query(@RequestBody AccountingStandardDetailsDTO dto){
        return accountingStandardDetailsService.findAccountingStandardDetailsPage(dto);
    }

	/**
	 * 新增/修改核算标准明细
	 * 
	 * @param accountingStandardDetailsDto 核算标准明细DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改核算标准明细")
    @ApiOperation(value = "核算标准明细新增/修改操作")
    @PostMapping(value = "/accountingStandardDetails", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandardDetails:add#accountingStandardDetails:edit")
    public TorchResponse add(@RequestBody AccountingStandardDetailsDTO accountingStandardDetailsDto) throws Exception {
		// BeanValidatorFactory.validate(accountingStandardDetailsDto);
		return accountingStandardDetailsService.saveOrUpdate(accountingStandardDetailsDto);
	}

	/**
	 * 查询核算标准明细详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("核算标准明细详情")
    @ApiOperation(value = "核算标准明细详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("accountingStandardDetails:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return accountingStandardDetailsService.findAccountingStandardDetails(id);
	}

	/**
	 * 删除核算标准明细
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除核算标准明细")
    @ApiOperation(value = "核算标准明细删除操作")
    @TorchPerm("accountingStandardDetails:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody String[] ids) {
		return accountingStandardDetailsService.delete(ids);
	}

    @ApiOperation(value = "核算标准明细联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return accountingStandardDetailsService.getOptionsList(id);
	}





    @Log("核算标准明细导出")
    @ApiOperation(value = "核算标准明细导出")
    @TorchPerm("accountingStandardDetails:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody AccountingStandardDetailsDTO dto)
    {
        List<AccountingStandardDetailsVO> list = accountingStandardDetailsService.selectAccountingStandardDetailsList(dto);
        ExcelUtil<AccountingStandardDetailsVO> util = new ExcelUtil<AccountingStandardDetailsVO>(AccountingStandardDetailsVO.class);
        util.exportExcel(response, list, "核算标准明细数据");
    }

    @Log("核算标准明细导入")
    @ApiOperation(value = "核算标准明细导入")
    @TorchPerm("accountingStandardDetails:import")
    @PostMapping("/importData/{accountingStandardId}")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns,@PathVariable(value = "accountingStandardId") String accountingStandardId) throws Exception
    {
        ExcelUtil<AccountingStandardDetailsVO> util = new ExcelUtil<AccountingStandardDetailsVO>(AccountingStandardDetailsVO.class);
        List<AccountingStandardDetailsVO> list = util.importExcel(file.getInputStream());
        return accountingStandardDetailsService.importAccountingStandardDetails(list, unionColumns, true, "",accountingStandardId);
    }

    @Log("核算标准明细导入模板")
    @ApiOperation(value = "核算标准明细导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<AccountingStandardDetailsVO> util = new ExcelUtil<AccountingStandardDetailsVO>(AccountingStandardDetailsVO.class);
        util.importTemplateExcel(response, "核算标准明细数据");
    }

    @Log("根据Ids获取核算标准明细列表")
    @ApiOperation(value = "核算标准明细 根据Ids批量查询")
    @PostMapping(value = "/accountingStandardDetailsList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getAccountingStandardDetailsListByIds(@RequestBody List<String> ids) {
        return accountingStandardDetailsService.selectAccountingStandardDetailsListByIds(ids);
    }




}