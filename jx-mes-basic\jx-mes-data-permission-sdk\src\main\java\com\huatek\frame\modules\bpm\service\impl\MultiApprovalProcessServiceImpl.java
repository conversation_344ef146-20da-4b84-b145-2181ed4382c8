package com.huatek.frame.modules.bpm.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.modules.bpm.constant.ApprovalAct;
import com.huatek.frame.modules.bpm.constant.ApprovalStatus;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.bpm.domain.ProcessHistory;
import com.huatek.frame.modules.bpm.domain.ProcessHistoryItem;
import com.huatek.frame.modules.bpm.dto.ProcessFormDTO;
import com.huatek.frame.modules.bpm.modeler.MultiApprovalBpmnViewer;
import com.huatek.frame.modules.bpm.service.MultiApprovalProcessService;
import com.huatek.frame.modules.bpm.service.ProcessPrivilegeService;
import com.huatek.frame.modules.bpm.util.ApprovalRecordUtil;
import com.huatek.frame.modules.bpm.util.HistoricTaskUtil;
import com.huatek.frame.modules.bpm.util.ProcessDefinitionParseUtil;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.SysProcessDefinitionVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.system.service.SysProcessDefinitionService;
import com.huatek.frame.modules.system.service.SysProcessRecordService;
import com.huatek.frame.modules.system.service.SysUserService;
import com.huatek.frame.modules.system.service.dto.RoleDTO;
import com.huatek.frame.modules.system.service.dto.SysProcessRecordDTO;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.history.HistoricTaskInstance;
import org.camunda.bpm.engine.history.HistoricVariableInstance;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.camunda.bpm.engine.variable.VariableMap;
import org.camunda.bpm.engine.variable.Variables;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Service("multiApproverProcessService")
@Scope("prototype")
@Slf4j
public class MultiApprovalProcessServiceImpl implements MultiApprovalProcessService {
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private IdentityService identityService;
    @Autowired
    private SecurityUser securityUser;
    @Autowired
    private TaskService taskService;
    @Autowired
    private FormService formService;
    @Autowired
    ProcessEngine processEngine;

    @Autowired
    private HistoryService historyService;

    @Autowired(required = false)
    private SysProcessRecordService sysProcessRecordService;

    @Autowired
    private MultiApprovalBpmnViewer multiApproverBpmnViewer;

    @Autowired(required = false)
    private SysProcessDefinitionService sysProcessDefinitionService;

    @Autowired
    private ProcessPrivilegeService processPrivilegeService;

    @Autowired(required = false)
    private SysUserService sysUserService;

    private String processDefinitionKey;

    @Override
    public String getProcessDefinitionKey() {
        return processDefinitionKey;
    }

    @Override
    public SysProcessRecordVO startProcessByKey(ProcessFormDTO processFormDTO, String token) {
        VariableMap variableMap = Variables.createVariables();

        TorchResponse<SysProcessDefinitionVO> recordsResponse = sysProcessDefinitionService.findSysProcessDefinitionByBusinessKey(null,processFormDTO.getBusinessKey());
        SysProcessDefinitionVO sysProcessDefinitionVO = recordsResponse.getData().getData();

        if(StringUtils.isEmpty(sysProcessDefinitionVO.getProcessDefinitionKey())){
            throw new ServiceException("多级审批流程服务, 未找到指对应的的流程定义，业务键：" + processFormDTO.getBusinessKey());
        }else{
            this.processDefinitionKey = sysProcessDefinitionVO.getProcessDefinitionKey();
        }

        if(sysProcessDefinitionVO.getApprovalMode().equalsIgnoreCase(ProcessConstant.PROCESS_DEF_ASSIGNEE_TYPE_USER)){
            processFormDTO.setProcessDefinitionApprover(sysProcessDefinitionVO.getParticipant());
            processFormDTO.setProcessDefinitionApproveRole("");

            String[] approvers = updateProcessVariableApprovers(sysProcessDefinitionVO,variableMap);
            processFormDTO.setApprover(approvers[0]);
        }else if (sysProcessDefinitionVO.getApprovalMode().equalsIgnoreCase(ProcessConstant.PROCESS_DEF_ASSIGNEE_TYPE_ROLE)){
            processFormDTO.setProcessDefinitionApproveRole(sysProcessDefinitionVO.getParticipantRole());

            String[] processRolesApprovers = updateProcessVariableCandidateUsers(sysProcessDefinitionVO,variableMap,processFormDTO);

            String allParticipantsStr = Arrays.stream(Optional.of(processRolesApprovers).orElse(new String[]{""})).collect(Collectors.joining(ProcessDefinitionParseUtil.SEPARATOR_APPROVER_LEVEL));
            processFormDTO.setProcessDefinitionApprover(allParticipantsStr);
        }

        processFormDTO.setProcessDefinitionKey(sysProcessDefinitionVO.getProcessDefinitionKey());

        JSONObject json = securityUser.currentUser(token);
        String userName = json.getString("userName");
        processFormDTO.setApplicant(userName);
        log.debug("【多级审批流程服务】，启动流程实例，workflowProcessDTO:{}", processFormDTO);

        variableMap.put(ProcessConstant.FORM_ID, processFormDTO.getFormId());
        variableMap.put(ProcessConstant.INITIATOR, processFormDTO.getApplicant());
        variableMap.put(ProcessConstant.APPLICANT, processFormDTO.getApplicant());

        //指定审批人方式
        variableMap.put(ProcessConstant.PROCESS_DEF_ASSIGNEE_TYPE, sysProcessDefinitionVO.getApprovalMode());

        //指定审批人列表
        variableMap.put(ProcessConstant.PROCESS_DEF_APPROVER, processFormDTO.getProcessDefinitionApprover());
        variableMap.put(ProcessConstant.PROCESS_DEF_APPROVER_ROLE, processFormDTO.getProcessDefinitionApproveRole());

        //多实例会签任务
        LinkedList<String> leaders = new LinkedList<>();
        leaders.add("minsc");
        leaders.add("zack");
        leaders.add("xim");
        leaders.add("robert");
        variableMap.put("leaders", leaders);

        //设置流程发起人
        identityService.setAuthenticatedUserId(processFormDTO.getApplicant());
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processFormDTO.getProcessDefinitionKey(), processFormDTO.getBusinessKey(), variableMap);

        log.info("【多级审批流程服务】，流程发起, processInstanceId:{}", processInstance.getProcessInstanceId());
        processFormDTO.setProcessInstanceId(processInstance.getProcessInstanceId());

        //流程记录
        SysProcessRecordDTO sysProcessRecordDto = new SysProcessRecordDTO();
        BeanUtils.copyProperties(processFormDTO,sysProcessRecordDto);
        sysProcessRecordDto.setApprover(processFormDTO.getApprover());
        sysProcessRecordDto.setApprovers(processFormDTO.getApprover());
        sysProcessRecordDto.setApproverRoles(processFormDTO.getProcessDefinitionApproveRole());
        sysProcessRecordDto.setApprovalStatus(ApprovalStatus.PENDING_APPROVAL.getName());
        sysProcessRecordDto.setApplyDatetime(new Timestamp(System.currentTimeMillis()));
        sysProcessRecordDto.setCreateTime(new Timestamp(System.currentTimeMillis()));
        log.info("【多级审批流程服务】，流程记录调用保存, sysProcessRecordDto:{}", sysProcessRecordDto);
        TorchResponse<SysProcessRecordVO> sysProcessRecordVOResp = sysProcessRecordService.saveOrUpdate(sysProcessRecordDto);

        return sysProcessRecordVOResp.getData().getData();
    }

    private String[] updateProcessVariableApprovers(SysProcessDefinitionVO sysProcessDefinitionVO,VariableMap variableMap){
        String[] approvers = ProcessDefinitionParseUtil.parseProcessDefinitionParticipants(sysProcessDefinitionVO.getParticipant());
        int multiApproverLevel = approvers.length;
        if(multiApproverLevel >= 1){
            //审批人校验
            int multiApproverRequired = Integer.parseInt(processDefinitionKey.substring(processDefinitionKey.length() -1));
            if(multiApproverRequired != multiApproverLevel){
                log.error("【多级审批流程服务】，启动流程实例，待审批人个数与流程需求不符，approvers :{}", approvers);
                throw  new ServiceException("多级审批流程服务, 待审批人个数与流程需求不符: " + approvers);
            }
        }else {
            log.error("【多级审批流程服务】，启动流程实例，待审批人个数异常，approvers :{}", approvers);
            throw  new ServiceException("多级审批流程服务, 待审批人个数不符: " + approvers);
        }

        variableMap.put(ProcessConstant.PROCESS_APPROVERS, approvers);
        variableMap.put(ProcessConstant.PROCESS_MULTI_APPROVER_LEVEL, multiApproverLevel);

        return approvers;
    }

    private String[] updateProcessVariableCandidateUsers(SysProcessDefinitionVO sysProcessDefinitionVO, VariableMap variableMap, ProcessFormDTO processFormDTO){
        String[] approverRoles = ProcessDefinitionParseUtil.parseProcessDefinitionParticipants(sysProcessDefinitionVO.getParticipantRole());
        int multiApproverLevel = approverRoles.length;
        if(multiApproverLevel >= 1){
            //审批人校验
            int multiApproverRequired = Integer.parseInt(processDefinitionKey.substring(processDefinitionKey.length() -1));
            if(multiApproverRequired != multiApproverLevel){
                log.error("【多级审批流程服务】，启动流程实例，待审批角色个数与流程需求不符，multiApproverLevel :{}", multiApproverLevel);
                throw  new ServiceException("多级审批流程服务, 待审批角色个数与流程需求不符, 待审批角色个数 :" + multiApproverLevel);
            }
        }else {
            log.error("【多级审批流程服务】，启动流程实例，待审批角色个数异常，multiApproverLevel :{}", multiApproverLevel);
            throw  new ServiceException("多级审批流程服务, 待审批角色个数不符: " + multiApproverLevel);
        }

        //根据角色获取人
        ArrayList<String>[] processRolesApproversList = new ArrayList[multiApproverLevel];
        String[] processRolesApprovers = new String[multiApproverLevel];
        int i = 0;
        for(String role : approverRoles){
            RoleDTO roleDTO = new RoleDTO();
            roleDTO.setDeleted("0");
            roleDTO.setRole(role);
            List<SysUser> sysUserList = sysUserService.findUserList(roleDTO);
            if(sysUserList.size() == 0){
                log.error("【多级审批流程服务】，启动流程实例，待审批角色用户异常，无可用用户，待审批角色:{}", role);
                throw  new ServiceException("多级审批流程服务, 无可用用户，待审批角色: " + role);
            }

            ArrayList<String> userIdList = new ArrayList<>(sysUserList.size());
            for(SysUser sysUser : sysUserList){
                //=====代码修改====
                userIdList.add(sysUser.getId());
//                userIdList.add(sysUser.getUserName());
            }
            processRolesApprovers[i] = String.join(ProcessDefinitionParseUtil.SEPARATOR_CANDIDATE_USERS,userIdList);
            processRolesApproversList[i++] = userIdList;
        }

        variableMap.put(ProcessConstant.PROCESS_ROLES, approverRoles);
        variableMap.put(ProcessConstant.PROCESS_ROLES_APPROVERS, processRolesApprovers);
        //variableMap.put(ProcessConstant.PROCESS_ROLES_APPROVERS, processRolesApproversList);
        variableMap.put(ProcessConstant.PROCESS_MULTI_APPROVER_LEVEL, multiApproverLevel);

        processFormDTO.setApprover(processRolesApprovers[0]);
        processFormDTO.setApproverRole(approverRoles[0]);

        return processRolesApprovers;
    }

    @Override
    @Transactional
    public SysProcessRecordVO approve(ProcessFormDTO processFormDTO, String token) {
        log.info("【多级审批流程服务】，formId:{}", processFormDTO.getFormId());

        JSONObject json = securityUser.currentUser(token);
        String currentUser = json.getString("userName");
        //=====新增代码======
        String currentUserId = SecurityContextHolder.getCurrentUserId();

        //确定是否有审批权限
        TorchResponse<SysProcessRecordVO> businessKeyRecordsResponse = sysProcessRecordService.findSysProcessRecord(processFormDTO.getFormId(),"");
        SysProcessRecordVO businessKeyRecordsResponseVO = businessKeyRecordsResponse.getData().getData();
        if(!processPrivilegeService.hasApprovalProcessPrivilege(currentUser,businessKeyRecordsResponseVO)){
            throw new ServiceException("当前用户没有审批权限");
        }

        if(!businessKeyRecordsResponseVO.getApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
            throw new ServiceException("当前表单无须审批，审批状态：" + businessKeyRecordsResponseVO.getApprovalStatus());
        }

        //分配任务给审批人
        if(!processPrivilegeService.assignApprovalProcessPrivilege(currentUser,businessKeyRecordsResponseVO)){
            throw new ServiceException("分配任务的审批用户失败");
        }

//        processFormDTO.setApprover(currentUser);
        //=========新增代码========
        processFormDTO.setApprover(currentUserId);
        //根据审批人和formId查询流程Id
        List<Task> tasks = taskService.createTaskQuery()
                .taskAssignee(currentUser)
                .initializeFormKeys()
                .list();
        if(tasks.size() == 0){
            throw new ServiceException("未查到分配的表单审批任务，待审批人：" + processFormDTO.getApprover()
                         + ", 表单：" + processFormDTO.getFormId());
        }

        Task approvalTask = null;
        for(Task task: tasks){
            String formKey = task.getFormKey();
            //Map<String, Object> variables = taskService.getVariables(task.getId());
            String formId = (String) taskService.getVariable(task.getId(), ProcessConstant.FORM_ID);
            if(StringUtils.isNotEmpty(formId) && formId.equals(processFormDTO.getFormId())){
                approvalTask = task;
                break;
            }
        }

        if(null == approvalTask){
            throw new ServiceException("未找到审批表单的运行任务流程, 表单：" + processFormDTO.getFormId());
        }

        String processInstanceId = approvalTask.getProcessInstanceId();
        processFormDTO.setProcessInstanceId(processInstanceId);

        log.info("【多级审批流程服务】，查找到的流程实例：processInstanceId:{} , assignee:{}", approvalTask.getProcessInstanceId(), approvalTask.getAssignee());

        String applicant = (String )taskService.getVariable(approvalTask.getId(), ProcessConstant.APPLICANT);
        String processDefinitionApprover = (String )taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_DEF_APPROVER);
        String processDefinitionApproverRoles = (String )taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_DEF_APPROVER_ROLE);
        String[] processDefinitionApprovers = (String[]) taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_APPROVERS);
        String[] processRoles = (String[]) taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_ROLES);
        String processRole = (String) taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_ROLE);
        String[] processRolesApprovers = (String[]) taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_ROLES_APPROVERS);
        int processApproverLevel = (Integer) taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_APPROVER_LEVEL);
        int processMultiApproverLevel = (Integer) taskService.getVariable(approvalTask.getId(), ProcessConstant.PROCESS_MULTI_APPROVER_LEVEL);

        processFormDTO.setApplicant(applicant);
        processFormDTO.setProcessDefinitionApprover(processDefinitionApprover);
        processFormDTO.setProcessDefinitionApproveRole(processDefinitionApproverRoles);
        processFormDTO.setApproverRole(processRole);

        int currentTaskLevelIndex = Integer.valueOf(approvalTask.getTaskDefinitionKey().substring(ProcessConstant.APPROVAL_TASK_ID.length()));

        VariableMap variableMap = Variables.createVariables();
        variableMap.put(ProcessConstant.APPROVER + currentTaskLevelIndex, processFormDTO.getApprover());
        switch (ApprovalAct.of(processFormDTO.getApprovalAct())) {
            case AGREE:
            case REJECT:
            case REAPPLY:
                variableMap.put(ProcessConstant.APPROVAL_ACT + currentTaskLevelIndex, processFormDTO.getApprovalAct());   //网关条件参数
                variableMap.put(ProcessConstant.APPROVAL_ACT, processFormDTO.getApprovalAct());           //流程业务变量
                variableMap.put(ProcessConstant.APPROVAL_OPINION, processFormDTO.getApprovalOpinion());   //流程业务变量
                break;
            default:
                variableMap.put(ProcessConstant.APPROVAL_ACT + currentTaskLevelIndex, "EXCEPTION");
                variableMap.put(ProcessConstant.APPROVAL_OPINION, "EXCEPTION");
        }

        taskService.complete(approvalTask.getId(), variableMap);

        log.info("【多级审批流程服务】，完成formId:{}", processFormDTO.getFormId());

        //更新流程记录
        SysProcessRecordDTO sysProcessRecordDto = new SysProcessRecordDTO();
        BeanUtils.copyProperties(processFormDTO,sysProcessRecordDto);

        TorchResponse<SysProcessRecordVO> recordsResponse = sysProcessRecordService.findSysProcessRecord(sysProcessRecordDto.getFormId(),sysProcessRecordDto.getProcessInstanceId());
        SysProcessRecordVO sysProcessRecordVO = recordsResponse.getData().getData();
        log.info("【多级审批流程服务】，更新流程状态，找到记录：sysProcessRecordVO:{}",sysProcessRecordVO);
        sysProcessRecordDto.setId(sysProcessRecordVO.getId());

        sysProcessRecordDto.setApproveDatetime(new Timestamp(System.currentTimeMillis()));
        sysProcessRecordDto.setApprovalStatus(ApprovalStatus.byApprovalAct(processFormDTO.getApprovalAct()).getName());
        sysProcessRecordDto.setApprovalAct(ApprovalAct.of(processFormDTO.getApprovalAct()).getName());

        ApprovalRecordUtil.updateApprovalRecord(sysProcessRecordDto,sysProcessRecordVO);
        updateFinalPendingStatus(sysProcessRecordDto,processFormDTO,sysProcessRecordVO);

        TorchResponse<SysProcessRecordVO> sysProcessRecordVOResp = sysProcessRecordService.saveOrUpdate(sysProcessRecordDto);

        log.info("【多级审批流程服务】，表单审批完成,sysProcessRecordVOResp:{}",sysProcessRecordVOResp.getData().getData());

        return sysProcessRecordVOResp.getData().getData();
    }

    public void updateFinalPendingStatus(SysProcessRecordDTO sysProcessRecordDto,ProcessFormDTO processFormDTO,SysProcessRecordVO sysProcessRecordVO){
        //审批服务返回，需要立即回显状态，靠工作流检测没有实时结果
        //判定最后状态,最后一个人审批结束即结束

        // 获取流程实例ID
        String processInstanceId = sysProcessRecordDto.getProcessInstanceId();

        // 再次查询流程实例来获取当前活动
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        Task applyTask = taskService.createTaskQuery()
                .taskAssignee(sysProcessRecordDto.getApplicant())
                .processVariableValueEquals(ProcessConstant.FORM_ID,processFormDTO.getFormId())
                .singleResult();

        //获取下一个审批
        updateProcessRecordNextStatus(sysProcessRecordDto,processFormDTO,sysProcessRecordVO);

    }

    private void updateProcessRecordNextStatus(SysProcessRecordDTO sysProcessRecordDto,ProcessFormDTO processFormDTO,SysProcessRecordVO sysProcessRecordVO){
        String processDefinitionApprover = processFormDTO.getProcessDefinitionApprover();
        String processDefinitionApproverRole = processFormDTO.getProcessDefinitionApproveRole();
        String[] processDefinitionApprovers = processDefinitionApprover.split(ProcessDefinitionParseUtil.SEPARATOR_APPROVER_LEVEL);
        String[] processDefinitionApproverRoles = processDefinitionApproverRole.split(ProcessDefinitionParseUtil.SEPARATOR_APPROVER_LEVEL);
        String currentApprover = processFormDTO.getApprover();
        String currentApproverRole = processFormDTO.getApproverRole();

        Task assignedTask = null;
        String nextApprover = "";
        String nextApproverRole = "";

        //查询是否有分配审批人的任务
        if(StringUtils.isNotEmpty(processDefinitionApproverRole)) {
            //查询候选角色的任务
            for (String approverRole : processDefinitionApproverRoles) {
                assignedTask = taskService.createTaskQuery()
                        .processVariableValueEquals(ProcessConstant.FORM_ID, processFormDTO.getFormId())
                        .processVariableValueLike(ProcessConstant.PROCESS_ROLE, approverRole)
                        .singleResult();

                if(assignedTask != null){
                    int approverLevel = (int)taskService.getVariable(assignedTask.getId(), ProcessConstant.PROCESS_APPROVER_LEVEL);

                    nextApprover = processDefinitionApprovers[approverLevel-1];
                    nextApproverRole = approverRole;
                    break;
                }
            }
        } else if(StringUtils.isNotEmpty(processDefinitionApprover)) {
            for (String approver : processDefinitionApprovers) {
                assignedTask = taskService.createTaskQuery()
                        .taskAssignee(approver)
                        .processVariableValueEquals(ProcessConstant.FORM_ID, processFormDTO.getFormId())
                        .singleResult();

                if(assignedTask != null){
                    nextApprover = assignedTask.getAssignee();
                    break;
                }
            }
        }

        if(assignedTask != null){
            if(assignedTask.getName().contains(ProcessConstant.APPROVAL_TASK_NAME)) {
                //如果还有下一个审批人，审批状态待定
                sysProcessRecordDto.setApprovalStatus(ApprovalStatus.PENDING_APPROVAL.getName());
            }else if(assignedTask.getName().contains(ProcessConstant.APPLY_TASK_NAME)){
                //如果是申请状态任务，待申请
                if(ApprovalAct.REAPPLY.getCode().equals(processFormDTO.getApprovalAct())){
                    sysProcessRecordDto.setApprovalStatus(ApprovalStatus.PENDING_REAPPLY.getName());
                }else {
                    sysProcessRecordDto.setApprovalStatus(ApprovalStatus.PENDING_APPLY.getName());
                }
            }

            sysProcessRecordDto.setApprovalAct("");
            sysProcessRecordDto.setApprovalOpinion("");
            sysProcessRecordDto.setApproveDatetime(null);
            sysProcessRecordDto.setApprover(nextApprover);
            sysProcessRecordDto.setApproverRole(nextApproverRole);
        }

        //更新记录中候选者
        String[] recordCandidateUsers= sysProcessRecordVO.getApprovers().split(ProcessDefinitionParseUtil.SEPARATOR_APPROVER_LEVEL);
        recordCandidateUsers[recordCandidateUsers.length -1] = currentApprover;
        String updatedRecordCandidateUsers = Arrays.stream(Optional.of(recordCandidateUsers).orElse(new String[]{""})).collect(Collectors.joining(ProcessDefinitionParseUtil.SEPARATOR_APPROVER_LEVEL));
        sysProcessRecordDto.setApprovers(updatedRecordCandidateUsers + ";" + nextApprover);

        log.info("【多级审批流程服务】，更新流程记录状态，formId:{}, approvalStatus:{}, nextApprover:{}, approvers:{}",processFormDTO.getFormId(),sysProcessRecordDto.getApprovalStatus(),nextApprover,sysProcessRecordDto.getApprovers());
    }

    @Override
    public String getDiagram(String formId) {
        try {
            String diagramXml = multiApproverBpmnViewer.getProcessRealtimeDiagram(formId);
            return diagramXml;

        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
    }

    @Override
    public ProcessHistory getHistory(String formId) {
        TorchResponse<SysProcessRecordVO> recordsResponse = sysProcessRecordService.findSysProcessRecord(formId, "");
        SysProcessRecordVO sysProcessRecordVO = recordsResponse.getData().getData();
        log.info("【多级审批流程服务】，获取流程历史，找到流程记录：sysProcessRecordVO:{}", sysProcessRecordVO);

        ProcessHistory processHistory = new ProcessHistory();
        int approvalsCount = 0;
        String processInstanceId = sysProcessRecordVO.getProcessInstanceId();
        List<HistoricTaskInstance> historicTaskInstanceList = historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByHistoricTaskInstanceEndTime()
                .asc()
                .list();
        HistoricTaskUtil.sortTaskList(historicTaskInstanceList);

        for (int i = 0; i < historicTaskInstanceList.size(); i++) {
            HistoricTaskInstance historicTaskInstance = historicTaskInstanceList.get(i);
            String historicReason = historicTaskInstance.getDeleteReason();
            String historicDefKey = historicTaskInstance.getTaskDefinitionKey();
            String historicAssignee = historicTaskInstance.getAssignee();
            Date taskEndDateTime = historicTaskInstance.getEndTime();
            log.info("【多级审批流程服务】，历史任务记录项：{}-{}-{}-{}", historicDefKey, historicReason, historicAssignee, taskEndDateTime);

            ProcessHistoryItem processHistoryItem = new ProcessHistoryItem();
            processHistoryItem.setTaskName(historicTaskInstance.getName());

            if(StringUtils.isNotEmpty(historicTaskInstance.getAssignee())) {
                processHistoryItem.setApprover(historicTaskInstance.getAssignee());
            }else{
                //如果当前任务还没有被操作，设置为待审批人
                processHistoryItem.setApprover(sysProcessRecordVO.getApprover());
            }

            if(taskEndDateTime != null) {
                processHistoryItem.setApprovalDatetime(new Timestamp(taskEndDateTime.getTime()));
            }

            if(historicTaskInstance.getName().contains(ProcessConstant.APPROVAL_TASK_NAME)) {
                approvalsCount++;

                List<HistoricVariableInstance> historicVariableInstanceList = historyService.createHistoricVariableInstanceQuery()
                        .processInstanceId(processInstanceId)
                        .executionIdIn(historicTaskInstance.getExecutionId())
                        .variableNameLike("%" + ProcessConstant.APPROVALS_VARIABLE_PREFIX + "%" + approvalsCount + "%")
                        .orderByVariableName()
                        .desc().list();
                for(HistoricVariableInstance historicVariableInstance : historicVariableInstanceList) {
                    if(historicVariableInstance.getName().contains(ProcessConstant.APPROVALS_ACT)){
                        processHistoryItem.setApprovalAct(ApprovalAct.of((String) historicVariableInstance.getValue()).getName());
                    }else if(historicVariableInstance.getName().contains(ProcessConstant.APPROVALS_OPINION)){
                        processHistoryItem.setApprovalOpinion((String) historicVariableInstance.getValue());
                    }
                }
            }

            processHistory.addItem(processHistoryItem);
        }

        log.info("【多级审批流程服务】，流程历史：{}",processHistory.getJsonString());

        return processHistory;
    }

}
