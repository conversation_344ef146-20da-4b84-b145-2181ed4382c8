package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 能力资产VO实体类
* <AUTHOR>
* @date 2025-08-04
**/
@Data
@ApiModel("能力资产DTO实体类")
public class CapabilityAssetImportVO implements Serializable {

	private static final long serialVersionUID = 1L;



    /**
	 * 能力类型
     **/
    @ApiModelProperty("能力类型")
    @Excel(name = "能力类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String capabilityType;

    /**
	 * 适用试验类型
     **/
    @ApiModelProperty("适用试验类型")
    @Excel(name = "适用试验类型",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String applicableTestType;

    /**
     * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String productName;
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;


    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;
    /**
     * 适用设备类型编码
     **/
    @ApiModelProperty("适用设备类型编码")
    @Excel(name = "适用设备类型编码",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String applicableEquipmentType;


    /**
	 * 任务编号
     **/
    @ApiModelProperty("任务编号")
    @Excel(name = "任务编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String taskNumber;

    /**
	 * 程序编号
     **/
    @ApiModelProperty("程序编号")
    @Excel(name = "程序编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String programNumber;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;

    /**
	 * 每板老化能力
     **/
    @ApiModelProperty("每板老化能力")
    @Excel(name = "每板老化能力",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long boardAgingCapability;

    /**
	 * 老化板总能力
     **/
    @ApiModelProperty("老化板总能力")
    @Excel(name = "老化板总能力",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long totalBoardAgingCapability;
    /**
     * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;

}