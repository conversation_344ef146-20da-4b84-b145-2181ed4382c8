package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.Abnormalfeedback;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO;
import com.huatek.frame.modules.business.mapper.AbnormalfeedbackMapper;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import com.huatek.frame.modules.business.mq.producer.MessageProducer;
import com.huatek.frame.modules.business.service.AbnormalfeedbackService;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.MessageManagementService;
import com.huatek.frame.modules.business.service.dto.AbnormalRequestDTO;
import com.huatek.frame.modules.business.service.dto.AbnormalfeedbackDTO;
import com.huatek.frame.modules.business.service.dto.AbnormalfeedbackSolveDTO;
import com.huatek.frame.modules.business.service.dto.MessageManagementDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.constant.UserInfoConstants;
import com.huatek.frame.modules.system.domain.UserRole;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Date;
import java.sql.Timestamp;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 异常反馈 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "abnormalfeedback")
//@RefreshScope
@Slf4j
public class AbnormalfeedbackServiceImpl implements AbnormalfeedbackService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private AbnormalfeedbackMapper abnormalfeedbackMapper;

	@Autowired
    private CustomerInformationManagementMapper customerInformationManagementMapper;

    @DubboReference
    private RoleService roleService;


    @Autowired
    private MessageManagementService messageManagementService;

    @Autowired
    protected Validator validator;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private MessageProducer messageProducer;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

    private DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");


	public AbnormalfeedbackServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<AbnormalfeedbackVO>> findAbnormalfeedbackPage(AbnormalfeedbackDTO dto) {
        if(!HuatekTools.isEmpty(dto.getToNotify())) {
           String abnormalfeedback = dto.getToNotify().replaceAll(",", "|");
           abnormalfeedback = MATCH_MULTIPLE_VALUE_REGEXP.replace("#", abnormalfeedback);
           dto.setToNotify(abnormalfeedback);
        }
		PageHelper.startPage(dto.getPage(), dto.getLimit());
        String currentUserId = SecurityContextHolder.getCurrentUserId();
        List<String> roles = roleService.selectByUserId(currentUserId).stream().map(UserRole::getRoleId).collect(Collectors.toList());
        //查看当前登录角色是否是异常反馈管理员
        boolean isExceptionFeedbackManager = roles.stream().anyMatch(UserInfoConstants.EXCEPTION_FEEDBACK_MANAGEMENT_ROLE_ID::equals);
        if (!isExceptionFeedbackManager){
            dto.setCurrentUserId(currentUserId);
        }
        dto.setSubmitter(SecurityContextHolder.getCurrentUserId());
        //查看当前登录角色是否是外部人员
        boolean isExternalRole = roles.stream().anyMatch(UserInfoConstants.OUT_ROLE_ID::equals);
        if (isExternalRole){
            //外部角色能看到所有启用状态的异常信息
            dto.setWhetherToEnable(DicConstant.Abnormalfeedback.ABNORMALFEEDBACK_ENABLE);
            dto.setCurrentUserId("");
        }
        Page<AbnormalfeedbackVO> abnormalfeedbacks = abnormalfeedbackMapper.selectAbnormalfeedbackPage(dto);



		TorchResponse<List<AbnormalfeedbackVO>> response = new TorchResponse<List<AbnormalfeedbackVO>>();
		response.getData().setData(abnormalfeedbacks);
		response.setStatus(200);
		response.getData().setCount(abnormalfeedbacks.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse<AbnormalfeedbackVO> saveOrUpdate(AbnormalfeedbackDTO abnormalfeedbackDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        String currentUserId = SecurityContextHolder.getCurrentUserId();
        if (HuatekTools.isEmpty(abnormalfeedbackDto.getCodexTorchDeleted())) {
            abnormalfeedbackDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = abnormalfeedbackDto.getId();
		Abnormalfeedback entity = new Abnormalfeedback();
        BeanUtils.copyProperties(abnormalfeedbackDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        String abnormalNumber = "";
		if (HuatekTools.isEmpty(id)) {
            TorchResponse response = codeManagementService.getOrderNumber("YC");
            entity.setAbnormalNumber(response.getData().getData().toString());
            abnormalNumber = response.getData().getData().toString();
            entity.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
            entity.setAbnormalDate(new Date(System.currentTimeMillis()));
            entity.setStatus(DicConstant.Abnormalfeedback.ABNORMALFEEDBACK_STATUS_IN_PROGRESS);
            abnormalfeedbackMapper.insert(entity);

		} else {
			abnormalfeedbackMapper.updateById(entity);
		}

        //消息通知
        List<String> userIDs = StrUtil.split(abnormalfeedbackDto.getToNotify() , ',');
        for (String  userId : userIDs){
            MessageManagementDTO messageManagementDTO = MessageManagementDTO.builder()
                    .messageContent("您收到一条异常反馈通知，异常编号为" + abnormalNumber)
                    .userId(userId)
                    .senderId(currentUserId)
                    .sendTime(new Timestamp(System.currentTimeMillis()))
                    .codexTorchCreatorId(SecurityContextHolder.getCurrentUserId())
                    .codexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId())
                    .codexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()))
                    .build();
            messageProducer.send(messageManagementDTO);
        }

		TorchResponse response = new TorchResponse();
        AbnormalfeedbackVO vo = new AbnormalfeedbackVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<AbnormalfeedbackVO> findAbnormalfeedback(String id) {
		AbnormalfeedbackVO vo = new AbnormalfeedbackVO();
		if (!HuatekTools.isEmpty(id)) {
			Abnormalfeedback entity = abnormalfeedbackMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<AbnormalfeedbackVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		abnormalfeedbackMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
  		    selectOptionsFuncMap.put("settlementUnit",abnormalfeedbackMapper::selectOptionsBySettlementUnit);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("toNotify",abnormalfeedbackMapper::selectOptionsByToNotify);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "abnormalfeedback", convertorFields = "abnormalType,status,solutionMeasures,whetherToEnable")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<AbnormalfeedbackVO> selectAbnormalfeedbackList(AbnormalfeedbackDTO dto) {
        return abnormalfeedbackMapper.selectAbnormalfeedbackList(dto);
    }

    /**
     * 导入异常反馈数据
     *
     * @param abnormalfeedbackList 异常反馈数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "abnormalfeedback", convertorFields = "abnormalType,status,solutionMeasures,whetherToEnable")
    public TorchResponse importAbnormalfeedback(List<AbnormalfeedbackVO> abnormalfeedbackList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(abnormalfeedbackList) || abnormalfeedbackList.size() == 0) {
            throw new ServiceException("导入异常反馈数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AbnormalfeedbackVO vo : abnormalfeedbackList) {
            try {
                Abnormalfeedback abnormalfeedback = new Abnormalfeedback();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, abnormalfeedback);
                QueryWrapper<Abnormalfeedback> wrapper = new QueryWrapper();
                Abnormalfeedback oldAbnormalfeedback = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = AbnormalfeedbackVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<Abnormalfeedback> oldAbnormalfeedbackList = abnormalfeedbackMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldAbnormalfeedbackList) && oldAbnormalfeedbackList.size() > 1) {
                        abnormalfeedbackMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldAbnormalfeedbackList) && oldAbnormalfeedbackList.size() == 1) {
                        oldAbnormalfeedback = oldAbnormalfeedbackList.get(0);
                    }
                }
                if (StringUtils.isNull(oldAbnormalfeedback)) {
                    BeanValidators.validateWithException(validator, vo);
                    abnormalfeedbackMapper.insert(abnormalfeedback);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、异常编号 " + vo.getAbnormalNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldAbnormalfeedback, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    abnormalfeedbackMapper.updateById(oldAbnormalfeedback);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、异常编号 " + vo.getAbnormalNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、异常编号 " + vo.getAbnormalNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、异常编号 " + vo.getAbnormalNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(AbnormalfeedbackVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getAbnormalNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>异常编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getAbnormalType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>异常类型不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getProductionWorkOrder())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>生产工单不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectAbnormalfeedbackListByIds(List<String> ids) {
        List<AbnormalfeedbackVO> abnormalfeedbackList = abnormalfeedbackMapper.selectAbnormalfeedbackListByIds(ids);

		TorchResponse<List<AbnormalfeedbackVO>> response = new TorchResponse<List<AbnormalfeedbackVO>>();
		response.getData().setData(abnormalfeedbackList);
		response.setStatus(200);
		response.getData().setCount((long)abnormalfeedbackList.size());
		return response;
    }

    @Override
    public TorchResponse batchSolveAbnormal(AbnormalfeedbackSolveDTO requestParam) {
        List<Abnormalfeedback> abnormalfeedbacks = abnormalfeedbackMapper.selectBatchIds(requestParam.getIds());
        Date currentDate = new Date(System.currentTimeMillis());
        abnormalfeedbacks.forEach(item -> {
            LambdaUpdateWrapper<Abnormalfeedback> updateWrapper = Wrappers.lambdaUpdate(Abnormalfeedback.class)
                    .eq(Abnormalfeedback::getId, item.getId())
                    .set(Abnormalfeedback::getComment, requestParam.getComment())
                    .set(Abnormalfeedback::getSolutionMeasures, requestParam.getSolutionMeasures())
                    .set(Abnormalfeedback::getAttachment, requestParam.getAttachment())
                    .set(Abnormalfeedback::getResolutionDate, currentDate)
                    .set(Abnormalfeedback::getStatus, DicConstant.Abnormalfeedback.ABNORMALFEEDBACK_STATUS_FINISH);
            abnormalfeedbackMapper.update(null, updateWrapper);
        });

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return  response;

    }

    @Override
    public TorchResponse getAbnormalDetailsBySourceId(String sourceId) {
        LambdaQueryWrapper<Abnormalfeedback> queryWrapper = Wrappers.lambdaQuery(Abnormalfeedback.class)
                .eq(Abnormalfeedback::getSourceId, sourceId);
        List<AbnormalfeedbackVO> abnormalfeedbackVOS = abnormalfeedbackMapper.getAbnormalDetailsBySourceId(sourceId);
        TorchResponse response = new TorchResponse();
        if (abnormalfeedbackVOS != null){
            response.getData().setData(abnormalfeedbackVOS);
        }
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;



    }

    @Override
    public TorchResponse<Boolean> checkIsOutRole(String id) {
        List<String> roles = roleService.selectByUserId(id).stream().map(UserRole::getRoleId).collect(Collectors.toList());
        //查看当前登录角色是否是外部角色
        boolean isExternalRole = roles.stream().anyMatch(UserInfoConstants.OUT_ROLE_ID::equals);
        TorchResponse<Boolean> response = new TorchResponse<>();
        response.getData().setData(isExternalRole);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;

    }

    @Override
    public List<AbnormalfeedbackVO> getAbnormalsOfWorkOrderNumberORProcessId(AbnormalRequestDTO requestParam) {
        return abnormalfeedbackMapper.getAbnormalsOfWorkOrderNumberORProcessId(requestParam);
    }


}
