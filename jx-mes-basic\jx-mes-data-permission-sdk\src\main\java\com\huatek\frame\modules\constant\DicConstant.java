package com.huatek.frame.modules.constant;

public class DicConstant  {

    public class CommonDic{
        public static final String DIC_YES = "1";//是
        public static final String DIC_NO = "0";//否
        public static final String DEFAULT_TRUE="true";
        public static final String DEFAULT_FALSE="false";
        public static final String DEFAULT_ZERO="0";
        public static final String DEFAULT_ONE="1";

        /**
         * 未删除
         */
        public static final String UN_DELETED = "0";

        /**
         * 已删除
         */
        public static final String DELETED = "1";

        public static final String DRAFT="0";//草稿

        public static final String WAITAPPROVE="1"; //待审批
        public static final String APPROVED="2";//审批通过
        public static final String REJECT="3";//驳回

    }

    /**
     * 消息管理字典
     */
    public class MessageManagement{
        /**
         * 消息状态
         */

        /**
         * 未读
         */
        public static final String MESSAGE_MANAGEMENT_STATUS_UN_READ = "0";

        /**
         * 已读
         */
        public static final String MESSAGE_MANAGEMENT_STATUS_HAS_READ = "1";
    }

    /**
     * 异常反馈字典常量字段
     */
    public class Abnormalfeedback{
        /**
         * 进行中
         */
        public static final String ABNORMALFEEDBACK_STATUS_IN_PROGRESS = "0";

        /**
         * 已处理
         */
        public static final String ABNORMALFEEDBACK_STATUS_FINISH = "1";

        /**
         * 启用
         */
        public static final String ABNORMALFEEDBACK_ENABLE = "0";

        /**
         * 禁用
         */
        public static final String ABNORMALFEEDBACK_DISABLE = "1";


    }

    public class CustomerInformation{
        /**
         * 上下游类型——上游
         */
        public static final String UP_DOWN_STREAM_TYPE_UP = "0";
        /**
         * 上下游类型——下游
         */
        public static final String UP_DOWN_STREAM_TYPE_DOWN = "1";
    }

    public class ProductionOrder{

        /**
         * 订单类型
         */

        public static final String WORK_ORDER_TYPE_RELIABILITY = "K";//可靠性类型工单：DPA、专项分析、失效分析、鉴定试验、质量一致性、其它试验类型
        public static final String WORK_ORDER_TYPE_SCREENING = "S";//测筛类型工单：一筛、二筛、环境试验、复验类型、微波鉴定试验

        public static final String WORK_ORDER_TYPE_SAW = "SAW"; //监制验收



        /**
         * 订单状态
         */
        public static final String WORK_ORDER_STATUS_DRAFT="0";//待提交
        public static final String WORK_ORDER_STATUS_WAITAPPROVE="1";//待审批
        public static final String WORK_ORDER_STATUS_APPROVED_SCHEDUL="2";//审批通过(待排产）
        public static final String WORK_ORDER_STATUS_REJECT="3";//驳回
        public static final String WORK_ORDER_STATUS_PROGRESS="4";//进行中
        public static final String WORK_ORDER_STATUS_PAUSE="5";//暂停
        public static final String WORK_ORDER_STATUS_CANCEL="6";//取消
        public static final String WORK_ORDER_STATUS_COMPLETE="7";//完成
        public static final String WORK_ORDER_STATUS_OUTSOURCED="8";//已外协
        public static final String WORK_ORDER_STATUS_PREPARATION="9";//草稿
        public static final String WORK_ORDER_STATUS_APPROVED="10";//审批通过
        /**
         * 生产阶段
         */
        public static final String PRODUCTION_STAGE_NOSTARTED="0";//未开始
        public static final String PRODUCTION_STAGE_PRODUCTION="1";//生产中
        public static final String PRODUCTION_STAGE_INSTORE="2";//入库
        public static final String PRODUCTION_STAGE_OUTSTORE="3";//出库
        /**
         * 试验类型
         */

        public static final String TEST_TYPE_ONE="14";//一筛
        public static final String TEST_TYPE_TWO="13";//二筛
        public static final String TEST_TYPE_ENVIRONMENT="12";//环境
        public static final String TEST_TYPE_REINSPECTION="11";//复验
        public static final String TEST_TYPE_DPA="10";//DPA
        public static final String TEST_TYPE_SPECIAL_ANALYSIS="9";//专项分析
        public static final String TEST_TYPE_FAILURE_ANALYSIS="8";//失效分析
        public static final String TEST_TYPE_QUALIFICATION_TEST="7";//鉴定试验
        public static final String TEST_TYPE_MICROWAVE_QUALIFICATION_TEST="6";//微波鉴定试验
        public static final String TEST_TYPE_QUALITY_CONSISTENCY="5";//质量一致性
        public static final String TEST_TYPE_OTHER_TEST="4";//其他试验
        public static final String TEST_TYPE_EXECUTIVE_PRODUCER="3";//监制
        public static final String TEST_TYPE_CHECK_ACCEPT="2";//验收
        public static final String TEST_TYPE_CERTIFICATION="1";//出证
        /**
         * 试验方式
         */
        public static final String TEST_METHODLOGY_SELF="0";//自产
        public static final String TEST_METHODLOGY_OUTSORCEING="1";//外协
        public static final String TEST_METHODLOGY_NOT_APPLICABLE="2";//不适用
        public static final String TEST_METHODLOGY_CANNOT_SCREENED="3";//不可筛
        /**
         * 复制类型
         */
        public static final String COPY_TYPE_SAME="1";//同级复制
        public static final String COPY_TYPE_CHILD="2";//子级复制

        //==========监制验收工单==================

        /**
         * 验收类型-厂家代验
         */
        public static final String SUPERVISOR_ACCEPTANCE_TYPE_FACTORY_ACCEPT = "0";

        /**
         * 验收类型-下厂验收
         */
        public static final String SUPERVISOR_ACCEPTANCE_TYPE_ONSITE_ACCEPT  = "1";

        /**
         * 状态-未完成
         */
        public static final String SUPERVISOR_ACCEPTANCE_STATUS_UNFINISHED = "0";

        /**
         * 状态-完成
         */
        public static final String SUPERVISOR_ACCEPTANCE_STATUS_FINISHED = "1";

        /**
         * 状态-已入库
         */
        public static final String SUPERVISOR_ACCEPTANCE_STATUS_STORED = "2";

        /**
         * 状态-已出库
         */
        public static final String SUPERVISOR_ACCEPTANCE_STATUS_DELIVERED = "3";




        //==============外协=================

        /**
         * 外协状态-草稿
         */
        public static final String OUTSOURCING_STATUS_DRAFT = "0";

        /**
         * 外协状态-待审批
         */
        public static final String OUTSOURCING_STATUS_PENDING_APPROVAL = "1";

        /**
         * 外协状态-审批通过
         */
        public static final String OUTSOURCING_STATUS_APPROVALED = "2";

        /**
         * 外协状态-审批驳回
         */
        public static final String OUTSOURCING_STATUS_REJECTED = "3";

        /**
         * 外协状态-已验收
         */
        public static final String OUTSOURCING_STATUS_ACCEPTED = "4";

        //==========外协类型================
        /**
         * 整单外协
         */
        public static final String OUTSOURCING_TYPE_ENTIRE = "0";

        /**
         * 工序外协
         */
        public static final String OUTSOURCING_TYPE_PROCESS = "1";

       
        
        //生产任务状态
        public static final String PRODUCTION_TASK_STATUS_WEIKAISHI  = "0";
        public static final String PRODUCTION_TASK_STATUS_JINXINGZHONG  = "1";
        public static final String PRODUCTION_TASK_STATUS_DAIAPPROVE  = "2";
        public static final String PRODUCTION_TASK_STATUS_BOHUI  = "3";
        public static final String PRODUCTION_TASK_STATUS_ZANTING  = "4";
        public static final String PRODUCTION_TASK_STATUS_QUXIAO  = "5";
        public static final String PRODUCTION_TASK_STATUS_WANCHENG = "6";
        public static final String PRODUCTION_TASK_STATUS_YIWAIXIE = "7";


        //排产计划状态
        public static final String SCHEDULESTATE_STATUS_YIXIAFA  = "1";//已下发
        public static final String SCHEDULESTATE_STATUS_JINXINGZHONG  = "2";//进行中
        public static final String SCHEDULESTATE_STATUS_WANCHENG = "3";//完成
        public static final String SCHEDULESTATE_STATUS_WAIXIEZANTING = "7";//外协暂停
        public static final String SCHEDULESTATE_STATUS_YIWAIXIE = "8";//已外协




        //生产任务暂停原因
        public static final String PRODUCTION_PAUSE_REASON_ZHENGCHANG = "0";
        public static final String PRODUCTION_PAUSE_REASON_YICHANG = "1";
        public static final String PRODUCTION_PAUSE_REASON_WAIXIE = "2";

        
        /**
         * 操作类型常量
         */
        public static final String OPERATION_TYPE_START = "0";      // 开始
        public static final String OPERATION_TYPE_PAUSE = "1";      // 暂停
        public static final String OPERATION_TYPE_RESUME = "2";    // 恢复
        public static final String OPERATION_TYPE_CANCEL = "3";    // 取消
        public static final String OPERATION_TYPE_SUBMIT = "4";      // 提交
        public static final String OPERATION_TYPE_BOHUI = "5";    // 审批驳回
        public static final String OPERATION_TYPE_WANCHENG = "6";    // 完成
        public static final String OPERATION_TYPE_WAIXIE = "7";    // 已外协


        public static final String NON_SCREENED = "non_screened";//不能测试原因

        public static final String NON_POWER_AGING = "non_power_aging";//不能老化原因




    }

    public class OutSourcingStatus{
        /**
         * 草稿
         */
        public static final String OUTSOURCING_DRAFT = "0";

        /**
         * 待审批
         */
        public static final String OUTSOURCING_WAITAPPROVE = "1";

        /**
         * 审批通过
         */
        public static final String OUTSOURCING_APPROVED="2";

        /**
         * 驳回
         */
        public static final String OUTSOURCING_REJECT="3";

        /**
         * 已验收
         */
        public static final String OUTSOURCING_ACCEPTED="4";
    }
    /**
     * 销售管理
     */
    public class SalesOrder{
        //==============封裝訂單===================
        /**
         * 进行中
         */
        public static final String PACKAGE_ORDER_STATUS_IN_PROGRESS = "0";

        /**
         * 已完成
         */
        public static final String PACKAGE_ORDER_STATUS_FINISHED = "1";



        //================产品列表=======================
        /**
         * 未下发
         */
        public static final String PRODUCT_LIST_STATUS_PENDING = "0";

        /**
         * 已下发
         */
        public static final String PRODUCT_LIST_STATUS_COMPLETED = "1";

        /**
         * 已退回
         */
        public static final String PRODUCT_LIST_STATUS_REJECTED = "2";

        /**
         * 不可筛
         */
        public static final String PRODUCT_LIST_STATUS_LOCKED = "3";

        /**
         * 监制验收状态完成
         */
        public static final String PRODUCT_LIST_STATUS_FINISHED = "4";

        /**
         * 监制验收状态未完成
         */
        public static final String PRODUCT_LIST_STATUS_UNFINISHED = "5";

        //========测评订单==========

        /**
         * 监制验收产品状态-未完成
         */
        public static final String SAW_ORDER_PRODUCT_LIST_STATUS_UNFINISHED = "0";

        /**
         * 监制验收产品状态-完成
         */
        public static final String SAW_ORDER_PRODUCT_LIST_STATUS_FINISHED = "1";

        /**
         * 测评订单生产阶段-未开始
         */
        public static final String EVALUATION_ORDER_STATUS_NOT_STARTED = "0";

        /**
         * 测评订单生产阶段-生产中
         */
        public static final String EVALUATION_ORDER_STATUS_IN_PRODUCTION = "1";

        /**
         * 测评订单生产阶段-入库
         */
        public static final String EVALUATION_ORDER_STATUS_IN_STOCK = "2";

        /**
         * 测评订单生产阶段-出库
         */
        public static final String EVALUATION_ORDER_STATUS_OUT_STOCK = "3";

        /**
         * 测评订单状态-草稿
         */
        public static final String EVALUATION_ORDER_STATUS_DRAFT = "0";

        /**
         * 测评订单状态-已确认
         */
        public static final String EVALUATION_ORDER_STATUS_CONFIRMED = "1";

        /**
         * 订单类型-测筛
         */
        public static final String EVALUATION_ORDER_TYPE_SCREENING = "0";

        /**
         * 订单类型-可靠性
         */
        public static final String EVALUATION_ORDER_TYPE_RELIABILITY = "1";

        /**
         * 订单类型-监制
         */
        public static final String EVALUATION_ORDER_TYPE_SUPEVISION = "2";
        /**
         * 订单类型-验收
         */
        public static final String EVALUATION_ORDER_TYPE_ACCEPTANCE = "3";

        //===============能力核验====================

        /**
         * 具备
         */
        public static final String CAPABILITY_VERIFICATION_RESULT_TRUE = "0";

        /**
         * 不具备
         */
        public static final String CAPABILITY_VERIFICATION_RESULT_FALSE = "1";

        /**
         * 草稿
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_DRAFT = "0";

        /**
         * 已核验
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_VERIFIED = "1";

        /**
         * 待确认
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_PENDING_CONFIRMATION = "2";

        /**
         * 转开发
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_TRANSFERRED_TO_DEV = "3";

        /**
         * 已确认
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_CONFIRMED = "4";


        /**
         * 已对账
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_STATUS_YES="1";
        /**
         * 未对账
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_STATUS_NO="0";


        /**
         * 客户 accounting_standard_chargeStandardType
         * 收费标准类型
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_STATUS_KEHU="1";
        /**
         * 君信 accounting_standard_chargeStandardType
         * 收费标准类型
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN="0";


        //个数
        public static final String ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU="0";
        //批
        public static final String ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_PI="1";


        public static final String ACCOUNTING_STANDARD_DETAILS_DANWEI_CI="次";
        public static final String ACCOUNTING_STANDARD_DETAILS_DANWEI_H="h";



        /**
         * 核算方式 按分类
         */
        public  static final String ACCOUNTING_STANDARD_ACCOUNTINGMETHOD_FENLEI="0";

        /**
         * 核算方式 按折扣
         */
        public  static final String ACCOUNTING_STANDARD_ACCOUNTINGMETHOD_ZHEKOU="1";
    }


    public class ReprotType{
        public static final String REPROT_TYPE_COMMON="1";//测筛试验公共模版
        public static final String REPROT_TYPE_CUSTOMER="2";//客户定制测筛试验模版
    }

    public class Group{
        public static final String GROUP_KEKAOXING="KKX";//可靠性部
        public static final String GROUP_SHENGCHAN="SCHB";//生产部
        public static final String GROUP_CSJSB="CSJSB";//技术部
    }
    public class Role{
        public static final String ROLE_SHENGCHAN_DIAODU="生产部调度员";
        public static final String ROLE_KEKAOXING_DIAODU="可靠性调度员";
        public static final String ROLE_SHENGCHAN_ZHUGUAN="生产部主管";
        public static final String ROLE_KEKAOXING_ZHUGUAN="可靠性主管";
        public static final String ROLE_SHENCHAN_REPORTMANAGE="生产部报告管理员";
        public static final String ROLE_KEKAOXING_REPORTMANAGE="可靠性报告管理员";

        public static final String ROLE_SHENGCHAN_GENERATEREPORT="生产部出报告员";
        public static final String ROLE_KEKAOXING_GENERATEREPORT="可靠性出报告员";
        public static final String ROLE_SHENGCHAN_ZHIDAN="生产部制单员";
        public static final String ROLE_KEKAOXING_ZHIDAN="可靠性制单员";
        public static final String ROLE_DIAODU="调度";
    }
    //设备管理
    public class deviceManagement{
        /**
         * 已完成
         */
        public static final String DEVICE_MANAGEMENT_STATUS_FINISHED ="1";
        /**
         * 已过期
         */
        public static final  String DEVICE_MANAGEMENT_STATUS_EXPIRED = "2";
        /**
         * 未开始
         */
        public static final String DEVICE_MANAGEMENT_STATUS_UN_FINISHED = "3";

        /**
         * 已失效
         */
        public static final String DEVICE_MANAGEMENT_STATUS_INVALID = "4";

        /**
         * 设备台账状态-未校准
         */
        public static final  String EQUIPMENT_INVENTORY_STATUS_UNCALIBRATED = "0";

        /**
         * 设备台账状态-正常使用
         */
        public static final String EQUIPMENT_INVENTORY_STATUS_IN_USE = "1";

        /**
         * 设备台账状态-停用
         */
        public static final String EQUIPMENT_INVENTORY_STATUS_SUSPENDED = "2";

        /**
         * 设备台账状态-报废
         */
        public static final String EQUIPMENT_INVENTORY_STATUS_SCRAPPED = "3";

        /**
         * 设备台账状态-过期
         */
        public static final String EQUIPMENT_INVENTORY_STATUS_EXPIRED = "4";

        /**
         * 是否校准核查操作-不是
         */
        public static final String IS_CALIBRATION_CHECK_NO = "0";

        /**
         * 是否校准核查操作-是
         */
        public static final String IS_CALIBRATION_CHECK_YES = "1";
    }
    /**
     * 技术管理
     */
    public class TechnicalManagement {

        //================产品列表=======================
        /**
         * 进行中
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_WEIKAISHI = "0";
        public static final String CAPABILITY_VERIFICATION_STATUS_JINXINGZHONG = "1";
        public static final String CAPABILITY_VERIFICATION_STATUS_QUANBUWANCHENG = "2";
        public static final String CAPABILITY_VERIFICATION_STATUS_YICHANGGUANBI = "3";
        public static final String CAPABILITY_VERIFICATION_STATUS_BUFENWANCHENG = "4";

        public static final String CAPABILITY_VERIFICATION_COMPLETED_BUFENWANCHENG = "0";
        public static final String CAPABILITY_VERIFICATION_COMPLETED_QUANBUWANCHENG = "1";
        public static final String CAPABILITY_VERIFICATION_COMPLETED_YICHANGGUANBI = "2";

        /**
         * 是否具备 字典值
         */
        public static final String CAPABILITY_VERIFICATION_VERIFICATIONRESULT_YES = "0";
        public static final String CAPABILITY_VERIFICATION_VERIFICATIONRESULT_NO = "1";

        public static final String CAPABILITY_REVIEW_ZIXUN = "0";
        public static final String CAPABILITY_REVIEW_DINGDAN = "1";

        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_CESHI_TEXT = "测试"; //检验类型-测试
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_LAOHUA_TEXT = "老化"; //检验类型-老化



        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_CESHI = "0"; //检验类型-测试
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_LAOHUA = "1"; //检验类型-老化
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_DPA = "2"; //检验类型-DPA
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_ZXFX= "3"; //检验类型-专项分析
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_SXFX = "4"; //检验类型-失效分析
//        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_KKXFX= "5"; //检验类型-可靠性分析
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_JDSY = "6"; //检验类型-鉴定试验
//        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_WBJDSY = "7"; //检验类型-微波鉴定试验
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_ZLYZX = "8"; //检验类型-质量一致性
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_QTSY= "9"; //检验类型-其他试验



        //反馈/处理
        public static final String FEEDBACKPROCESSING_FANKUIQUERENKAIFA = "0"; //反馈确认开发
        public static final String FEEDBACKPROCESSING_BUKESHAI = "1"; //不可做
        public static final String FEEDBACKPROCESSING_DAIZHUANKAIFA = "2"; //待转开发
        public static final String FEEDBACKPROCESSING_WUXUHEYAN = "3"; //无需核验
        public static final String FEEDBACKPROCESSING_YIJUBEINENGLI = "4"; //已具备能力
        public static final String FEEDBACKPROCESSING_YIZHUANKAIFA = "5"; //已转开发

        //评审结果
        public static final String CAPABILITY_REVIEW_RESULT_NO = "0";//不通过
        public static final String CAPABILITY_REVIEW_RESULT_YES = "1";//通过

        //===============能力资产=================

//        public static final String CAPABILITY_ASSET_CAPABILITY_TYPE_CESHI = "0"; //能力类型-测试
//        public static final String CAPABILITY_ASSET_CAPABILITY_TYPE_LAOHUA = "1"; //能力类型-老化

        //===============能力开发================
        public static final String CAPABILITY_DEVELOPMENT_SOURCE_PROJECT = "0"; //立项开发
        public static final String CAPABILITY_DEVELOPMENT_SOURCE_PRODUCTION = "1"; //生产开发

        //能力开发状态
        public  static final  String CAPABILITY_DEVELOPMENT_STATUS_WEIKAISHI="0";//未开始
        public  static final  String CAPABILITY_DEVELOPMENT_STATUS_JINXINGZHONG="1";//进行中
        public  static final  String CAPABILITY_DEVELOPMENT_STATUS_QUANBUWANCHENG="2";//全部完成
        public  static final  String CAPABILITY_DEVELOPMENT_STATUS_YICHANGGUANBI="3";//异常关闭
        public  static final  String CAPABILITY_DEVELOPMENT_STATUS_BUFENWANCHENG="4";//部分完成



        public  static final  String CAPABILITY_DEVELOPMENT_DATA_SOURCE_NLKF="1";//能力开发
        public  static final  String CAPABILITY_DEVELOPMENT_DATA_SOURCE_NLPS="2";//能力评审
        public  static final  String CAPABILITY_DEVELOPMENT_DATA_SOURCE_NLHY="3";//能力核验
    }
    //体系文件
    public class SystemFileFileType{
        /**
         * 质量手册
         * 0
         * 程序文件
         * 1
         * 作业指导书
         * 2
         * 表单
         * 3
         */
        public static final String SYSTEM_FILE_FILETYPE_ZHILIANGSHOUCE="0";
        public static final String SYSTEM_FILE_FILETYPE_CHENGXUWENJIAN="1";
        public static final String SYSTEM_FILE_FILETYPE_ZHIYINSHU="2";
        public static final String SYSTEM_FILE_FILETYPE_BIAODAN="3";
    }

    /**
     * 库存字典常量字段
     */
    public class Warehousing{
        /**
         * 入库状态-未入库
         */
        public static final String WAREHOUSING_UNSTOCK = "0";

        /**
         * 入库状态-已入库
         */
        public static final String WAREHOUSING_INSTOCK = "1";
        public static final String WAREHOUSING_OUTSTOCK = "1";

    }


    public class AccountingStandard{
        public static final String ACCOUNTING_JUNXIN="0";//财务
        public static final String ACCOUNTING_CUSTOMER="1";//预算
    }

    public class ReportManagement{
        public static final String REPORT_STATUS_DRAFT="0";//草稿
        public static final String REPORT_STATUS_WAITAPPROVE="1";//待审批
        public static final String REPORT_STATUS_APPROVED="2";//审批通过
        public static final String REPORT_STATUS_REJCCT="3";//审批驳回
    }

    public class FileReview {
        public static final String REVIEW_RESULT_PASS = "1";//通过
        public static final String REVIEW_RESULT_NOPASS = "0";//不通过
    }
}
