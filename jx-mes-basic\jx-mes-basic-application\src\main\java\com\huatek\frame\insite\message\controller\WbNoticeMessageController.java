package com.huatek.frame.insite.message.controller;

import com.huatek.frame.insite.message.domain.WbNoticeMessage;
import com.huatek.frame.insite.message.service.IWbNoticeMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 通知信息框Controller
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@RestController
@RequestMapping("/WbNoticeMessage/WbNoticeMessage")
public class WbNoticeMessageController
{
//    @Autowired
//    private IWbNoticeMessageService wbNoticeMessageService;
//
//    /**
//     * 查询通知信息框列表
//     */
//    @Anonymous
//    @GetMapping("/list")
//    public TableDataInfo list(WbNoticeMessage wbNoticeMessage)
//    {
//        startPage();
//        List<WbNoticeMessage> list = wbNoticeMessageService.selectWbNoticeMessageList(wbNoticeMessage);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出通知信息框列表
//     */
//    @PreAuthorize("@ss.hasPermi('WbNoticeMessage:WbNoticeMessage:export')")
//    @Log(title = "通知信息框", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, WbNoticeMessage wbNoticeMessage)
//    {
//        List<WbNoticeMessage> list = wbNoticeMessageService.selectWbNoticeMessageList(wbNoticeMessage);
//        ExcelUtil<WbNoticeMessage> util = new ExcelUtil<WbNoticeMessage>(WbNoticeMessage.class);
//        util.exportExcel(response, list, "通知信息框数据");
//    }
//
//    /**
//     * 获取通知信息框详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('WbNoticeMessage:WbNoticeMessage:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(wbNoticeMessageService.selectWbNoticeMessageById(id));
//    }
//
//    /**
//     * 新增通知信息框
//     */
//    @PreAuthorize("@ss.hasPermi('WbNoticeMessage:WbNoticeMessage:add')")
//    @Log(title = "通知信息框", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody WbNoticeMessage wbNoticeMessage)
//    {
//        return toAjax(wbNoticeMessageService.insertWbNoticeMessage(wbNoticeMessage));
//    }
//
//    /**
//     * 修改通知信息框
//     */
//    @PreAuthorize("@ss.hasPermi('WbNoticeMessage:WbNoticeMessage:edit')")
//    @Log(title = "通知信息框", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody WbNoticeMessage wbNoticeMessage)
//    {
//        return toAjax(wbNoticeMessageService.updateWbNoticeMessage(wbNoticeMessage));
//    }
//
//    /**
//     * 删除通知信息框
//     */
//    @PreAuthorize("@ss.hasPermi('WbNoticeMessage:WbNoticeMessage:remove')")
//    @Log(title = "通知信息框", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(wbNoticeMessageService.deleteWbNoticeMessageByIds(ids));
//    }
//
//    /**
//     * 消息设置为已读
//     */
//    @PutMapping("/updateReadStatus/{id}")
//    public AjaxResult updateReadStatus(@PathVariable Long id)
//    {
//        return toAjax(wbNoticeMessageService.updateWbNoticeMessageReadStatus(id));
//    }
}
