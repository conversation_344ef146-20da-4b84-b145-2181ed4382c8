package com.huatek.frame.modules.business.utils;

import com.huatek.frame.modules.business.conf.ApsConfiguration;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockMultipartFile;

import static org.mockito.Mockito.when;

/**
 * HttpClientUtil测试类
 * 测试修改后的callMes和callMesWithFile方法
 */
public class HttpClientUtilTest {

    @Mock
    private ApsConfiguration apsConfiguration;

    @InjectMocks
    private HttpClientUtil httpClientUtil;

    private MockHttpServletRequest request;
    private InputParamDto inputParamDto;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 模拟配置
        when(apsConfiguration.getIp()).thenReturn("localhost");
        
        // 创建测试请求
        request = new MockHttpServletRequest();
        request.addHeader("token", "test-token");
        
        // 创建测试参数
        inputParamDto = new InputParamDto();
        inputParamDto.ServiceUrl = "/test/api";
        inputParamDto.Param = "{\"test\":\"data\"}";
        inputParamDto.HttpMethod = "POST";
    }

    @Test
    void testCallMesWithFormData() {
        // 这个测试主要验证方法调用不会抛出编译错误
        // 实际的网络调用会失败，但我们主要关注请求格式是否正确
        
        try {
            String result = httpClientUtil.callMes(request, inputParamDto);
            System.out.println("callMes result: " + result);
        } catch (RuntimeException e) {
            // 预期会有网络异常，因为测试环境中没有真实的APS服务
            System.out.println("Expected network exception: " + e.getMessage());
        }
    }

    @Test
    void testCallMesWithFile() {
        // 创建模拟文件
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "test.txt", 
                "text/plain", 
                "test file content".getBytes()
        );
        
        try {
            String result = httpClientUtil.callMesWithFile(request, inputParamDto, file);
            System.out.println("callMesWithFile result: " + result);
        } catch (RuntimeException e) {
            // 预期会有网络异常，因为测试环境中没有真实的APS服务
            System.out.println("Expected network exception: " + e.getMessage());
        }
    }

    @Test
    void testCallMesWithNullFile() {
        // 测试传入null文件的情况
        try {
            String result = httpClientUtil.callMesWithFile(request, inputParamDto, null);
            System.out.println("callMesWithFile (null file) result: " + result);
        } catch (RuntimeException e) {
            // 预期会有网络异常，因为测试环境中没有真实的APS服务
            System.out.println("Expected network exception: " + e.getMessage());
        }
    }

    /**
     * 测试InputParamDto字段访问
     */
    @Test
    void testInputParamDtoFieldAccess() {
        InputParamDto dto = new InputParamDto();
        dto.ServiceUrl = "/test/service";
        dto.Param = "{\"key\":\"value\"}";
        dto.HttpMethod = "GET";
        
        // 验证字段可以正常访问
        assert dto.ServiceUrl.equals("/test/service");
        assert dto.Param.equals("{\"key\":\"value\"}");
        assert dto.HttpMethod.equals("GET");
        
        System.out.println("InputParamDto field access test passed");
    }
}
