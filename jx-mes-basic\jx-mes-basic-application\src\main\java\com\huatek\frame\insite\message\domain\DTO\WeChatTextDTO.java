package com.huatek.frame.insite.message.domain.DTO;

import lombok.Data;

@Data
public class WeChatTextDTO {

    //text专用
    private String content;//消息内容，最长不超过2048个字节，超过将截断（支持id转译）

    //textcard专用
    private String title;//标题，不超过128个字符，超过会自动截断（支持id转译）
    private String description;//描述，不超过512个字符，超过会自动截断（支持id转译）
    private String url;//点击后跳转的链接。最长2048字节，请确保包含了协议头(http/https)
    private String btntxt;//按钮文字。 默认为“详情”， 不超过4个文字，超过自动截断。

    //图文、语音、视频等...

}
