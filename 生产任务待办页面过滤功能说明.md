# 生产任务待办页面过滤功能说明

## 功能需求

在生产任务待办页面中，对于相同工单编号的多个任务，只展示执行顺序最小且状态为未完成的任务。

### 具体规则
- **适用页面**: 待办页面（toDoOrAll = "0"）
- **过滤条件**: 相同工单编号（work_order_number）
- **展示规则**: 只显示执行顺序（execution_sequence）最小的任务
- **状态限制**: 只考虑未完成状态的任务
  - 未开始 (0)
  - 进行中 (1) 
  - 待审批 (2)
  - 驳回 (3)
  - 暂停 (4)

### 示例场景
工单编号 WO001 有3个任务：
- 任务1：执行顺序=1，状态=未开始
- 任务2：执行顺序=2，状态=未开始  
- 任务3：执行顺序=3，状态=未开始

**待办页面只显示**: 任务1（执行顺序=1）
**全部页面显示**: 任务1、任务2、任务3

## 技术实现

### 1. 修改SQL查询

在 `ProductionTaskMapper.xml` 的 `selectProductionTaskPage` 方法中添加过滤条件：

```xml
<!-- 待办页面：相同工单编号只显示执行顺序最小的未完成任务 -->
<if test="toDoOrAll != null and toDoOrAll == '0'">
    and t.execution_sequence = (
        select min(t2.execution_sequence)
        from production_task t2
        where t2.work_order_number = t.work_order_number
        and t2.codex_torch_deleted = '0'
        and t2.status in ('0', '1', '2', '3', '4')
    )
</if>
```

### 2. 过滤逻辑说明

#### 2.1 条件判断
- `toDoOrAll == '0'`: 只在待办页面生效
- `BusinessConstant.PRODUCTIONTASK_TODO` 的值为 "0"

#### 2.2 子查询逻辑
```sql
select min(t2.execution_sequence)
from production_task t2
where t2.work_order_number = t.work_order_number
and t2.codex_torch_deleted = '0'
and t2.status in ('0', '1', '2', '3', '4')
```

**子查询作用**:
1. 查找相同工单编号的所有任务
2. 过滤掉已删除的任务
3. 只考虑未完成状态的任务
4. 返回最小的执行顺序

#### 2.3 主查询过滤
```sql
and t.execution_sequence = (子查询结果)
```

确保当前任务的执行顺序等于该工单下未完成任务的最小执行顺序。

### 3. 状态常量映射

| 状态代码 | 状态名称 | 常量定义 |
|---------|---------|----------|
| 0 | 未开始 | `PRODUCTION_TASK_STATUS_WEIKAISHI` |
| 1 | 进行中 | `PRODUCTION_TASK_STATUS_JINXINGZHONG` |
| 2 | 待审批 | `PRODUCTION_TASK_STATUS_DAIAPPROVE` |
| 3 | 驳回 | `PRODUCTION_TASK_STATUS_BOHUI` |
| 4 | 暂停 | `PRODUCTION_TASK_STATUS_ZANTING` |
| 5 | 取消 | `PRODUCTION_TASK_STATUS_QUXIAO` |
| 6 | 完成 | `PRODUCTION_TASK_STATUS_WANCHENG` |
| 7 | 已外协 | `PRODUCTION_TASK_STATUS_YIWAIXIE` |

### 4. 现有逻辑保持不变

在 `ProductionTaskServiceImpl.queryExportCommon()` 方法中，待办页面的状态过滤逻辑保持不变：

```java
if (BusinessConstant.PRODUCTIONTASK_TODO.equals(dto.getToDoOrAll())) {
    if (StringUtils.isEmpty(dto.getStatus())) {
        dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI + "," +
                DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG + "," +
                DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING + "," +
                DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI + "," +
                DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE);
    }
}
```

## 测试验证

### 1. 测试类
创建了 `ProductionTaskTodoFilterTest.java` 测试类，包含以下测试方法：

- `testTodoPageShowsMinExecutionSequenceOnly()`: 验证待办页面过滤逻辑
- `testSpecificWorkOrderFiltering()`: 测试特定工单的过滤
- `testExecutionSequenceFiltering()`: 专门测试执行顺序过滤

### 2. 验证要点

1. **数量验证**: 待办页面的任务数量 ≤ 全部页面的任务数量
2. **执行顺序验证**: 每个工单在待办页面只显示最小执行顺序的任务
3. **状态验证**: 只显示未完成状态的任务
4. **工单唯一性**: 每个工单在待办页面最多只有一个任务

### 3. 测试数据准备

建议准备以下测试数据：
```sql
-- 工单WO001的多个任务
INSERT INTO production_task (work_order_number, execution_sequence, status, ...) VALUES
('WO001', 1, '0', ...),  -- 未开始
('WO001', 2, '0', ...),  -- 未开始
('WO001', 3, '1', ...);  -- 进行中

-- 工单WO002的多个任务（部分已完成）
INSERT INTO production_task (work_order_number, execution_sequence, status, ...) VALUES
('WO002', 1, '6', ...),  -- 已完成
('WO002', 2, '1', ...),  -- 进行中
('WO002', 3, '0', ...);  -- 未开始
```

**预期结果**:
- 待办页面显示: WO001的执行顺序1任务, WO002的执行顺序2任务
- 全部页面显示: 所有6个任务

## 影响范围

### 1. 直接影响
- **待办页面**: 显示的任务数量可能减少
- **用户体验**: 用户在待办页面只看到当前需要处理的任务

### 2. 不影响的功能
- **全部页面**: 显示逻辑不变
- **任务详情**: 查看和操作逻辑不变
- **任务状态流转**: 业务逻辑不变
- **权限控制**: 现有权限逻辑不变

### 3. 兼容性
- **向后兼容**: 不影响现有API接口
- **数据库兼容**: 不需要修改表结构
- **前端兼容**: 前端代码无需修改

## 部署注意事项

1. **数据验证**: 部署前确认执行顺序字段数据完整性
2. **性能测试**: 验证子查询对性能的影响
3. **功能测试**: 确认过滤逻辑符合业务预期
4. **用户培训**: 告知用户待办页面显示逻辑的变化

## 后续优化建议

1. **性能优化**: 如果数据量大，考虑添加索引或优化查询
2. **配置化**: 可以考虑将过滤规则配置化，支持开关控制
3. **监控**: 添加日志监控，跟踪过滤效果
4. **用户反馈**: 收集用户使用反馈，持续优化体验
