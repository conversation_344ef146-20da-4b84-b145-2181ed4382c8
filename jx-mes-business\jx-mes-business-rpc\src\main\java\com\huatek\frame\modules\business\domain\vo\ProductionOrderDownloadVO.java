package com.huatek.frame.modules.business.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;
import java.util.List;

/**
 * 生产工单下载工单返回VO实体
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProductionOrderDownloadVO {

    /**
     * 编号
     */
    private String workOrderNumber;
    /**
     * 产品名称
     */
    private String productionName;

    /**
     * 生产批次
     */
    private String productionBatch;

    /**
     * 送筛单位
     */
    private String customerNumber;

    /**
     * 质量等级
     */
    private String qualityGrade;

    /**
     * 要求完成时间
     */
    private String deadline;

    /**
     * 产品型号
     */
    private String productModel;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 完成时间
     */
    private String completionTime;

    List<ProcessFilterVO> processFilterVOList;

    /**
     * 制单人
     */
    private String orderCreator;

    /**
     * 批准人
     */
    private String approver;

    /**
     * 制单时间
     */
    private String orderCreateTime;

    /**
     * 审批时间
     */
    private String approveTime;

    /**
     * 备注
     */
    private String comment;

    /**
     * 测试图片url
     */
    private String imageUrl;

    /**
     * QRCode  Base64编码
     */
    private String QRCode;

}
