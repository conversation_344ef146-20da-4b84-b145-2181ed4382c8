<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProductionTaskMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.task_number as taskNumber,
		t.work_order_number as workOrderNumber,
		t.execution_sequence as executionSequence,
		t.asso_wo_pred_proc as assoWoPredProc,
		t.related_work_order as relatedWorkOrder,
		t.inspection_quantity2 as inspectionQuantity2,
		t.process_name2 as processName2,
		t.process_code as processCode,
		t.test_basis as testBasis,
		t.test_conditions as testConditions,
		t.judgment_criteria as judgmentCriteria,
		t.status as status,
		t.technical_competency_number as technicalCompetencyNumber,
		t.operation_card as operationCard,
		t.`comment` as `comment`,
		t.ticket_level as ticketLevel,
		t.scheduled_start_time as scheduledStartTime,
		t.scheduled_end_time as scheduledEndTime,
		t.product_name as productName,
		t.product_model as productModel,
		t.manufacturer as manufacturer,
		t.batch_number as batchNumber,
		t.product_category as productCategory,
		t.product_information1 as productInformation1,
		t.entrusted_unit as entrustedUnit,
		t.`grouping` as `grouping`,
		t.test_methodology as testMethodology,
		t.actual_start_time as actualStartTime,
		t.actual_end_time as actualEndTime,
		t.test_type as testType,
		t.department as department,
		t.belonging_team2 as belongingTeam2,
		t.reporting_time0 as reportingTime0,
		t.pda_warning as pdaWarning,
		t.reporter4 as reporter4,
		t.non_conformity_number as nonConformityNumber,
		t.qualified_quantity as qualifiedQuantity,
		t.unqualified_quantity as unqualifiedQuantity,
		t.failure_mode as failureMode,
		t.completion_time6 as completionTime6,
		t.pda as pda,
		t.record_change_stamp as recordChangeStamp,
		t.humidity as humidity,
		t.temperature as temperature,
		t.test_result_summary as testResultSummary,
		t.report_work_remarks as reportWorkRemarks,
		t.attachment as attachment,
		t.assoc_exception_feedback_num as assocExceptionFeedbackNum,
		t.pause_reason as pauseReason,
        t.workstation as workstation,
		t.completed_quantity as completedQuantity,
		t.customer_process_name as customerProcessName,
        t.generate_report as generateReport,
        t.order_number as orderNumber,
		t.display_number as displayNumber,
        t.aps_schedule_plan_step_id as apsSchedulePlanStepId,
        t.predecessor_work_order as predecessorWorkOrder,
		t.responsible_person as responsiblePerson,
        t.pre_execution_sequence as preExecutionSequence,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
        t.customer_experiment_project_id as customerExperimentProjectId
	</sql>
    <sql id="Base_Column_List2">
        t.id as id,
        t.task_number as taskNumber,
        t.work_order_number as workOrderNumber,
        t.execution_sequence as executionSequence,
        t.asso_wo_pred_proc as assoWoPredProc,
        t.related_work_order as relatedWorkOrder,
        t.inspection_quantity2 as inspectionQuantity2,
        t.process_name2 as processName2,
        t.process_code as processCode,
        t.test_basis as testBasis,
        t.test_conditions as testConditions,
        t.judgment_criteria as judgmentCriteria,
        t.status as status,
        t.technical_competency_number as technicalCompetencyNumber,
        t.operation_card as operationCard,
        t.`comment` as `comment`,
        t.ticket_level as ticketLevel,
        t.scheduled_start_time as scheduledStartTime,
        t.scheduled_end_time as scheduledEndTime,
        t.product_name as productName,
        t.product_model as productModel,
        t.manufacturer as manufacturer,
        t.batch_number as batchNumber,
        pc.category_name as productCategory,
        COALESCE(ss.attachment, pim.attachment) AS productInformation1,
        t.entrusted_unit as entrustedUnit,
        t.`grouping` as `grouping`,
        t.test_methodology as testMethodology,
        t.actual_start_time as actualStartTime,
        t.actual_end_time as actualEndTime,
        t.test_type as testType,
        t.department as department,
        t.belonging_team2 as belongingTeam2,
        t.reporting_time0 as reportingTime0,
        t.pda_warning as pdaWarning,
        t.reporter4 as reporter4,
        t.non_conformity_number as nonConformityNumber,
        t.qualified_quantity as qualifiedQuantity,
        t.unqualified_quantity as unqualifiedQuantity,
        t.failure_mode as failureMode,
        t.completion_time6 as completionTime6,
        t.pda as pda,
        t.record_change_stamp as recordChangeStamp,
        t.humidity as humidity,
        t.temperature as temperature,
        t.test_result_summary as testResultSummary,
        t.report_work_remarks as reportWorkRemarks,
        t.attachment as attachment,
        t.assoc_exception_feedback_num as assocExceptionFeedbackNum,
        t.pause_reason as pauseReason,
        t.workstation as workstation,
        t.completed_quantity as completedQuantity,
        t.customer_process_name as customerProcessName,
        t.generate_report as generateReport,
        t.order_number as orderNumber,
        t.display_number as displayNumber,
        t.responsible_person as responsiblePerson,
        su.user_name as codexTorchCreatorId,
        su2.user_name as codexTorchUpdater,
        t.predecessor_work_order as predecessorWorkOrder,
        t.codex_torch_group_id as codexTorchGroupId,
        t.codex_torch_create_datetime as codexTorchCreateDatetime,
        t.codex_torch_update_datetime as codexTorchUpdateDatetime,
        t.codex_torch_deleted as codexTorchDeleted,
        t.aps_schedule_plan_step_id as apsSchedulePlanStepId,
        t.pre_execution_sequence as preExecutionSequence,
        w.entrusted_unit entrustedUnitName,
        sg.group_name departmentName,
        sg2.group_name belongingTeam2Name,
        t.customer_experiment_project_id as customerExperimentProjectId,
        po.id as productionOrderId,
        po2.work_order_number as relatedWorkOrderNumber
    </sql>
	<select id="selectProductionTaskPage" parameterType="com.huatek.frame.modules.business.service.dto.ProductionTaskDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
        select
        <include refid="Base_Column_List2" />
        from production_task t
        left join customer_information_management w on t.entrusted_unit = w.id
        left join sys_group sg on t.department = sg.id
        left join sys_group sg2 on t.belonging_team2 = sg2.group_code
        LEFT JOIN standard_specification ss
        ON t.product_information1 = ss.id
        LEFT JOIN product_information_management pim
        ON t.product_information1 = pim.id
        left join product_category pc on t.product_category = pc.id
        left join sys_user su on t.codex_torch_creator_id  = su.id
        left join sys_user su2 on t.codex_torch_updater  = su2.id
        left join production_order po on t.work_order_number = po.work_order_number
        left join production_order po2 on t.related_work_order = po2.work_order_number
            <where>
                and t.codex_torch_deleted = '0'
                <!-- 待办页面：相同工单编号只显示执行顺序最小的未完成任务 -->
                <if test="toDoOrAll != null and toDoOrAll == 0">
                    and t.execution_sequence = (
                        select min(t2.execution_sequence)
                        from production_task t2
                        where t2.work_order_number = t.work_order_number
                        and t2.codex_torch_deleted = '0'
                        and t2.status in ('0', '1', '2', '3', '4')
                    )
                </if>
                <if test="taskNumber != null and taskNumber != ''">
                    and t.task_number  like concat('%', #{taskNumber} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="processCode != null and processCode != ''">
                    and t.process_code  like concat('%', #{processCode} ,'%')
                </if>
                <if test="pauseReason != null and pauseReason != ''">
                    and t.pause_reason like concat('%', #{pauseReason} ,'%')
                </if>
                <if test="completedQuantity != null">
                    and t.completed_quantity = #{completedQuantity}
                </if>
                <if test="testBasis != null and testBasis != ''">
                    and t.test_basis  like concat('%', #{testBasis} ,'%')
                </if>
                <if test="testConditions != null and testConditions != ''">
                    and t.test_conditions  like concat('%', #{testConditions} ,'%')
                </if>
                <if test="judgmentCriteria != null and judgmentCriteria != ''">
                    and t.judgment_criteria  like concat('%', #{judgmentCriteria} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  in
                    <foreach collection="status.split(',')" item="s" open="(" separator="," close=")">
                        #{s}
                    </foreach>
                </if>
                <if test="technicalCompetencyNumber != null and technicalCompetencyNumber != ''">
                    and t.technical_competency_number  = #{technicalCompetencyNumber}
                </if>
                <if test="operationCard != null and operationCard != ''">
                    and t.operation_card  like concat('%', #{operationCard} ,'%')
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="ticketLevel != null and ticketLevel != ''">
                    and t.ticket_level  = #{ticketLevel}
                </if>
                <if test="scheduledStartTime != null">
                    and t.scheduled_start_time  = #{scheduledStartTime}
                </if>
                <if test="scheduledEndTime != null">
                    and t.scheduled_end_time  = #{scheduledEndTime}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and w.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="grouping != null and grouping != ''">
                    and t.grouping  like concat('%', #{grouping} ,'%')
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="actualStartTime != null">
                    and t.actual_start_time  = #{actualStartTime}
                </if>
                <if test="actualEndTime != null">
                    and t.actual_end_time  = #{actualEndTime}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                <if test="department != null and department != ''">
                    and sg.group_name    like concat('%', #{department} ,'%')
                </if>
                <if test="workstation != null and workstation != ''">
                    and t.workstation  = #{workstation}
                </if>
                <if test="belongingTeam2 != null and belongingTeam2 != ''">
                    and sg2.group_name  like concat('%', #{belongingTeam2} ,'%')
                </if>
                <if test="belongingTeamId != null and belongingTeamId != ''">
                    and t. belonging_team2 =#{belongingTeamId}
                </if>
                <if test="reportingTime0 != null">
                    and t.reporting_time0  = #{reportingTime0}
                </if>
                <if test="pdaWarning != null and pdaWarning != ''">
                    and t.pda_warning = #{pdaWarning}
                </if>
                <if test="reporter4 != null and reporter4 != ''">
                    and t.reporter4  like concat('%', #{reporter4} ,'%')
                </if>
                <if test="nonConformityNumber != null and nonConformityNumber != ''">
                    and t.non_conformity_number  like concat('%', #{nonConformityNumber} ,'%')
                </if>
                <if test="qualifiedQuantity != null and qualifiedQuantity != ''">
                    and t.qualified_quantity  = #{qualifiedQuantity}
                </if>
                <if test="unqualifiedQuantity != null and unqualifiedQuantity != ''">
                    and t.unqualified_quantity  = #{unqualifiedQuantity}
                </if>
                <if test="failureMode != null and failureMode != ''">
                    and t.failure_mode  = #{failureMode}
                </if>
                <if test="completionTime6 != null">
                    and t.completion_time6  = #{completionTime6}
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="recordChangeStamp != null and recordChangeStamp != ''">
                    and t.record_change_stamp  = #{recordChangeStamp}
                </if>
                <if test="humidity != null and humidity != ''">
                    and t.humidity  like concat('%', #{humidity} ,'%')
                </if>
                <if test="temperature != null and temperature != ''">
                    and t.temperature  like concat('%', #{temperature} ,'%')
                </if>
                <if test="testResultSummary != null and testResultSummary != ''">
                    and t.test_result_summary  like concat('%', #{testResultSummary} ,'%')
                </if>
                <if test="reportWorkRemarks != null and reportWorkRemarks != ''">
                    and t.report_work_remarks  like concat('%', #{reportWorkRemarks} ,'%')
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="customerProcessName != null and customerProcessName != ''">
                    and t.customer_process_name  like concat('%', #{customerProcessName} ,'%')
                </if>
                <if test="displayNumber != null">
                    and t.display_number  = #{displayNumber}
                </if>
                <if test="generateReport != null and generateReport != ''">
                    and t.generate_report  = #{generateReport}
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  = #{orderNumber}
                </if>   

                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                <if test="belongingGroup != null and belongingGroup != ''">
                    or exists (
                        select 1 from prod_task_eq_info ptei
                        left join equipment_inventory ei on ptei.device_serial_number = ei.device_serial_number
                        where ptei.task_number = t.task_number
                        and ptei.codex_torch_deleted = '0'
                        and ei.belonging_group = #{belongingGroup}
                        order by ptei.codex_torch_create_datetime asc
                        limit 1
                    )
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    or t.responsible_person =#{codexTorchDeleted}
                </if>
                ${params.dataScope}
            </where>

        order by  t.codex_torch_create_datetime desc
	</select>
     <select id="selectOptionsByTechnicalCompetencyNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.capability_number label,
        	t.capability_number value
        from capability_asset t
        WHERE t.capability_number != ''
         and t.codex_torch_deleted = '0'
     </select>
     <select id="selectDataLinkageByTechnicalCompetencyNumber" parameterType="String"
             resultType="java.util.Map">
        select
            t.operation_card as operationCard
        from capability_asset t
        WHERE t.capability_number = #{capability_number}
            and t.codex_torch_deleted = '0'
     </select>

    <select id="selectProductionTaskList" parameterType="com.huatek.frame.modules.business.service.dto.ProductionTaskDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
        select
        <include refid="Base_Column_List2" />
        from production_task t
        left join customer_information_management w on t.entrusted_unit = w.id
        left join sys_group sg on t.department = sg.id
        left join sys_group sg2 on t.belonging_team2 = sg2.group_code
        LEFT JOIN standard_specification ss
        ON t.product_information1 = ss.id
        LEFT JOIN product_information_management pim
        ON t.product_information1 = pim.id
        left join product_category pc on t.product_category = pc.id
        left join sys_user su on t.codex_torch_creator_id  = su.id
        left join sys_user su2 on t.codex_torch_updater  = su2.id
        left join production_order po on t.work_order_number = po.work_order_number
        left join production_order po2 on t.related_work_order = po2.work_order_number
            <where>
                and t.codex_torch_deleted = '0'
                <!-- 待办页面：相同工单编号只显示执行顺序最小的未完成任务 -->
                <if test="toDoOrAll != null and toDoOrAll == 0">
                    and t.execution_sequence = (
                    select min(t2.execution_sequence)
                    from production_task t2
                    where t2.work_order_number = t.work_order_number
                    and t2.codex_torch_deleted = '0'
                    and t2.status in ('0', '1', '2', '3', '4')
                    )
                </if>
                <if test="taskNumber != null and taskNumber != ''">
                    and t.task_number  like concat('%', #{taskNumber} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="inspectionQuantity2 != null and inspectionQuantity2 != ''">
                    and t.inspection_quantity2  = #{inspectionQuantity2}
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="processCode != null and processCode != ''">
                    and t.process_code  like concat('%', #{processCode} ,'%')
                </if>
                <if test="testBasis != null and testBasis != ''">
                    and t.test_basis  like concat('%', #{testBasis} ,'%')
                </if>
                <if test="testConditions != null and testConditions != ''">
                    and t.test_conditions  like concat('%', #{testConditions} ,'%')
                </if>
                <if test="judgmentCriteria != null and judgmentCriteria != ''">
                    and t.judgment_criteria  like concat('%', #{judgmentCriteria} ,'%')
                </if>
                <if test="status != null and status != ''">
                    and t.status  in
                    <foreach collection="status.split(',')" item="s" open="(" separator="," close=")">
                        #{s}
                    </foreach>
                </if>
                <if test="technicalCompetencyNumber != null and technicalCompetencyNumber != ''">
                    and t.technical_competency_number  = #{technicalCompetencyNumber}
                </if>
                <if test="operationCard != null and operationCard != ''">
                    and t.operation_card  like concat('%', #{operationCard} ,'%')
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="ticketLevel != null and ticketLevel != ''">
                    and t.ticket_level  = #{ticketLevel}
                </if>
                <if test="scheduledStartTime != null">
                    and t.scheduled_start_time  = #{scheduledStartTime}
                </if>
                <if test="scheduledEndTime != null">
                    and t.scheduled_end_time  = #{scheduledEndTime}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and t.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name  like concat('%', #{productCategory} ,'%')
                </if>
                <if test="productInformation1 != null and productInformation1 != ''">
                    and t.product_information1  = #{productInformation1}
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and w.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="grouping != null and grouping != ''">
                    and t.grouping  like concat('%', #{grouping} ,'%')
                </if>
                <if test="testMethodology != null and testMethodology != ''">
                    and t.test_methodology  = #{testMethodology}
                </if>
                <if test="actualStartTime != null">
                    and t.actual_start_time  = #{actualStartTime}
                </if>
                <if test="actualEndTime != null">
                    and t.actual_end_time  = #{actualEndTime}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                 <if test="department != null and department != ''">
                    and sg.group_name  like concat('%', #{department} ,'%')
                </if>
                <if test="workstation != null and workstation != ''">
                    and t.workstation  = #{workstation}
                </if>
                <if test="belongingTeam2 != null and belongingTeam2 != ''">
                    and sg2.group_name  like concat('%', #{belongingTeam2} ,'%')
                </if>
                <if test="reportingTime0 != null">
                    and t.reporting_time0  = #{reportingTime0}
                </if>
                <if test="pdaWarning != null and pdaWarning != ''">
                    and t.pda_warning  =#{pdaWarning}
                </if>
                <if test="reporter4 != null and reporter4 != ''">
                    and t.reporter4  like concat('%', #{reporter4} ,'%')
                </if>
                <if test="nonConformityNumber != null and nonConformityNumber != ''">
                    and t.non_conformity_number  like concat('%', #{nonConformityNumber} ,'%')
                </if>
                <if test="qualifiedQuantity != null and qualifiedQuantity != ''">
                    and t.qualified_quantity  = #{qualifiedQuantity}
                </if>
                <if test="unqualifiedQuantity != null and unqualifiedQuantity != ''">
                    and t.unqualified_quantity  = #{unqualifiedQuantity}
                </if>
                <if test="failureMode != null and failureMode != ''">
                    and t.failure_mode  = #{failureMode}
                </if>
                <if test="completionTime6 != null">
                    and t.completion_time6  = #{completionTime6}
                </if>
                <if test="pda != null and pda != ''">
                    and t.pda  = #{pda}
                </if>
                <if test="recordChangeStamp != null and recordChangeStamp != ''">
                    and t.record_change_stamp  = #{recordChangeStamp}
                </if>
                <if test="humidity != null and humidity != ''">
                    and t.humidity  like concat('%', #{humidity} ,'%')
                </if>
                <if test="temperature != null and temperature != ''">
                    and t.temperature  like concat('%', #{temperature} ,'%')
                </if>
                <if test="testResultSummary != null and testResultSummary != ''">
                    and t.test_result_summary  like concat('%', #{testResultSummary} ,'%')
                </if>
                <if test="reportWorkRemarks != null and reportWorkRemarks != ''">
                    and t.report_work_remarks  like concat('%', #{reportWorkRemarks} ,'%')
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="customerProcessName != null and customerProcessName != ''">
                    and t.customer_process_name  like concat('%', #{customerProcessName} ,'%')
                </if>
                <if test="displayNumber != null">
                    and t.display_number  = #{displayNumber}
                </if>
                <if test="generateReport != null and generateReport != ''">
                    and t.generate_report  = #{generateReport}
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and t.order_number  = #{orderNumber}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                <if test="belongingGroup != null and belongingGroup != ''">
                    or exists (
                        select 1 from prod_task_eq_info ptei
                        left join equipment_inventory ei on ptei.device_serial_number = ei.device_serial_number
                        where ptei.task_number = t.task_number
                        and ptei.codex_torch_deleted = '0'
                        and ei.belonging_group = #{belongingGroup}
                        order by ptei.codex_torch_create_datetime asc
                        limit 1
                    )
                </if>
                <if test="responsiblePerson != null and responsiblePerson != ''">
                    or t.responsible_person =#{codexTorchDeleted}
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProductionTaskListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
		select
		<include refid="Base_Column_List" />
			from production_task t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>



	<select id="selectProductionTaskByProductionOrder"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskViewVO">
        select
        cep.display_number as displayNumber,
        spm.process_name2 as processName2,
        cep.customer_process_name as customerProcessName,
        cep.execution_sequence as executionSequence,
        spm1.process_name2  as assoWoPredProc,
        cep.test_conditions as testConditions,
        cep.testing_times as testingTimes,
        cep.test_basis as testBasis,
        cep.duration_of_testing as durationOfTesting,
        cep.judgment_criteria as judgmentCriteria,
        w.workstation_name as workstation ,
        cep.product_information1 as productInformation1,
        pim.file_name  as productInformation1,
        pim.attachment as productInformationAttachment,
        cep.test_methodology as  testMethodology,
        cep.testing_team as testingTeam,
        cep.pda ,
        cep.`grouping`,
        cep.whether_to_include_in_scheduling as whetherToIncludeInScheduling,
        cep.estimated_start_time as estimatedStartTime,
        cep.estimated_end_time as estimatedEndTime,
        pt.inspection_quantity2 as inspectionQuantity2,
        pt.task_number as taskNumber ,
        pt.status ,
        pt.assoc_exception_feedback_num as assocExceptionFeedbackNum,
        pt.qualified_quantity as qualifiedQuantity ,
        pt.unqualified_quantity as unqualifiedQuantity,
        pt.failure_mode as failureMode,
        pt.reporter4 as reporter4,
        pt.reporting_time0 as reportingTime0,
        pt.process_code as processCode,
        pt.actual_start_time as actualStartTime
        from customer_process_scheme cps
        left join production_order po on cps.work_order  = po.id
        left join customer_experiment_project cep on cps.id  = cep.CODEX_TORCH_MASTER_FORM_ID
        left join standard_process_management spm on cep.process_id =spm.id
        left join production_task pt on  pt.work_order_number  = po.work_order_number and pt.process_code = cep.process_code3
        left join product_information_management pim on cep.product_information1  = pim.id
        left join standard_process_management spm1 on cep.asso_wo_pred_proc  = spm1.id
        left join workstation w on cep.workstation  = w.id
        where po.work_order_number =#{workOrderNumber}
    </select>

    <!-- 查询工单最后一个工序的合格数量 -->
    <select id="getLastProcessQualifiedQuantity" resultType="java.lang.Long">
        SELECT qualified_quantity
        FROM production_task
        WHERE work_order_number = #{workOrderNumber}
          AND codex_torch_deleted = '0'
          AND execution_sequence = (
              SELECT MAX(execution_sequence)
              FROM production_task
              WHERE work_order_number = #{workOrderNumber}
                AND codex_torch_deleted = '0'
                AND execution_sequence IS NOT NULL
          )
        LIMIT 1
    </select>

    <!-- 查询工单的所有不合格工序信息 -->
    <select id="getWorkOrderQualityInfo" resultType="com.huatek.frame.modules.business.domain.vo.UnqualifiedProcessVO">
        SELECT
            customer_process_name as processName,
            unqualified_quantity as unqualifiedQuantity,
            failure_mode as failureMode
        FROM production_task
        WHERE work_order_number = #{workOrderNumber}
          AND codex_torch_deleted = '0'
          AND unqualified_quantity > 0
        ORDER BY execution_sequence ASC
    </select>

    <!-- 检查前一个工序是否已完成 -->
    <select id="checkPreviousProcessStatus" resultType="java.lang.String">
        SELECT status
        FROM production_task
        WHERE work_order_number = #{workOrderNumber}
          AND execution_sequence = #{executionSequence} - 1
          AND codex_torch_deleted = '0'
        LIMIT 1
    </select>

    
    <!-- 检查是否为最后一个工序 -->
    <select id="isLastProcess" resultType="java.lang.String">
        SELECT id
        FROM production_task
        WHERE work_order_number = (
            SELECT work_order_number
            FROM production_task
            WHERE id = #{id}
        )
          AND execution_sequence = (
              SELECT MAX(execution_sequence)
              FROM production_task
              WHERE work_order_number = (
                  SELECT work_order_number
                  FROM production_task
                  WHERE id = #{id}
              )
          )
    </select>
    
    <select id="getSkillByCode" resultType="java.lang.String">
        SELECT skill
        FROM standard_process_management 
        WHERE step_number = #{processCode}
        AND codex_torch_deleted = '0' 
        LIMIT 1
    </select>
    <select id="findDelayedTasks" resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
        select
        t.*,
        w.entrusted_unit entrustedUnitName,
        sg.group_name departmentName,
        sg2.group_name belongingTeam2Name
        from production_task t
        left join customer_information_management w on t.entrusted_unit = w.id
        left join sys_group sg on t.department = sg.id
        left join sys_group sg2 on t.belonging_team2 = sg2.id
        where t.status = '0' and t.scheduled_start_time &lt; now()
    </select>

	<select id="selectProductionTaskByWorkOrderAndProcss"
            resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskVO">
        select <include refid="Base_Column_List"/> from production_task t
        where t.work_order_number =#{workOrder} and t.process_code =#{processCode}
    </select>
    <select id="findProcessesByWorkOrderNumber" resultType="com.huatek.frame.modules.business.domain.vo.ProductionTaskInfoVO">
        select
        t.customer_process_name as customerProcessName, t.test_basis as testBasis, t.display_number as displayNumber
        from production_task t
        where t.work_order_number = #{productionWorkOrderNumber}
    </select>

    <update id="updateApsSchedulePlanStepStateByStepId">
        UPDATE aps_schedule_plan_step
        SET state = #{state}
        WHERE isdeleted=0 and  stepId = #{apsSchedulePlanStepId}
    </update>

    <!-- 生产任务开始时更新APS排产计划步骤的实际开始时间和状态 -->
    <update id="updateApsSchedulePlanStepOnTaskStart">
        UPDATE aps_schedule_plan_step
        SET actualbegintime = #{actualStartTime},
            state = '2'
        WHERE isdeleted=0 and  stepId = #{apsSchedulePlanStepId}
    </update>

    <!-- 生产任务设置完成时间时更新APS排产计划步骤的实际结束时间 -->
    <update id="updateApsSchedulePlanStepActualEndTime">
        UPDATE aps_schedule_plan_step
        SET actualendtime = #{actualEndTime}
        WHERE isdeleted=0 and  stepId = #{apsSchedulePlanStepId}
    </update>

    <!-- 生产任务完成时更新APS排产计划步骤的状态 -->
    <update id="updateApsSchedulePlanStepOnTaskComplete">
        UPDATE aps_schedule_plan_step
        SET state = '3',reportresult=#{reportResult}
        WHERE isdeleted=0 and stepId = #{apsSchedulePlanStepId}
    </update>
    <update id="updateApsSchedulePlanState">
        UPDATE aps_schedule_plan
        SET state = '1'
        WHERE isdeleted=0 and id in
        <foreach collection="scheduleIds" close=")" open="(" separator="," index="" item="scheduleId">
            #{scheduleId}
        </foreach>
    </update>
    <update id="updateApsSchedulePlanStepState">
        UPDATE aps_schedule_plan_step
        SET state = '1'
        WHERE isdeleted=0 and scheduleid in
        <foreach collection="scheduleIds" close=")" open="(" separator="," index="" item="scheduleId">
            #{scheduleId}
        </foreach>
    </update>
    <update id="updateApsSchedulePlan">
        UPDATE aps_schedule_plan
        SET state = #{state}
        WHERE isdeleted=0 and productionplancode = #{workOrderNumber}
    </update>

</mapper>