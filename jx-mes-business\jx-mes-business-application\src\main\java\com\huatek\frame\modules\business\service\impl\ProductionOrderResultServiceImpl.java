package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.AttachmentService;
import com.huatek.frame.modules.business.service.AwaitingProductionOrderService;
import com.huatek.frame.modules.business.service.CapabilityAssetService;
import com.huatek.frame.modules.business.service.ProductionOrderResultService;
import com.huatek.frame.modules.business.service.dto.CapabilityAssetDTO;
import com.huatek.frame.modules.business.service.dto.CapabilityVerificationCheckDTO;
import com.huatek.frame.modules.business.service.dto.ProductionOrderResultDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;


/**
 * 工单检验结果 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@DubboService
@Slf4j
public class ProductionOrderResultServiceImpl implements ProductionOrderResultService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    protected Validator validator;
    @Autowired
    private AttachmentService attachmentService;
    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;
    @Autowired
    private CustomerProcessSchemeMapper customerProcessSchemeMapper;
    @Autowired
    private ProductionOrderProcessTestMapper productionOrderProcessTestMapper;
    @Autowired
    private ProductionOrderResultMapper productionOrderResultMapper;
	public ProductionOrderResultServiceImpl(){

	}

    @Override
    public ProductionOrderResultVO selectResultByProductionOrder(String workOrder) {
        return productionOrderResultMapper.selectByWorkOrder(workOrder);
    }

    @Override
    public TorchResponse getProductionOrderTestData(ProductionOrderResultDTO productionOrderDTO) {

        ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
        CustomerProcessScheme scheme = customerProcessSchemeMapper.selectByProductionOrder(order.getId());
        ProductionOrderResultVO vo =  productionOrderResultMapper.selectByWorkOrder(order.getWorkOrderNumber());
        if(ObjectUtils.isEmpty(vo)){
            vo = new ProductionOrderResultVO();
            vo.setTestSpecifications("标准规范名称/客户委托");
            vo.setOtherExplanationRemark(scheme.getReportComment());
            if(order.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)){
                vo.setInspectionConclusion("各项目试验均通过，分析结果合格");
            }
        }
        //根据工单查询所有的工序报工结果
       List<ProductionOrderProcessTestVO> processTestVOS =  productionOrderResultMapper.selectPrcessTestDataByWorkOrder(order.getWorkOrderNumber());
        processTestVOS.stream().forEach(x->{
            x.setProdTaskEqInfoList(JSON.parseArray(x.getDeviceData(), ProdTaskEqInfoVO.class));
            TorchResponse<List<AttachmentVO>> attachmentResponse = attachmentService.selectAttachmentListByWorkOrderAndProcess(
                    x.getWorkOrder(), x.getProcessCode());
            if (attachmentResponse.getStatus() == Constant.REQUEST_SUCCESS && !CollectionUtils.isEmpty(attachmentResponse.getData().getData())) {
                x.setAttachmentVOList(attachmentResponse.getData().getData());
            }
           x.setProductionTaskTestDataList(JSON.parseArray(x.getTestData(), ProductionTaskTestDataVO.class));
            x.setTask(JSON.parseObject(x.getProcessData(), ProductionTaskVO.class));
        });
       vo.setTestVOList(processTestVOS);
       TorchResponse response = new TorchResponse();
       response.setStatus(Constant.REQUEST_SUCCESS);
       response.getData().setData(vo);
       return response;
    }

    @Override
    public TorchResponse saveProductionOrderTestData(ProductionOrderResultDTO productionOrderResultDTO) {
        ProductionOrderResultVO resultVO =  productionOrderResultMapper.selectByWorkOrder(productionOrderResultDTO.getWorkOrder());
        ProductionOrderResult result = new ProductionOrderResult();
        BeanUtils.copyProperties(productionOrderResultDTO, result);
        if(resultVO != null){
            result.setId(resultVO.getId());
            productionOrderResultMapper.updateById(result);
        }else{
            productionOrderResultMapper.insert(result);
        }
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("work_order", productionOrderResultDTO.getWorkOrder());
        List<ProductionOrderProcessTest> tests = productionOrderProcessTestMapper.selectList(wrapper);
        productionOrderResultDTO.getTestList().stream().forEach(x->{
            //保存测试数据
            Optional<ProductionOrderProcessTest> matchingTest = tests.stream()
                    .filter(test ->
                            test.getWorkOrder().equals(x.getTask().getWorkOrderNumber()) &&
                            test.getProcessCode().equals(x.getTask().getProcessCode())).findFirst();
            if(matchingTest.isPresent()){
                ProductionOrderProcessTest testdata = matchingTest.get();
                testdata.setTestData(JSON.toJSONString(x.getProductionTaskTestDataList()));
                testdata.setDeviceData(JSON.toJSONString(x.getProdTaskEqInfoList()));
                testdata.setProcessData(JSON.toJSONString(x.getTask()));
                productionOrderProcessTestMapper.updateById(testdata);
            }
            if(!ObjectUtils.isEmpty(x.getAttachmentList())){
                x.getAttachmentList().stream().forEach(attachment->{
                    attachmentService.saveOrUpdate(attachment);
                });
            }
        });
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }
}
