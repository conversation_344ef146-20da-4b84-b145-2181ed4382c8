package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.io.Serializable;

/**
* @description 溯源信息DTO 实体类
* <AUTHOR>
* @date 2025-07-18
**/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("溯源信息DTO实体类")
public class TraceInformationDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 设备台账id
     */
    @ApiModelProperty("设备台账id")
    private String equipmentInventoryId;

    /**
	 * 设备编号
     **/
    @ApiModelProperty("设备编号")
    private String deviceSerialNumber;
    
    /**
	 * 溯源方式
     **/
    @ApiModelProperty("溯源方式")
    private String d0;
    
    /**
	 * 校准单位
     **/
    @ApiModelProperty("校准单位")
    private String d01;
    
    /**
	 * 溯源日期
     **/
    @ApiModelProperty("溯源日期")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @JsonFormat(pattern = "yyyy-MM-dd")
    private String tracebackDate;
    
    /**
	 * 溯源周期
     **/
    @ApiModelProperty("溯源周期")
    private Long tracebackPeriod;
    
    /**
	 * 溯源有效期
     **/
    @ApiModelProperty("溯源有效期")
    private String tracebackValidityPeriod;
    
    /**
	 * 溯源方案
     **/
    @ApiModelProperty("溯源方案")
    private String tracebackScheme;
    
    /**
	 * 溯源报告
     **/
    @ApiModelProperty("溯源报告")
    private String tracebackReport;

    /**
     * 溯源状态
     */
    @ApiModelProperty("溯源状态")
    private String status;

    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;

}