package com.huatek.frame.modules.business.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description 扫码请求DTO实体类
 * <AUTHOR>
 * @date 2025-08-14
 **/
@Data
@ApiModel("扫码请求DTO实体类")
public class ScanCodeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 扫码枪编号
     **/
    @ApiModelProperty("扫码枪编号")
    private String scannerGunNumber;

    /**
     * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;
    /**
     * 编号
     **/
    @ApiModelProperty("编号")
    private String number;
}
