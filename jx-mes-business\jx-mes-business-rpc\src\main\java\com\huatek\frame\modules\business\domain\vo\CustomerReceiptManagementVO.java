package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 客户收件管理VO实体类
* <AUTHOR>
* @date 2025-07-14
**/
@Data
@ApiModel("客户收件管理DTO实体类")
public class CustomerReceiptManagementVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "*委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;
    
    /**
	 * 收件人姓名
     **/
    @ApiModelProperty("收件人姓名")
    @Excel(name = "*收件人姓名",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipientsName;
    
    /**
	 * 收件人电话
     **/
    @ApiModelProperty("收件人电话")
    @Excel(name = "*收件人电话",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipientsPhoneNumber;
    
    /**
	 * 收件地址
     **/
    @ApiModelProperty("收件地址")
    @Excel(name = "收件地址",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipientsAddress;
    
    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String comment;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updator;

    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String creator;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}