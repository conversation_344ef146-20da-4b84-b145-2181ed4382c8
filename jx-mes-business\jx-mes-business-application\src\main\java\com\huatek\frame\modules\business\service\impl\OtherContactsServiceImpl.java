package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;

import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;


import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.OtherContacts;
import com.huatek.frame.modules.business.domain.vo.OtherContactsVO;
import com.huatek.frame.modules.business.mapper.OtherContactsMapper;
import com.huatek.frame.modules.business.service.OtherContactsService;
import com.huatek.frame.modules.business.service.dto.OtherContactsDTO;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 其他联系人 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-14
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "otherContacts")
//@RefreshScope
@Slf4j
public class OtherContactsServiceImpl implements OtherContactsService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private CustomerInformationManagementMapper customerInformationManagementMapper;

	@Autowired
	private OtherContactsMapper otherContactsMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public OtherContactsServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<OtherContactsVO>> findOtherContactsPage(OtherContactsDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<OtherContactsVO> otherContactss = otherContactsMapper.selectOtherContactsPage(dto);
		TorchResponse<List<OtherContactsVO>> response = new TorchResponse<List<OtherContactsVO>>();
		response.getData().setData(otherContactss);
		response.setStatus(200);
		response.getData().setCount(otherContactss.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(OtherContactsDTO otherContactsDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(otherContactsDto.getCodexTorchDeleted())) {
            otherContactsDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        if (StrUtil.isNotEmpty(otherContactsDto.getCodexTorchMasterFormId())){
            //更新主表的更新时间和更新人
            LambdaUpdateWrapper<CustomerInformationManagement> updateWrapper = Wrappers.lambdaUpdate(CustomerInformationManagement.class)
                    .eq(CustomerInformationManagement::getId, otherContactsDto.getCodexTorchMasterFormId())
                    .set(CustomerInformationManagement::getCodexTorchUpdateDatetime, new Timestamp(System.currentTimeMillis()))
                    .set(CustomerInformationManagement::getCodexTorchUpdater, SecurityContextHolder.getCurrentUserName());
            customerInformationManagementMapper.update(null, updateWrapper);
        }
		String id = otherContactsDto.getId();
		OtherContacts entity = new OtherContacts();
        BeanUtils.copyProperties(otherContactsDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            try {
                otherContactsMapper.insert(entity);
            }catch (DuplicateKeyException ex){
                throw new ServiceException(String.format("当前客户信息关联的名称为 %s 的其他联系人已存在，不允许存在相同名称和电话的其他联系人", entity.getName()));
            }

		} else {
			otherContactsMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        OtherContactsVO vo = new OtherContactsVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<OtherContactsVO> findOtherContacts(String id) {
		OtherContactsVO vo = new OtherContactsVO();
		if (!HuatekTools.isEmpty(id)) {
			OtherContacts entity = otherContactsMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<OtherContactsVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		otherContactsMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "other_contacts", convertorFields = "")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<OtherContactsVO> selectOtherContactsList(OtherContactsDTO dto) {
        return otherContactsMapper.selectOtherContactsList(dto);
    }

    /**
     * 导入其他联系人数据
     *
     * @param otherContactsList 其他联系人数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "other_contacts", convertorFields = "")
    public TorchResponse importOtherContacts(List<OtherContactsVO> otherContactsList, List<String> unionColumns, Boolean isUpdateSupport, String operName, String codexTorchMasterFormId) {
        if (StringUtils.isNull(otherContactsList) || otherContactsList.size() == 0) {
            throw new ServiceException("导入其他联系人数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        CustomerInformationManagement customerInformationManagement = customerInformationManagementMapper.selectById(codexTorchMasterFormId);
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int index = 0; //记录导入行号
        for (OtherContactsVO vo : otherContactsList) {
            index++;
            if (customerInformationManagement != null){
                vo.setCustomerId0(customerInformationManagement.getCustomerId0());
            }
            try {
                OtherContacts otherContacts = new OtherContacts();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg, index)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, otherContacts);
                otherContacts.setCodexTorchMasterFormId(codexTorchMasterFormId);
                QueryWrapper<OtherContacts> wrapper = new QueryWrapper();
                OtherContacts oldOtherContacts = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = OtherContactsVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<OtherContacts> oldOtherContactsList = otherContactsMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldOtherContactsList) && oldOtherContactsList.size() > 1) {
                        otherContactsMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldOtherContactsList) && oldOtherContactsList.size() == 1) {
                        oldOtherContacts = oldOtherContactsList.get(0);
                    }
                }
                if (StringUtils.isNull(oldOtherContacts)) {
                    BeanValidators.validateWithException(validator, vo);
                    try {
                        otherContactsMapper.insert(otherContacts);
                    }catch (DuplicateKeyException ex){
                        throw new ServiceException(String.format("<br/> 第%d条数据导入失败，当前客户信息关联的名称为 %s 的其他联系人已存在，不允许存在相同名称和电话的其他联系人", index, otherContacts.getName()));
                    }

                    successNum++;
                    successMsg.append("<br/>" + successNum + "、客户编号 " + vo.getCustomerId0() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldOtherContacts, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    otherContactsMapper.updateById(oldOtherContacts);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、客户编号 " + vo.getCustomerId0() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、客户编号 " + vo.getCustomerId0() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + String.format("、第 %d 行数据导入失败, 请仔细检查所导入的数据内容：" +
                        "确保当前已存在的其他联系人和导入数据的姓名和电话不重复，并且字段长度不要过长。", index);
                failureMsg.append(msg);
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(OtherContactsVO vo, int failureNum, StringBuilder failureMsg, int index) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(StrUtil.trim(vo.getName()))) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>姓名不能为空!");
        }
        if (HuatekTools.isEmpty(StrUtil.trim(vo.getUserMobile()))) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>电话不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append(String.format("第 %d 行数据导入失败，失败原因为", index) + "数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectOtherContactsListByIds(List<String> ids) {
        List<OtherContactsVO> otherContactsList = otherContactsMapper.selectOtherContactsListByIds(ids);

		TorchResponse<List<OtherContactsVO>> response = new TorchResponse<List<OtherContactsVO>>();
		response.getData().setData(otherContactsList);
		response.setStatus(200);
		response.getData().setCount((long)otherContactsList.size());
		return response;
    }

    @Override
    public TorchResponse getOtherContactsListByCustomerId(String customerId) {
        List<OtherContactsVO> otherContactsList = otherContactsMapper.getOtherContactsListByCustomerId(customerId);

        TorchResponse<List<OtherContactsVO>> response = new TorchResponse<List<OtherContactsVO>>();
        response.getData().setData(otherContactsList);
        response.setStatus(200);
        response.getData().setCount((long)otherContactsList.size());
        return response;

    }


}
