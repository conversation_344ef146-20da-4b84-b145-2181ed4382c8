package com.huatek.frame.modules.business.service.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ConfirmOutboundDTO {
    @ApiModelProperty("出库的产品ID集合")
    private List<String> ids;

    @ApiModelProperty("状态：2=已出库")
    private String status;

    @ApiModelProperty("发货公司")
    private String expressCompany;

    @ApiModelProperty("快递单号")
    private String trackingNumber;

    @ApiModelProperty("发货日期")
    private String shipDate;

    @ApiModelProperty("收件人姓名")
    private String customerName;        // 收件人姓名

    @ApiModelProperty("收件人电话")
    private String phone; // 收件人电话

    @ApiModelProperty("收件人地址")
    private String address;     // 收件人地址

    @ApiModelProperty("收件人地址信息")
    private String customerInfo;     // 收件人地址信息
    @ApiModelProperty("备注")
    private String remark;

}

