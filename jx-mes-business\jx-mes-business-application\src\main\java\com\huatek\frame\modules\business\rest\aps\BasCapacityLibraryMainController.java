package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 标准产能模型库管理
 */

@Api(tags = "标准产能模型库管理")
@RestController
@RequestMapping("/api/capacitylibrary")
public class BasCapacityLibraryMainController {

    @Autowired
    private HttpClientUtil httpClientUtil;

    /**
     * 获取标准产能模型库分页信息
     */
    @Log("获取标准产能模型库分页信息")
    @ApiOperation(value = "获取标准产能模型库分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/page");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取所有标准产能模型库信息
     */
    @Log("获取所有标准产能模型库信息")
    @ApiOperation(value = "获取所有标准产能模型库信息")
    @PostMapping(value = "/findAll", produces = { "application/json;charset=utf-8" })
    public Object GetAllAsync(HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/getall");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增标准产能模型库
     */
    @Log("新增标准产能模型库")
    @ApiOperation(value = "新增标准产能模型库")
    @TorchPerm("standardCapacityModelLibrary:add")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object GetAllAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/add");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增标准产能模型库
     */
    @Log("更新标准产能模型库")
    @ApiOperation(value = "更新标准产能模型库")
    @PostMapping(value = "/edit", produces = { "application/json;charset=utf-8" })
    public Object UpdateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/edit");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 删除标准产能模型库
     */
    @Log("删除标准产能模型库")
    @ApiOperation(value = "删除标准产能模型库")
    @TorchPerm("standardCapacityManagement:delete")
    @PostMapping(value = "/batchdelete", produces = { "application/json;charset=utf-8" })
    public Object DeleteAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/batchdelete");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * actualName工艺流程
     */
    @Log("根据id获取标准产能模型库工艺流程")
    @ApiOperation(value = "根据id获取标准产能模型库工艺流程")
    @GetMapping(value = "/stepslist/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetStepsListAsyn(@PathVariable(value = "id") String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/stepslist/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }


//    /**
//     * 根据id获取标准产能模型库
//     */
//    @Log("根据id获取标准产能模型库")
//    @ApiOperation(value = "根据id获取标准产能模型库")
//    @GetMapping(value = "/{id}", produces = { "application/json;charset=utf-8" })
//    public Object GetAsyn(@PathVariable(value = "id") String id, HttpServletRequest request){
//        InputParamDto inputParamDto = new InputParamDto();
//        inputParamDto.setParam("");
//        inputParamDto.setHttpMethod("GET");
//        inputParamDto.setServiceUrl("aps/api/capacitylibrary" + id);
//        return httpClientUtil.callMes(request, inputParamDto);
//    }


    /**
     * 删除标准产能模型库工序
     */
    @Log("删除标准产能模型库工序")
    @ApiOperation(value = "删除标准产能模型库工序")
    @TorchPerm("standardCapacityModelLibrary:delete")
    @PostMapping(value = "/deletestep/{id}", produces = { "application/json;charset=utf-8" })
    public Object DeleteStepAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/deletestep/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 选择标准工序方案
     */
    @Log("选择标准工序方案")
    @ApiOperation(value = "选择标准工序方案")
    @PostMapping(value = "/choosestep", produces = { "application/json;charset=utf-8" })
    public Object DeleteStepAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/choosestep");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * actualName
     */
    @Log("actualName")
    @ApiOperation(value = "获取工序方案主资源")
    @GetMapping(value = "/mainresource/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetMainSourceAsync(@PathVariable(value = "id") String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/mainresource/"+ id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 保存工序方案主资源
     */
    @Log("保存工序方案主资源")
    @ApiOperation(value = "保存工序方案主资源")
    @PostMapping(value = "/mainresource/save", produces = { "application/json;charset=utf-8" })
    public Object SaveMainSourceAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/mainresource/save");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * actualName
     */
    @Log("获取工序方案辅助资源")
    @ApiOperation(value = "获取工序方案辅助资源")
    @GetMapping(value = "/assistantresource/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetAssistantSourceAsync(@PathVariable(value = "id") String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/assistantresource/"+ id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 保存工序方案辅助资源
     */
    @Log("保存工序方案辅助资源")
    @ApiOperation(value = "保存工序方案辅助资源")
    @PostMapping(value = "/assistantresource/save", produces = { "application/json;charset=utf-8" })
    public Object SaveAssistantSourceAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/assistantresource/save");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 根据ID获取标准产能模型库
     */
    @Log("根据ID获取标准产能模型库")
    @ApiOperation(value = "根据ID获取标准产能模型库")
    @GetMapping(value = "/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetAsync(@PathVariable(value = "id")String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/capacitylibrary/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }









}
