<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.CardinventoryMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.dut_board_name as dutBoardName,
		t.dut_board_number as dutBoardNumber,
		t.dut_board_type as dutBoardType,
		t.board_capacity as boardCapacity,
		t.storage_location as storageLocation,
        su.user_name as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectCardinventoryPage" parameterType="com.huatek.frame.modules.business.service.dto.CardinventoryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CardinventoryVO">
		select
		<include refid="Base_Column_List" />
			from cardinventory t
        left join sys_user su on t.codex_torch_creator_id = su.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="dutBoardName != null and dutBoardName != ''">
                    and t.dut_board_name  like concat('%', #{dutBoardName} ,'%')
                </if>
                <if test="dutBoardNumber != null and dutBoardNumber != ''">
                    and t.dut_board_number  like concat('%', #{dutBoardNumber} ,'%')
                </if>
                <if test="dutBoardType != null and dutBoardType != ''">
                    and t.dut_board_type  = #{dutBoardType}
                </if>
                <if test="boardCapacity != null and boardCapacity != ''">
                    and t.board_capacity  like concat('%', #{boardCapacity} ,'%')
                </if>
                <if test="storageLocation != null and storageLocation != ''">
                    and t.storage_location  like concat('%', #{storageLocation} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        order by  t.codex_torch_create_datetime desc
	</select>

    <select id="selectCardinventoryList" parameterType="com.huatek.frame.modules.business.service.dto.CardinventoryDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CardinventoryVO">
		select
		<include refid="Base_Column_List" />
			from cardinventory t
        left join sys_user su on t.codex_torch_creator_id = su.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="dutBoardName != null and dutBoardName != ''">
                    and t.dut_board_name  like concat('%', #{dutBoardName} ,'%')
                </if>
                <if test="dutBoardNumber != null and dutBoardNumber != ''">
                    and t.dut_board_number  like concat('%', #{dutBoardNumber} ,'%')
                </if>
                <if test="dutBoardType != null and dutBoardType != ''">
                    and t.dut_board_type  = #{dutBoardType}
                </if>
                <if test="boardCapacity != null and boardCapacity != ''">
                    and t.board_capacity  like concat('%', #{boardCapacity} ,'%')
                </if>
                <if test="storageLocation != null and storageLocation != ''">
                    and t.storage_location  like concat('%', #{storageLocation} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectCardinventoryListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.CardinventoryVO">
		select
		<include refid="Base_Column_List" />
			from cardinventory t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>