package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.vo.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
* @description 工单工序试验数据
* <AUTHOR>
* @date 2025-07-30
**/
@Data
@ApiModel("工单工序试验数据DTO实体类")
public class ProductionOrderProcessTestDTO implements Serializable {


    @ApiModelProperty("主键ID")
    private String id;

    
    /**
	 * 工单
     **/
    @ApiModelProperty("工单")
    private String workOrder;

    
    /**
	 * 工序
     **/
    @ApiModelProperty("工序")
    private String processCode;

    /**
     * 工序报工内容
     **/
    @ApiModelProperty("工序报工内容")
    private String processData;

    /**
     * 设备数据
     **/
    @ApiModelProperty("设备数据")
    private String deviceData;
    /**
	 * 试验结果
     **/
    @ApiModelProperty("试验结果")
    private String testData;

    /**
     * 任务id
     **/
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 执行顺序
     **/
    @ApiModelProperty("执行顺序")
    private Integer executionSequence;

    @ApiModelProperty("报工数据")
    private ProductionTaskDTO task;
    @ApiModelProperty("试验数据")
    private List<ProductionTaskTestDataDTO> productionTaskTestDataList;

    @ApiModelProperty("设备信息")
    private List<ProdTaskEqInfoDTO> prodTaskEqInfoList;

    @ApiModelProperty("附件信息")
    private List<ProductionTaskAttachmentsDTO> prodTaskAttachmentList;

    @ApiModelProperty("设备原始数据")
    private List<AttachmentDTO> attachmentList;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;
}