package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.WorkReportTable;
import com.huatek.frame.modules.business.domain.vo.WorkReportTableVO;
import com.huatek.frame.modules.business.mapper.WorkReportTableMapper;
import com.huatek.frame.modules.business.service.ProductionValueCalculationService;
import com.huatek.frame.modules.business.service.WorkReportTableService;
import com.huatek.frame.modules.business.service.dto.WorkReportTableDTO;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import java.sql.Date;
import org.springframework.util.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.mapper.*;
import java.util.stream.Collectors;
import java.util.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import com.huatek.frame.modules.constant.DicConstant;



/**
 * 工时报表 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-18
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "workReportTable")
//@RefreshScope
@Slf4j
public class WorkReportTableServiceImpl implements WorkReportTableService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private WorkReportTableMapper workReportTableMapper;

	@Autowired
	private ProductCategoryMapper productCategoryMapper;


    @Autowired
    protected Validator validator;

    @Autowired
    private ProductionTaskMapper productionTaskMapper;
    
    @Autowired
    private ProdTaskEqInfoMapper prodTaskEqInfoMapper;
    
    @Autowired
    private EquipmentInventoryMapper equipmentInventoryMapper;
    
    @Autowired
    private ProdTaskOpHistMapper prodTaskOpHistMapper;
    
    @Autowired
    private CapabilityAssetMapper capabilityAssetMapper;

	@Autowired
	private ProductionValueCalculationService productionValueCalculationService;

	@Autowired
	private CustomerInformationManagementMapper customerInformationManagementMapper;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public WorkReportTableServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<WorkReportTableVO>> findWorkReportTablePage(WorkReportTableDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<WorkReportTableVO> workReportTables = workReportTableMapper.selectWorkReportTablePage(dto);
		TorchResponse<List<WorkReportTableVO>> response = new TorchResponse<List<WorkReportTableVO>>();
		response.getData().setData(workReportTables);
		response.setStatus(200);
		response.getData().setCount(workReportTables.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(WorkReportTableDTO workReportTableDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(workReportTableDto.getCodexTorchDeleted())) {
            workReportTableDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = workReportTableDto.getId();
		WorkReportTable entity = new WorkReportTable();
        BeanUtils.copyProperties(workReportTableDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			workReportTableMapper.insert(entity);
		} else {
			workReportTableMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        WorkReportTableVO vo = new WorkReportTableVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<WorkReportTableVO> findWorkReportTable(String id) {
		WorkReportTableVO vo = new WorkReportTableVO();
		if (!HuatekTools.isEmpty(id)) {
			WorkReportTable entity = workReportTableMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<WorkReportTableVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<WorkReportTable> workReportTableList = workReportTableMapper.selectBatchIds(Arrays.asList(ids));
        for (WorkReportTable workReportTable : workReportTableList) {
            workReportTable.setCodexTorchDeleted(Constant.DEFAULT_YES);
            workReportTableMapper.updateById(workReportTable);
        }
		//workReportTableMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
	@ExcelExportConversion(tableName = "work_report_table", convertorFields = "yes_or_no#durationOfTheSameProcess,yes_or_no#wipaia,yes_or_no#whetherOutsourcedProcess6,experiment_project_testMethodology#testMethodology")

	@DataScope(groupAlias = "t", userAlias = "t")
    public List<WorkReportTableVO> selectWorkReportTableList(WorkReportTableDTO dto) {
        return workReportTableMapper.selectWorkReportTableList(dto);
    }

    /**
     * 导入工时报表数据
     *
     * @param workReportTableList 工时报表数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "work_report_table", convertorFields = "")
    public TorchResponse importWorkReportTable(List<WorkReportTableVO> workReportTableList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(workReportTableList) || workReportTableList.size() == 0) {
            throw new ServiceException("导入工时报表数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (WorkReportTableVO vo : workReportTableList) {
            try {
                WorkReportTable workReportTable = new WorkReportTable();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, workReportTable);
                QueryWrapper<WorkReportTable> wrapper = new QueryWrapper();
                WorkReportTable oldWorkReportTable = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = WorkReportTableVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<WorkReportTable> oldWorkReportTableList = workReportTableMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldWorkReportTableList) && oldWorkReportTableList.size() > 1) {
                        workReportTableMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldWorkReportTableList) && oldWorkReportTableList.size() == 1) {
                        oldWorkReportTable = oldWorkReportTableList.get(0);
                    }
                }
                if (StringUtils.isNull(oldWorkReportTable)) {
                    BeanValidators.validateWithException(validator, vo);
                    workReportTableMapper.insert(workReportTable);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工单编号 " + vo.getWorkOrderNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldWorkReportTable, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    workReportTableMapper.updateById(oldWorkReportTable);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工单编号 " + vo.getWorkOrderNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、工单编号 " + vo.getWorkOrderNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工单编号 " + vo.getWorkOrderNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(WorkReportTableVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectWorkReportTableListByIds(List<String> ids) {
        List<WorkReportTableVO> workReportTableList = workReportTableMapper.selectWorkReportTableListByIds(ids);

		TorchResponse<List<WorkReportTableVO>> response = new TorchResponse<List<WorkReportTableVO>>();
		response.getData().setData(workReportTableList);
		response.setStatus(200);
		response.getData().setCount((long)workReportTableList.size());
		return response;
    }


	@Override
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse generateWorkReportTable() {
		// 创建异步任务
		Runnable task = new Runnable() {
			@Override
			public void run() {
				// 查询未生成报表且状态为已完成的生产任务
				List<ProductionTask> taskList = productionTaskMapper.selectList(new QueryWrapper<ProductionTask>()
						.eq("generate_report", DicConstant.CommonDic.DEFAULT_ZERO)
						.eq("status", DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG)
						.eq("codex_torch_deleted", Constant.DEFAULT_NO)
				);

				if (CollectionUtils.isEmpty(taskList)) {
					log.info("无可生成的数据");
					return;
				}

				String currentUserId = SecurityContextHolder.getCurrentUserId();
				String currentGroupId = SecurityContextHolder.getCurrentUserGroupId();
				Timestamp now = new Timestamp(System.currentTimeMillis());

				for (ProductionTask task : taskList) {
					try {
						// 查询设备子表
						List<ProdTaskEqInfo> eqInfos = prodTaskEqInfoMapper.selectList(new QueryWrapper<ProdTaskEqInfo>()
								.eq("task_number", task.getTaskNumber())
								.eq("codex_torch_deleted", Constant.DEFAULT_NO)
						);

						if (CollectionUtils.isEmpty(eqInfos)) {
							// 按任务维度生成一条
							WorkReportTable report = buildWorkReportFromTaskAndEq(task, null);
							report.setCodexTorchCreatorId(currentUserId);
							report.setCodexTorchGroupId(currentGroupId);
							report.setCodexTorchCreateDatetime(now);
							report.setCodexTorchUpdateDatetime(now);
							report.setCodexTorchDeleted(Constant.DEFAULT_NO);
							workReportTableMapper.insert(report);
						} else {
							for (ProdTaskEqInfo eqInfo : eqInfos) {
								WorkReportTable report = buildWorkReportFromTaskAndEq(task, eqInfo);
								report.setCodexTorchCreatorId(currentUserId);
								report.setCodexTorchGroupId(currentGroupId);
								report.setCodexTorchCreateDatetime(now);
								report.setCodexTorchUpdateDatetime(now);
								report.setCodexTorchDeleted(Constant.DEFAULT_NO);
								workReportTableMapper.insert(report);
							}
						}

						// 标记任务已生成报表
						task.setGenerateReport(DicConstant.CommonDic.DEFAULT_ONE);
						task.setCodexTorchUpdateDatetime(now);
						productionTaskMapper.updateById(task);
					} catch (Exception e) {
						log.error("生成工作报表失败，任务编号: {}", task.getTaskNumber(), e);
					}
				}
				log.info("工作报表生成完成，共处理 {} 个任务", taskList.size());
			}
		};

		// 启动线程执行任务
		Thread thread = new Thread(task);
		thread.start();

		// 立即返回响应
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.setMessage("工作报表生成任务已启动，正在后台处理");
		return response;
	}


	private WorkReportTable buildWorkReportFromTaskAndEq(ProductionTask task, ProdTaskEqInfo eqInfo) {
		WorkReportTable report = new WorkReportTable();

		// 基础字段（来自生产任务）
		report.setWorkOrderNumber(task.getWorkOrderNumber());
		report.setInspectionQuantity2(task.getInspectionQuantity2() == null ? null : task.getInspectionQuantity2().longValue());
		report.setStandardProcessName(task.getProcessName2());
		report.setCustomerTestName(task.getCustomerProcessName());
		report.setProductName(task.getProductName());
		report.setProductModel(task.getProductModel());
		report.setManufacturer(task.getManufacturer());
		report.setBatchNumber(task.getBatchNumber());

		//根据分类id查询分类
		ProductCategory productCategory = productCategoryMapper.selectById(task.getProductCategory());
		if(productCategory != null) {
			report.setProductCategory(productCategory.getCategoryName());
		}else{
			report.setProductCategory("");
		}

		report.setProductInformation1(task.getProductInformation1());
		//委托单位是id需要拿到名称
		CustomerInformationManagement customer = customerInformationManagementMapper.selectById(task.getEntrustedUnit());
		if(customer != null) {
			report.setEntrustedUnit(customer.getEntrustedUnit());
		}else{
			report.setEntrustedUnit(task.getEntrustedUnit());
		}

		report.setTestMethodology(task.getTestMethodology());
		report.setProcessStartTime(task.getActualStartTime());
		report.setProcessEndTime(task.getActualEndTime());
		report.setReporter4(task.getReporter4());
		report.setOrderNumber(task.getOrderNumber());

		// 研发任务编号
		report.setRdTaskNumber(resolveRdTaskNumbers(task.getTechnicalCompetencyNumber()));

		// 是否参与核算（首次使用显示是 -> '1'，否则 '0'）
		report.setWipaia(determineFirstUseFlag(task));

		// 是否外协工序
		String isOutsourced = DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING.equals(task.getTestMethodology())
				? DicConstant.CommonDic.DEFAULT_ONE : DicConstant.CommonDic.DEFAULT_ZERO;
		report.setWhetherOutsourcedProcess6(isOutsourced);

		// 设备维度字段
		if (eqInfo != null) {
			report.setDeviceSerialNumber(eqInfo.getDeviceSerialNumber());
			report.setDurationOfTheSameProcess(eqInfo.getDurationOfTheSameProcess());

			// 设备名称、固定资产编码、能耗
			EquipmentInventory equipment = null;
			if (!HuatekTools.isEmpty(eqInfo.getDeviceSerialNumber())) {
				equipment = equipmentInventoryMapper.selectOne(new QueryWrapper<EquipmentInventory>()
						.eq("device_serial_number", eqInfo.getDeviceSerialNumber())
						.eq("CODEX_TORCH_DELETED", Constant.DEFAULT_NO)
				);
			}
			if (equipment != null) {
				report.setDeviceName(equipment.getDeviceName());
				report.setFixedAssetCoding(equipment.getAssetNumber());
				report.setEquipmentEnergyConsumption(equipment.getDeviceEnergyConsumption() == null ? null : equipment.getDeviceEnergyConsumption().longValue());
			}

			// 设备开始/结束时间与用时（小时）
			Timestamp eqStart = eqInfo.getStartTime5();
			Timestamp eqEnd = eqInfo.getEndTime();
			if (eqStart != null) {
				report.setEquipmentEndTime(eqStart);
			}
			if (eqEnd != null) {
				report.setEquipmentEndTime27(eqEnd);
			}
			if (eqStart != null && eqEnd != null && eqEnd.after(eqStart)) {
				BigDecimal hours = millisToHours(eqEnd.getTime() - eqStart.getTime());
				report.setEquipmentRunningTimeh(hours);
			}

			// 设备功耗 = 设备能耗 * 设备用时
			if (report.getEquipmentEnergyConsumption() != null && report.getEquipmentRunningTimeh() != null) {
				BigDecimal power = BigDecimal.valueOf(report.getEquipmentEnergyConsumption())
						.multiply(report.getEquipmentRunningTimeh());
				report.setEquipmentPowerConsumption(power.setScale(0, RoundingMode.HALF_UP).longValue());
			}

			if(eqInfo!=null){
				// 设备共用工单
				report.setSharedEquipmentWorkOrder(findSharedWorkOrders(task, eqInfo));
				//给之前的数据补充设备共用工单

				updateSharedEquipmentWorkOrder(task, eqInfo);

			}

			// 合同编号 根据工单编号从核算表中拿 已对账+客户标准
			report.setContractNumber(findContractNumber(task.getWorkOrderNumber()));
		}

		// 工序时长（小时，扣除暂停时长）
		report.setProcessDuration(calculateProcessDurationHours(task));

		return report;
	}

	/**
	 * 根据当前的
	 * 设备编号
	 * 设备开始时间
	 * 设备结束时间
	 * 去查历史数据
	 * 查到了判断当前工单是否存在设备共用工单中不存在则添加进去
	 */
	private void updateSharedEquipmentWorkOrder(ProductionTask task, ProdTaskEqInfo eqInfo) {
		if (eqInfo == null) {
			return;
		}
		if (HuatekTools.isEmpty(eqInfo.getDeviceSerialNumber())) {
			return;
		}
		if (eqInfo.getStartTime5() == null) {
			return;
		}
		if (eqInfo.getEndTime() == null) {
			return;
		}
		List<WorkReportTable> workReportTables = workReportTableMapper.selectList(new QueryWrapper<WorkReportTable>()
				.eq("device_serial_number", eqInfo.getDeviceSerialNumber())
				.le("equipment_end_time", eqInfo.getEndTime())
				.ge("equipment_end_time27", eqInfo.getStartTime5())
				.eq("codex_torch_deleted", Constant.DEFAULT_NO)
		);
		if (CollectionUtils.isEmpty(workReportTables)) {
			return;
		}
		for (WorkReportTable workReportTable : workReportTables) {
			if (HuatekTools.isEmpty(workReportTable.getSharedEquipmentWorkOrder())) {
				log.info("工单{}之前设备共用工单为空，追加后为{}", workReportTable.getWorkOrderNumber(), task.getWorkOrderNumber());
				workReportTable.setSharedEquipmentWorkOrder(task.getWorkOrderNumber());
				workReportTableMapper.updateById(workReportTable);
			} else {
				if (!workReportTable.getSharedEquipmentWorkOrder().contains(task.getWorkOrderNumber())) {
					String newSharedEquipmentWorkOrder= workReportTable.getSharedEquipmentWorkOrder() + "," + task.getWorkOrderNumber();
					log.info("工单{}之前设备共用工单为{}，追加后为{}", workReportTable.getWorkOrderNumber(), workReportTable.getSharedEquipmentWorkOrder(),newSharedEquipmentWorkOrder);
					workReportTable.setSharedEquipmentWorkOrder(newSharedEquipmentWorkOrder);
					workReportTableMapper.updateById(workReportTable);
				}
			}
		}

	}

	private String findContractNumber(String workOrderNumber) {
		return productionValueCalculationService.findContractNumber(workOrderNumber);
	}

	private BigDecimal calculateProcessDurationHours(ProductionTask task) {
		Timestamp start = task.getActualStartTime();
		Timestamp end = task.getActualEndTime();
		if (start == null || end == null || !end.after(start)) {
			return null;
		}

		long totalMillis = end.getTime() - start.getTime();

		// 统计暂停时长（按操作历史的创建时间配对 暂停->恢复）
		List<ProdTaskOpHist> histories = prodTaskOpHistMapper.selectList(new QueryWrapper<ProdTaskOpHist>()
				.eq("task_number", task.getTaskNumber())
				.eq("codex_torch_deleted", Constant.DEFAULT_NO)
				.orderByAsc("codex_torch_create_datetime")
		);

		Long pauseStart = null;
		long pausedMillis = 0L;
		if (!CollectionUtils.isEmpty(histories)) {
			for (ProdTaskOpHist hist : histories) {
				String op = hist.getOperationType();
				Timestamp opTime = hist.getCodexTorchCreateDatetime();
				if (opTime == null) {
					continue;
				}
				if (DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE.equals(op)) {
					pauseStart = opTime.getTime();
				} else if (DicConstant.ProductionOrder.OPERATION_TYPE_RESUME.equals(op)) {
					if (pauseStart != null) {
						long resumeTime = opTime.getTime();
						if (resumeTime > pauseStart) {
							pausedMillis += (resumeTime - pauseStart);
						}
						pauseStart = null;
					}
				}
			}
			if (pauseStart != null) {
				pausedMillis += Math.max(0, end.getTime() - pauseStart);
			}
		}

		long effectiveMillis = Math.max(0, totalMillis - pausedMillis);
		return millisToHours(effectiveMillis);
	}

	private BigDecimal millisToHours(long millis) {
		return BigDecimal.valueOf(millis)
				.divide(BigDecimal.valueOf(3600000L), 2, RoundingMode.HALF_UP);
	}

	private String resolveRdTaskNumbers(String technicalCompetencyNumber) {
		if (HuatekTools.isEmpty(technicalCompetencyNumber)) {
			return null;
		}
		List<CapabilityAsset> assets = capabilityAssetMapper.selectList(new QueryWrapper<CapabilityAsset>()
				.eq("capability_number", technicalCompetencyNumber)
				.eq("codex_torch_deleted", Constant.DEFAULT_NO)
		);
		if (CollectionUtils.isEmpty(assets)) {
			return null;
		}
		return assets.stream()
				.map(CapabilityAsset::getTaskNumber)
				.filter(Objects::nonNull)
				.distinct()
				.collect(Collectors.joining(","));
	}

	private String determineFirstUseFlag(ProductionTask task) {
		String tcn = task.getTechnicalCompetencyNumber();
		if (HuatekTools.isEmpty(tcn)) {
			return DicConstant.CommonDic.DEFAULT_ZERO;
		}
        Long count = productionTaskMapper.selectCount(new QueryWrapper<ProductionTask>()
                .ne("id", task.getId())
                .like("technical_competency_number", tcn)
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        if (count != null && count > 0) {
            return DicConstant.CommonDic.DEFAULT_ZERO;
        }
		return DicConstant.CommonDic.DEFAULT_ONE;
	}

	//查询设备共用工单，ProdTaskEqInfo中的开始时间和结束时间 需要找与当前设备时间交叉的都算
	private String findSharedWorkOrders(ProductionTask currentTask, ProdTaskEqInfo eqInfo) {
		if (eqInfo==null || currentTask==null) {
			return null;
		}

		//目前没有包含把这个时间包含在内的

		List<ProdTaskEqInfo> sameDeviceEqList = prodTaskEqInfoMapper.selectList(new QueryWrapper<ProdTaskEqInfo>()
				.eq("device_serial_number", eqInfo.getDeviceSerialNumber())
				.eq("codex_torch_deleted", Constant.DEFAULT_NO)
				.ne("task_number", currentTask.getTaskNumber())
				.le("start_time5", eqInfo.getEndTime())
				.ge("end_time", eqInfo.getStartTime5())


		);
		if (CollectionUtils.isEmpty(sameDeviceEqList)) {
			return null;
		}
		List<String> taskNums = sameDeviceEqList.stream()
				.map(ProdTaskEqInfo::getTaskNumber)
				.filter(Objects::nonNull)
				.distinct()
				.collect(Collectors.toList());
		if (CollectionUtils.isEmpty(taskNums)) {
			return null;
		}
		//根据taskNums在ProductionTask中查询工单编号
		List<String> workOrderNumbers = productionTaskMapper.selectList(new QueryWrapper<ProductionTask>()
				.in("task_number", taskNums)
				.eq("status", DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG)
				.eq("codex_torch_deleted", Constant.DEFAULT_NO)
		).stream()
				.map(ProductionTask::getWorkOrderNumber)
				.filter(Objects::nonNull)
				.distinct()
				.collect(Collectors.toList());



		if (CollectionUtils.isEmpty(workOrderNumbers)) {
			return null;
		}
		return String.join(",", workOrderNumbers);
	}
}
