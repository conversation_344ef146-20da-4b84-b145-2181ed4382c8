# 生产任务与APS排产计划集成功能说明

## 功能概述

本功能实现了生产任务（production_task）与APS排产计划步骤（aps_schedule_plan_step）的数据同步，确保生产任务状态变更时能够自动更新对应的APS排产计划信息。

## 实现的功能

### 1. 生产任务开始时更新APS排产计划步骤

**触发条件：** 生产任务状态从"未开始"变更为"进行中"时

**更新内容：**
- 更新 `aps_schedule_plan_step.actualbegintime` = `production_task.actual_start_time`
- 更新 `aps_schedule_plan_step.state` = '2'

**实现位置：** `ProductionTaskServiceImpl.startTask()` 方法

### 2. 生产任务设置完成时间时更新APS排产计划步骤

**触发条件：** 生产任务的 `completion_time6` 字段被设置时

**更新内容：**
- 更新 `aps_schedule_plan_step.actualendtime` = `production_task.actual_end_time`

**实现位置：** `ProductionTaskServiceImpl.saveOrUpdate()` 方法

### 3. 生产任务完成时更新APS排产计划步骤状态

**触发条件：** 生产任务状态变更为"完成"时

**更新内容：**
- 更新 `aps_schedule_plan_step.state` = '3'

**实现位置：** 
- `ProductionTaskServiceImpl.completeTask()` 方法（无需审批直接完成）
- `ProductionTaskServiceImpl.approveTask()` 方法（审批通过后完成）

## 状态映射关系

根据代码注释中的说明：

```
production_task的状态 -> aps_schedule_plan_step的状态
1 (进行中) -> 2
6 (完成) -> 3
```

## 技术实现

### 1. 数据库层面

在 `ProductionTaskMapper.xml` 中添加了三个SQL更新语句：

```xml
<!-- 生产任务开始时更新APS排产计划步骤的实际开始时间和状态 -->
<update id="updateApsSchedulePlanStepOnTaskStart">
    UPDATE aps_schedule_plan_step 
    SET actualbegintime = #{actualStartTime}, 
        state = '2'
    WHERE id = #{apsSchedulePlanStepId}
    AND #{apsSchedulePlanStepId} IS NOT NULL
</update>

<!-- 生产任务设置完成时间时更新APS排产计划步骤的实际结束时间 -->
<update id="updateApsSchedulePlanStepActualEndTime">
    UPDATE aps_schedule_plan_step 
    SET actualendtime = #{actualEndTime}
    WHERE id = #{apsSchedulePlanStepId}
    AND #{apsSchedulePlanStepId} IS NOT NULL
    AND #{actualEndTime} IS NOT NULL
</update>

<!-- 生产任务完成时更新APS排产计划步骤的状态 -->
<update id="updateApsSchedulePlanStepOnTaskComplete">
    UPDATE aps_schedule_plan_step 
    SET state = '3'
    WHERE id = #{apsSchedulePlanStepId}
    AND #{apsSchedulePlanStepId} IS NOT NULL
</update>
```

### 2. Mapper接口层面

在 `ProductionTaskMapper.java` 中添加了对应的方法声明：

```java
/**
 * 生产任务开始时更新APS排产计划步骤的实际开始时间和状态
 */
int updateApsSchedulePlanStepOnTaskStart(@Param("apsSchedulePlanStepId") String apsSchedulePlanStepId,
                                       @Param("actualStartTime") java.sql.Timestamp actualStartTime);

/**
 * 生产任务设置完成时间时更新APS排产计划步骤的实际结束时间
 */
int updateApsSchedulePlanStepActualEndTime(@Param("apsSchedulePlanStepId") String apsSchedulePlanStepId,
                                         @Param("actualEndTime") java.sql.Timestamp actualEndTime);

/**
 * 生产任务完成时更新APS排产计划步骤的状态
 */
int updateApsSchedulePlanStepOnTaskComplete(@Param("apsSchedulePlanStepId") String apsSchedulePlanStepId);
```

### 3. 服务层面

在 `ProductionTaskServiceImpl.java` 中的关键位置调用这些方法：

#### 任务开始时（startTask方法）：
```java
// 1.生产任务开始的时候更新aps_schedule_plan_step中的actualbegintime为production_task的actual_start_time，state为2
if (!HuatekTools.isEmpty(productionTask.getApsSchedulePlanStepId())) {
    productionTaskMapper.updateApsSchedulePlanStepOnTaskStart(
            productionTask.getApsSchedulePlanStepId(), 
            productionTask.getActualStartTime());
}
```

#### 设置完成时间时（saveOrUpdate方法）：
```java
if (null != entity.getCompletionTime6()) {
    entity.setActualEndTime(new Timestamp(entity.getCompletionTime6().getTime()));
    // 2.生产任务设置完成时间时增加更新aps_schedule_plan_step中的actualendtime
    if (!HuatekTools.isEmpty(entity.getApsSchedulePlanStepId())) {
        productionTaskMapper.updateApsSchedulePlanStepActualEndTime(
                entity.getApsSchedulePlanStepId(), 
                entity.getActualEndTime());
    }
}
```

#### 任务完成时（completeTask和approveTask方法）：
```java
// 3.生产任务更新到完成状态的时候更新aps_schedule_plan_step中的state为3
if (!HuatekTools.isEmpty(task.getApsSchedulePlanStepId())) {
    productionTaskMapper.updateApsSchedulePlanStepOnTaskComplete(task.getApsSchedulePlanStepId());
}
```

## 安全性考虑

1. **空值检查：** 所有更新操作都会检查 `apsSchedulePlanStepId` 是否为空，只有在有效时才执行更新
2. **条件更新：** SQL语句中包含了必要的条件检查，确保只更新有效的记录
3. **事务性：** 这些更新操作都在现有的事务范围内执行，确保数据一致性

## 测试

创建了 `ProductionTaskApsIntegrationTest.java` 测试类来验证集成功能的正确性，包括：
- 任务开始时的APS更新
- 完成时间设置时的APS更新  
- 任务完成时的APS状态更新
- 空值情况下的处理

## 注意事项

1. 这些更新操作是在现有业务逻辑基础上添加的，不会影响原有功能
2. 只有当生产任务关联了APS排产计划步骤（apsSchedulePlanStepId不为空）时才会执行更新
3. 更新操作是同步执行的，如果APS表更新失败，会影响整个事务
4. 建议在生产环境部署前进行充分的集成测试
