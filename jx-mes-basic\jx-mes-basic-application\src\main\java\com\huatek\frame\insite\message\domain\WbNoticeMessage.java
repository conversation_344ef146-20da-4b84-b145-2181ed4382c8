package com.huatek.frame.insite.message.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.modules.system.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * 通知信息框对象 wb_notice_message
 *
 * <AUTHOR>
 * @date 2025-06-11
 */
@Setter
@Getter
@TableName("wb_notice_message")
public class WbNoticeMessage implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 通知标题 */
    @Excel(name = "通知标题")
    private String title;

    /** 通知内容 */
    @Excel(name = "通知内容")
    private String content;

    /** 消息类型（如：系统、任务、流程等） */
    @Excel(name = "消息类型", readConverterExp = "如=：系统、任务、流程等")
    private String type;

    /** 发送人ID（系统发可为 0） */
    @Excel(name = "发送人ID", readConverterExp = "系=统发可为,0=")
    private Long senderId;

    /** 发送人名称 */
    @Excel(name = "发送人名称")
    private String senderName;

    /** 接收人ID */
    @Excel(name = "接收人ID")
    private Long receiverId;

    /** 接收人昵称 */
    @Excel(name = "接收人昵称")
    private String receiverName;

    /** 是否已读（0-未读，1-已读） */
    @Excel(name = "是否已读", readConverterExp = "0=-未读，1-已读")
    private Integer isRead;

    /** 阅读时间（已读时记录） */
    @Excel(name = "阅读时间", readConverterExp = "已=读时记录")
    private Date readTime;

    /** 删除标志（0-正常，1-删除） */
    private Integer delFlag;

    /** 优先级（0-低，1-中，2-高） */
    @Excel(name = "优先级", readConverterExp = "0=-低，1-中，2-高")
    private Long priority;

    /** 点击通知跳转的链接地址 */
    @Excel(name = "点击通知跳转的链接地址")
    private String targetUrl;

    /** 业务类型（如合同、审批、公告等） */
    @Excel(name = "业务类型", readConverterExp = "如=合同、审批、公告等")
    private String bizType;

    /** 业务ID */
    @Excel(name = "业务ID")
    private String bizId;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT)
    private Date updateTime;


}
