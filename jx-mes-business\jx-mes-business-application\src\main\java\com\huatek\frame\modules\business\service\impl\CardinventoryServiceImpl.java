package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.Cardinventory;
import com.huatek.frame.modules.business.domain.vo.CardinventoryVO;
import com.huatek.frame.modules.business.mapper.CardinventoryMapper;
import com.huatek.frame.modules.business.service.CardinventoryService;
import com.huatek.frame.modules.business.service.dto.CardinventoryDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import java.sql.Date;
import org.springframework.util.CollectionUtils;



/**
 * 板卡存放 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-04
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "cardinventory")
//@RefreshScope
@Slf4j
public class CardinventoryServiceImpl implements CardinventoryService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private CardinventoryMapper cardinventoryMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public CardinventoryServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<CardinventoryVO>> findCardinventoryPage(CardinventoryDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<CardinventoryVO> cardinventorys = cardinventoryMapper.selectCardinventoryPage(dto);
		TorchResponse<List<CardinventoryVO>> response = new TorchResponse<List<CardinventoryVO>>();
		response.getData().setData(cardinventorys);
		response.setStatus(200);
		response.getData().setCount(cardinventorys.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(CardinventoryDTO cardinventoryDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(cardinventoryDto.getCodexTorchDeleted())) {
            cardinventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = cardinventoryDto.getId();
		Cardinventory entity = new Cardinventory();
        BeanUtils.copyProperties(cardinventoryDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        try{
            if (HuatekTools.isEmpty(id)) {
                cardinventoryMapper.insert(entity);
            } else {

                cardinventoryMapper.updateById(entity);
            }
        }catch(DuplicateKeyException ex){
            throw new ServiceException("DUT板编号不能重复！");
        }


		TorchResponse response = new TorchResponse();
        CardinventoryVO vo = new CardinventoryVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<CardinventoryVO> findCardinventory(String id) {
		CardinventoryVO vo = new CardinventoryVO();
		if (!HuatekTools.isEmpty(id)) {
			Cardinventory entity = cardinventoryMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<CardinventoryVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<Cardinventory> cardinventoryList = cardinventoryMapper.selectBatchIds(Arrays.asList(ids));
        for (Cardinventory cardinventory : cardinventoryList) {
            cardinventory.setCodexTorchDeleted(Constant.DEFAULT_YES);
            cardinventoryMapper.updateById(cardinventory);
        }
		//cardinventoryMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "cardinventory", convertorFields = "dutBoardType")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CardinventoryVO> selectCardinventoryList(CardinventoryDTO dto) {
        return cardinventoryMapper.selectCardinventoryList(dto);
    }

    /**
     * 导入板卡存放数据
     *
     * @param cardinventoryList 板卡存放数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "cardinventory", convertorFields = "dutBoardType")
    public TorchResponse importCardinventory(List<CardinventoryVO> cardinventoryList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(cardinventoryList) || cardinventoryList.size() == 0) {
            throw new ServiceException("导入板卡存放数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CardinventoryVO vo : cardinventoryList) {
            try {
                Cardinventory cardinventory = new Cardinventory();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, cardinventory);
                QueryWrapper<Cardinventory> wrapper = new QueryWrapper();
                Cardinventory oldCardinventory = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = CardinventoryVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<Cardinventory> oldCardinventoryList = cardinventoryMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldCardinventoryList) && oldCardinventoryList.size() > 1) {
                        cardinventoryMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldCardinventoryList) && oldCardinventoryList.size() == 1) {
                        oldCardinventory = oldCardinventoryList.get(0);
                    }
                }

                cardinventory.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
                if (StringUtils.isNull(oldCardinventory)) {
                    BeanValidators.validateWithException(validator, vo);
                    cardinventoryMapper.insert(cardinventory);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、DUT板名称 " + vo.getDutBoardName() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldCardinventory, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    cardinventoryMapper.updateById(oldCardinventory);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、DUT板名称 " + vo.getDutBoardName() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、DUT板名称 " + vo.getDutBoardName() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、DUT板名称 " + vo.getDutBoardName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CardinventoryVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getDutBoardName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>DUT板名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDutBoardNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>DUT板编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getDutBoardType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>DUT板类型不能为空!");
        }
        if (!vo.getDutBoardType().equals(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_CESHI) && !vo.getDutBoardType().equals(DicConstant.TechnicalManagement.CAPABILITY_REVIEW_INSPECTIONTYPE_LAOHUA)) {
            failureRecord++;
            failureRecordMsg.append("<br/>" + failureRecord + "=>DUT板类型必须是测试/老化!");
        }
        if (HuatekTools.isEmpty(vo.getStorageLocation())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>存放位置不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectCardinventoryListByIds(List<String> ids) {
        List<CardinventoryVO> cardinventoryList = cardinventoryMapper.selectCardinventoryListByIds(ids);

		TorchResponse<List<CardinventoryVO>> response = new TorchResponse<List<CardinventoryVO>>();
		response.getData().setData(cardinventoryList);
		response.setStatus(200);
		response.getData().setCount((long)cardinventoryList.size());
		return response;
    }



}
