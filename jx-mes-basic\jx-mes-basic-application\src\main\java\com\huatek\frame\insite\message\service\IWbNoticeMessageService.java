package com.huatek.frame.insite.message.service;


import com.huatek.frame.insite.message.domain.WbNoticeMessage;

import java.util.List;

/**
 * 通知信息框Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
public interface IWbNoticeMessageService 
{
    /**
     * 查询通知信息框
     * 
     * @param id 通知信息框主键
     * @return 通知信息框
     */
    public WbNoticeMessage selectWbNoticeMessageById(Long id);

    /**
     * 查询通知信息框列表
     * 
     * @param wbNoticeMessage 通知信息框
     * @return 通知信息框集合
     */
    public List<WbNoticeMessage> selectWbNoticeMessageList(WbNoticeMessage wbNoticeMessage);

    /**
     * 新增通知信息框
     * 
     * @param wbNoticeMessage 通知信息框
     * @return 结果
     */
    public int insertWbNoticeMessage(WbNoticeMessage wbNoticeMessage);

    /**
     * 修改通知信息框
     * 
     * @param wbNoticeMessage 通知信息框
     * @return 结果
     */
    public int updateWbNoticeMessage(WbNoticeMessage wbNoticeMessage);

    /**
     * 批量删除通知信息框
     * 
     * @param ids 需要删除的通知信息框主键集合
     * @return 结果
     */
    public int deleteWbNoticeMessageByIds(Long[] ids);

    /**
     * 删除通知信息框信息
     * 
     * @param id 通知信息框主键
     * @return 结果
     */
    public int deleteWbNoticeMessageById(Long id);

    /**
     * 设为已读
     * @param id 消息的id
     * @return 结果
     * */
    public int updateWbNoticeMessageReadStatus(Long id);


}
