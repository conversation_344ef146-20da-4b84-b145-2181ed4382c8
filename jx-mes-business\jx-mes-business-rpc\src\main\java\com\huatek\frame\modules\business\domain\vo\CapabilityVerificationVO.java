package com.huatek.frame.modules.business.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 能力核验VO实体类
* <AUTHOR>
* @date 2025-08-05
**/
@Data
@ApiModel("能力核验DTO实体类")
public class CapabilityVerificationVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
     * 客户信息id
     */
    @ApiModelProperty("客户信息ID")
    private String customerInfomationId;

    /**
	 * 能力核验编号
     **/
    @ApiModelProperty("能力核验编号")
    @Excel(name = "能力核验编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String capabilityVerificationNumber;


    /**
     * 委托单位
     */
    @ApiModelProperty("委托单位")
    @Excel(name = "*委托单位",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String entrustedUnit;



    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "*产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "*产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
     * 产品分类
     */
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String productCategory;


    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "*生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
//    @Excel(name = "产品资料",
//        cellType = Excel.ColumnType.STRING,
//        type = Excel.Type.ALL)
    private String productInformation;

    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    @Excel(name = "状态",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String status;

    /**
     * 确认能力
     **/
    @ApiModelProperty("确认能力")
    @Excel(name = "*确认能力",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String confirmationOfCapability;

    /**
	 * 核验结果
     **/
    @ApiModelProperty("核验结果")
    @Excel(name = "核验结果",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String verificationResult;

    /**
     * 能力反馈
     **/
    @ApiModelProperty("能力反馈")
    @Excel(name = "能力反馈",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String capabilityFeedback;


    /**
     * 反馈人
     **/
    @ApiModelProperty("反馈人")
    @Excel(name = "反馈人",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.EXPORT)
    private String feedbacker;

    /**
	 * 备注
     **/
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String comment;


    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @Excel(name = "创建时间", cellType = Excel.ColumnType.NUMERIC,dateFormat = "yyyy-MM-dd", type = Excel.Type.EXPORT)
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}