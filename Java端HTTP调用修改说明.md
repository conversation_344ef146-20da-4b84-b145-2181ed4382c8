# Java端HTTP调用修改说明

## 问题描述

.NET端的API方法参数发生了变化：

```csharp
[AllowAnonymous]
[HttpPost("mescall")]
public async Task<ActionResult<object>> MesCallFunction([FromForm]InputParamDto dto, IFormFile? file)
```

主要变化：
1. 使用 `[FromForm]` 接收 `InputParamDto`
2. 同时可以接收一个可选的文件参数 `IFormFile? file`
3. Content-Type 需要是 `multipart/form-data` 而不是 `application/json`

这导致Java端原有的 `callMes` 方法报415错误（Unsupported Media Type）。

## 解决方案

### 1. 修改 `callMes` 方法

**修改前：**
```java
try {
    return httpRequest("POST", baseUrl, JSON.toJSONString(inputParamDto), headersMap);
} catch (Exception e) {
    log.error("APS调用失败", e.getMessage(),e);
    throw new RuntimeException(e);
}
```

**修改后：**
```java
try {
    // 修改为使用multipart/form-data格式，不传文件
    return httpRequestWithFormData("POST", baseUrl, inputParamDto, headersMap, null);
} catch (Exception e) {
    log.error("APS调用失败", e.getMessage(),e);
    throw new RuntimeException(e);
}
```

### 2. 新增 `httpRequestWithFormData` 方法

创建了一个新的方法来处理 `multipart/form-data` 格式的请求：

```java
public static String httpRequestWithFormData(
        String method,
        String urlStr,
        InputParamDto inputParamDto,
        Map<String, String> headers,
        MultipartFile file) throws Exception
```

**主要特性：**
- 支持发送 `multipart/form-data` 格式的请求
- 将 `InputParamDto` 的各个字段作为独立的表单字段发送
- 支持可选的文件上传
- 兼容.NET端的 `[FromForm]` 参数绑定

### 3. 修改 `callMesWithFile` 方法

**修改前：**
```java
return httpRequestWithFile("POST", baseUrl, inputParamDto, headersMap, file);
```

**修改后：**
```java
return httpRequestWithFormData("POST", baseUrl, inputParamDto, headersMap, file);
```

### 4. 更新 `httpRequestWithFile` 方法

修改了原有的 `httpRequestWithFile` 方法，使其与.NET端的参数名称匹配：

**主要变化：**
- 将 `InputParamDto` 的表单字段名从 `"inputParamDto"` 改为 `"dto"`
- 增加了对null文件的处理

## 技术细节

### 表单字段映射

Java端发送的表单字段：
```
ServiceUrl -> .NET端的 InputParamDto.ServiceUrl
Param -> .NET端的 InputParamDto.Param  
HttpMethod -> .NET端的 InputParamDto.HttpMethod
file -> .NET端的 IFormFile? file (可选)
```

### Content-Type变化

**修改前：** `application/json`
**修改后：** `multipart/form-data; boundary=----WebKitFormBoundary{timestamp}`

### 请求体格式变化

**修改前（JSON格式）：**
```json
{
  "ServiceUrl": "/test/api",
  "Param": "{\"test\":\"data\"}",
  "HttpMethod": "POST"
}
```

**修改后（multipart/form-data格式）：**
```
------WebKitFormBoundary1234567890
Content-Disposition: form-data; name="ServiceUrl"

/test/api
------WebKitFormBoundary1234567890
Content-Disposition: form-data; name="Param"

{"test":"data"}
------WebKitFormBoundary1234567890
Content-Disposition: form-data; name="HttpMethod"

POST
------WebKitFormBoundary1234567890--
```

## 兼容性说明

1. **向后兼容：** 修改后的方法签名保持不变，现有调用代码无需修改
2. **文件支持：** 两个方法都支持文件上传，`callMes` 传入null文件，`callMesWithFile` 传入实际文件
3. **错误处理：** 保持原有的异常处理机制

## 测试验证

创建了 `HttpClientUtilTest.java` 测试类来验证：
1. `callMes` 方法的表单数据发送
2. `callMesWithFile` 方法的文件上传
3. `InputParamDto` 字段访问
4. null文件的处理

## 注意事项

1. **字段访问：** `InputParamDto` 使用public字段，直接访问而不是通过getter方法
2. **边界处理：** 使用时间戳生成唯一的boundary字符串
3. **编码处理：** 统一使用UTF-8编码
4. **异常处理：** 网络异常会被包装为RuntimeException抛出

## 部署建议

1. 在部署前进行充分的集成测试
2. 确认.NET端API已经更新到新的参数格式
3. 监控415错误是否消失
4. 验证文件上传功能是否正常工作
