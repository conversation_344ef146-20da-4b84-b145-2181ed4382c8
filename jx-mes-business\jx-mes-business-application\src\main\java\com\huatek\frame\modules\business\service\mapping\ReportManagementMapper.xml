<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ReportManagementMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.report_number as reportNumber,
		t.work_order_number as workOrderNumber,
<!--		t.preparer_of_the_report as preparerOfTheReport,-->
		t.compilation_time as compilationTime,
		t.report_status as reportStatus,
		t.attachment as attachment,
		t.codex_torch_applicant as codexTorchApplicant,
		t.codex_torch_approver as codexTorchApprover,
		t.codex_torch_approvers as codexTorchApprovers,
		t.codex_torch_approval_status as codexTorchApprovalStatus,
		t.codex_torch_creator_id as codexTorch<PERSON>reatorId,
		t.codex_torch_updater as codex<PERSON>or<PERSON><PERSON><PERSON><PERSON>r,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectReportManagementPage" parameterType="com.huatek.frame.modules.business.service.dto.ReportManagementDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ReportManagementVO">
		select
		<include refid="Base_Column_List" />,pl.test_type as testType ,o.order_number as orderNumber ,pl.product_name as productName ,pl.product_model as productModel ,
        pl.product_category as productCategory, pc.category_name  as productCategoryName,su.user_name as preparerOfTheReport,
        cim.entrusted_unit as entrustedUnit ,
        GROUP_CONCAT(DISTINCT su2.user_name SEPARATOR ', ') as codexTorchApproverName,
        GROUP_CONCAT(DISTINCT su3.user_name SEPARATOR ', ') as codexTorchApproverNames
			from report_management t
        left join production_order o on t.work_order_number  = o.work_order_number
        left join product_list pl on o.product  = pl.id
        left join product_category pc on pl.product_category =pc.id
        left join evaluation_order eo on eo.id  =pl.evaluation_order_id
        left join customer_information_management cim on eo.entrusted_unit  = cim.id
        left join sys_user su on t.preparer_of_the_report =su.id
        LEFT JOIN sys_user su2 ON FIND_IN_SET(su2.id, REPLACE(t.codex_torch_approver, ' ', ''))
        LEFT JOIN sys_user su3 ON FIND_IN_SET(su3.id, REPLACE(t.codex_torch_approvers, ' ', ''))
            <where>
                and t.codex_torch_deleted = '0'
                <if test="reportNumber != null and reportNumber != ''">
                    and t.report_number  like concat('%', #{reportNumber} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="testType != null and testType != ''">
                    and pl.test_type  = #{testType}
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and o.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and pl.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and pl.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pc.category_name like concat('%', #{productCategory} ,'%')
                </if>
                <if test="codexTorchApprovalStatus != null and codexTorchApprovalStatus != ''">
                    and (t.codex_torch_approval_status  like concat('%', #{codexTorchApprovalStatus} ,'%')
                    or t.codex_torch_approval_status  REGEXP #{codexTorchApprovalStatus})
                </if>

<!--                <if test="codexTorchApprovers != null and codexTorchApprovers != ''">-->
<!--                    and t.codex_torch_approver  like concat('%', #{codexTorchApprovers} ,'%')-->
<!--                </if>-->
                <if test="(codexTorchApprovers != null and codexTorchApprovers != '') or (codexTorchApproverRole != null and codexTorchApproverRole != '')">
                    and (
                    <trim prefixOverrides="OR">
                        <if test="codexTorchApprovers != null and codexTorchApprovers != ''">
                            t.codex_torch_approver  like concat('%', #{codexTorchApprovers} ,'%')
                        </if>
                        <if test="codexTorchApproverRole != null and codexTorchApproverRole != ''">
                            OR t.codex_torch_approver_role like concat('%', #{codexTorchApproverRole}, '%')
                        </if>
                    </trim>
                    )
                </if>
                <if test="compilationTime != null and compilationTime != ''">
                    and t.compilation_time  = #{compilationTime}
                </if>
                <if test="reportStatus != null and reportStatus != ''">
                    and t.report_status  = #{reportStatus}
                </if>

                ${params.dataScope}
            </where>
                GROUP BY t.id
                ORDER BY
                    CASE
                        WHEN t.codex_torch_approval_status LIKE '%保存%' THEN 1
                        WHEN t.codex_torch_approval_status LIKE '%已审批%' THEN 4
                        WHEN t.codex_torch_approval_status LIKE '%驳回%' THEN 3
                        WHEN t.codex_torch_approval_status LIKE '%待审批%' THEN
                            CASE
                                WHEN #{codexTorchApprovers} = null OR  #{codexTorchApprovers} = '' THEN 2
                                WHEN LOCATE(#{codexTorchApprovers}, t.codex_torch_approvers) = 0 THEN 2
                                WHEN t.codex_torch_approver = #{codexTorchApprovers} OR LOCATE(#{codexTorchApprovers}, t.codex_torch_approver) > 0 THEN 2
                                WHEN LOCATE(#{codexTorchApprovers}, t.codex_torch_approvers) &lt; LOCATE(t.codex_torch_approver, t.codex_torch_approvers) THEN 4
                                ELSE 2
                            END
                    END, t.codex_torch_update_datetime DESC
	</select>
     <select id="selectOptionsByWorkOrderNumber" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.work_order_number label,
        	t.work_order_number value
        from production_order t
        WHERE t.work_order_number != ''
         and t.codex_torch_deleted = '0'
     </select>
     <select id="selectDataLinkageByWorkOrderNumber" parameterType="String"
             resultType="java.util.Map">
        select
            t.order_number as orderNumber
        from production_order t
        WHERE t.work_order_number = #{work_order_number}
            and t.codex_torch_deleted = '0'
     </select>

    <select id="selectReportManagementList" parameterType="com.huatek.frame.modules.business.service.dto.ReportManagementDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ReportManagementVO">
		select
		<include refid="Base_Column_List" />,pl.test_type as testType ,o.order_number as orderNumber ,pl.product_name as productName ,pl.product_model as productModel ,
        pl.product_category as productCategory,pc.category_name  as productCategoryName,cim.entrusted_unit as entrustedUnit ,su.user_name as preparerOfTheReport
        from report_management t
        left join production_order o on t.work_order_number  = o.work_order_number
        left join product_list pl on o.product  = pl.id
        left join product_category pc on pl.product_category =pc.id
        left join evaluation_order eo on eo.id  =pl.evaluation_order_id
        left join customer_information_management cim on eo.entrusted_unit  = cim.id
        left join sys_user su on t.preparer_of_the_report =su.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="reportNumber != null and reportNumber != ''">
                    and t.report_number  like concat('%', #{reportNumber} ,'%')
                </if>
                <if test="workOrderNumber != null and workOrderNumber != ''">
                    and t.work_order_number  like concat('%', #{workOrderNumber} ,'%')
                </if>
                <if test="testType != null and testType != ''">
                    and pl.test_type  = #{testType}
                </if>
                <if test="orderNumber != null and orderNumber != ''">
                    and o.order_number  like concat('%', #{orderNumber} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and pl.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and pl.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pl.product_category  like concat('%', #{productCategory} ,'%')
                </if>

                <if test="compilationTime != null and compilationTime != ''">
                    and t.compilation_time  = #{compilationTime}
                </if>
                <if test="reportStatus != null and reportStatus != ''">
                    and t.report_status  = #{reportStatus}
                </if>

                ${params.dataScope}
            </where>
                ORDER BY t.codex_torch_approval_status DESC ,t.codex_torch_update_datetime DESC
	</select>

    <select id="selectReportManagementListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ReportManagementVO">
		select
		<include refid="Base_Column_List" />,pl.test_type as testType ,o.order_number as orderNumber ,pl.product_name as productName ,pl.product_model as productModel ,
        pl.product_category as productCategory, pc.category_name  as productCategoryName,cim.entrusted_unit as entrustedUnit ,su.user_name as preparerOfTheReport
        from report_management t
        left join production_order o on t.work_order_number  = o.work_order_number
        left join product_list pl on o.product  = pl.id
        left join product_category pc on pl.product_category =pc.id
        left join evaluation_order eo on eo.id  =pl.evaluation_order_id
        left join customer_information_management cim on eo.entrusted_unit  = cim.id
        left join sys_user su on t.preparer_of_the_report =su.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

	<select id="selectOtherReportByWorkOrderNumber"
            resultType="com.huatek.frame.modules.business.domain.ReportManagement">
        select * from report_management where work_order_number = #{workOrderNumber} and id != #{id} and (report_status = #{reportStatus} or report_status = #{reportStatus2})
    </select>
</mapper>