package com.huatek.frame.modules.business.utils.excel;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import com.alibaba.excel.event.WriteHandler;
import com.alibaba.excel.metadata.Cell;

public class CustomBackgroundWriteHandler implements WriteHandler {
    private int specialRowIndex;

    public CustomBackgroundWriteHandler(int specialRowIndex) {
        this.specialRowIndex = specialRowIndex;
    }


	@Override
	public void sheet(int sheetNo, Sheet sheet) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void row(int rowNum, Row row) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void cell(int cellNum, org.apache.poi.ss.usermodel.Cell cell) {
		 if (cell.getRowIndex() == specialRowIndex) {
	            Workbook workbook = cell.getSheet().getWorkbook();
	            CellStyle cellStyle = workbook.createCellStyle();
	            
	            // 设置背景色为浅灰
	            cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
	            cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

	            cell.setCellStyle(cellStyle);
	        }
		
	}
    
    // 其他必须实现的方法...
}
