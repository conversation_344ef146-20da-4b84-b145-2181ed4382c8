package com.huatek.frame.insite.message.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 企业微信验证Url控制层
 */
@RestController
@RequestMapping("/wx/verify")
public class WxVerifyController  {

    public final static String sToken = "xxxx";
    public final static String sEncodingAESKey = "xxx";
    public final static String sCorpID = "xxx";

    @GetMapping("/verifyUrl")
    public void verifyUrl(HttpServletRequest request, HttpServletResponse response) throws IOException {
//        // URL 参数
//        String sVerifyMsgSig = request.getParameter("msg_signature");
//        String sVerifyTimeStamp = request.getParameter("timestamp");
//        String sVerifyNonce = request.getParameter("nonce");
//        String sEchoStr = request.getParameter("echostr");
//
//        try {
//            WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(sToken, sEncodingAESKey, sCorpID);
//            // 验证 URL
//            String sReplyEchoStr = wxcpt.VerifyURL(sVerifyMsgSig, sVerifyTimeStamp, sVerifyNonce, sEchoStr);
//            System.out.println("verifyurl echostr: " + sReplyEchoStr);
//            // 返回验证结果给微信服务器
//            response.setContentType("text/plain");
//            response.setCharacterEncoding("UTF-8");
//            response.getWriter().write(sReplyEchoStr);
//        } catch (Exception e) {
//            e.printStackTrace();
//            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
//        }
   }


}
