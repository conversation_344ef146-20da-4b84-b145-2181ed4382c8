package com.huatek.frame.modules.business.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 不合格工序信息
 */
@Data
@ApiModel("不合格工序信息")
public  class UnqualifiedProcessVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 客户工序名称
     **/
    @ApiModelProperty("客户工序名称")
    private String processName;

    /**
     * 不合格数量
     **/
    @ApiModelProperty("不合格数量")
    private Long unqualifiedQuantity;

    /**
     * 不合格原因(失效模式)
     **/
    @ApiModelProperty("不合格原因(失效模式)")
    private String failureMode;
}
