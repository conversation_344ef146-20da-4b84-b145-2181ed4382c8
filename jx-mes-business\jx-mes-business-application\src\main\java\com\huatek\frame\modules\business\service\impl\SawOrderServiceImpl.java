package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.ProductList;
import com.huatek.frame.modules.business.domain.vo.ProductListVO;
import com.huatek.frame.modules.business.domain.vo.SawOrderProductVO;
import com.huatek.frame.modules.business.mapper.ProductListMapper;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.constant.UserInfoConstants;
import com.huatek.frame.modules.system.domain.Dic;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
//import com.huatek.frame.modules.system.service.SysUserService;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import com.huatek.frame.modules.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.SawOrder;
import com.huatek.frame.modules.business.domain.vo.SawOrderVO;
import com.huatek.frame.modules.business.mapper.SawOrderMapper;

import java.sql.Date;
import java.util.stream.Collectors;

import com.huatek.frame.modules.system.domain.SysUser;

import org.springframework.util.CollectionUtils;

import com.huatek.frame.modules.business.domain.vo.ProductDetailsVO;


import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;

/**
 * 监制验收工单 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-20
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "sawOrder")
//@RefreshScope
@Slf4j
public class SawOrderServiceImpl extends ServiceImpl<SawOrderMapper, SawOrder> implements SawOrderService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private SawOrderMapper sawOrderMapper;

    @Autowired
    private ProductListService productListService;

    @Autowired
    private ProductListMapper productListMapper;

    @DubboReference
    private SysGroupService sysGroupService;

    @DubboReference
    private SysUserService sysUserService;

//	@Autowired
//    private SysUserMapper sysUserMapper;

//    @Autowired
//    private SysUserService sysUserService;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private ProductInventoryService productInventoryService;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private ProductDetailsService productDetailsService;


	public SawOrderServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<SawOrderVO>> findSawOrderPage(SawOrderPageDTO requestParam) {
		PageHelper.startPage(requestParam.getPage(), requestParam.getLimit());
		Page<SawOrderVO> sawOrders = sawOrderMapper.selectSawOrderPage(requestParam);
		TorchResponse<List<SawOrderVO>> response = new TorchResponse<List<SawOrderVO>>();
		response.getData().setData(sawOrders);
		response.setStatus(200);
		response.getData().setCount(sawOrders.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse<SawOrderVO> saveOrUpdate(SawOrderAddOrUpdateDTO requestParam) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(requestParam.getCodexTorchDeleted())) {
            requestParam.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = requestParam.getId();
		SawOrder entity = new SawOrder();
        BeanUtils.copyProperties(requestParam, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
            TorchResponse response = codeManagementService.getOrderNumber("JZYS");
            entity.setWorkOrderNumber(response.getData().getData().toString());
			sawOrderMapper.insert(entity);
		} else {
			sawOrderMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        SawOrderVO vo = new SawOrderVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<SawOrderVO> findSawOrder(String id) {
        SawOrderVO sawOrderVO = new SawOrderVO();
		if (!HuatekTools.isEmpty(id)) {
			sawOrderVO = sawOrderMapper.findSawOrder(id);
			if(HuatekTools.isEmpty(sawOrderVO)) {
                throw new ServiceException("查询失败");
			}
		}
		TorchResponse<SawOrderVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(sawOrderVO);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(List<String> ids) {
	    List<SawOrder> sawOrderList = sawOrderMapper.selectBatchIds(ids);
        for (SawOrder sawOrder : sawOrderList) {
            sawOrder.setCodexTorchDeleted(Constant.DEFAULT_YES);
            sawOrderMapper.updateById(sawOrder);
        }
		//sawOrderMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("responsiblePerson",sawOrderMapper::selectOptionsByResponsiblePerson);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "saw_order", convertorFields = "acceptanceType,status")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<SawOrderVO> selectSawOrderList(SawOrderDTO dto) {
        return sawOrderMapper.selectSawOrderList(dto);
    }

    /**
     * 导入监制验收工单数据
     *
     * @param sawOrderList 监制验收工单数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "saw_order", convertorFields = "acceptanceType,status")
    public TorchResponse importSawOrder(List<SawOrderVO> sawOrderList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(sawOrderList) || sawOrderList.size() == 0) {
            throw new ServiceException("导入监制验收工单数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (SawOrderVO vo : sawOrderList) {
            try {
                SawOrder sawOrder = new SawOrder();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, sawOrder);
                QueryWrapper<SawOrder> wrapper = new QueryWrapper();
                SawOrder oldSawOrder = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = SawOrderVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<SawOrder> oldSawOrderList = sawOrderMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldSawOrderList) && oldSawOrderList.size() > 1) {
                        sawOrderMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldSawOrderList) && oldSawOrderList.size() == 1) {
                        oldSawOrder = oldSawOrderList.get(0);
                    }
                }
                if (StringUtils.isNull(oldSawOrder)) {
                    BeanValidators.validateWithException(validator, vo);
                    sawOrderMapper.insert(sawOrder);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单编号 " + vo.getOrderNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldSawOrder, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    sawOrderMapper.updateById(oldSawOrder);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、订单编号 " + vo.getOrderNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、订单编号 " + vo.getOrderNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、订单编号 " + vo.getOrderNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(SawOrderVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
//        if (!HuatekTools.isEmpty(vo.getResponsiblePerson())) {
//            List<String> responsiblePersonList = Arrays.asList(vo.getResponsiblePerson().split(","));
//            //todo 根据部门选择名字
////            List<SysUser> list = sysUserService.selectList(new QueryWrapper<SysUser>().in("name", responsiblePersonList));
//            if (CollectionUtils.isEmpty(list)) {
//                failureRecord++;
//                failureRecordMsg.append("负责人=" + vo.getResponsiblePerson() + "; ");
//            }
//        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse<List<SawOrderVO>> selectSawOrderListByIds(List<String> ids) {
        List<SawOrderVO> sawOrderList = sawOrderMapper.selectSawOrderListByIds(ids);

		TorchResponse<List<SawOrderVO>> response = new TorchResponse<List<SawOrderVO>>();
		response.getData().setData(sawOrderList);
		response.setStatus(200);
		response.getData().setCount((long)sawOrderList.size());
		return response;
    }

    /**
     * 监制验收工单主子表单组合提交
     *
	 * @param sawOrderDto 监制验收工单DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(SawOrderDTO sawOrderDto){
//        String currentUser = SecurityContextHolder.getCurrentUserName();
//        if (HuatekTools.isEmpty(sawOrderDto.getCodexTorchDeleted())) {
//            sawOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
//        }
//
//        //非必要字段处理
//        sawOrderDto.setId("");
//        sawOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
//
//        //TODO: TorchDetailItemIds TO BE DEPRECATED
//        sawOrderDto.setCodexTorchDetailItemIds("");
//
//        TorchResponse<SawOrderVO> masterSubmitResp = this.saveOrUpdate(sawOrderDto);
//        SawOrderVO masterVo = masterSubmitResp.getData().getData();
//
//        List<ProductDetailsDTO> productDetailsDTOs = new ArrayList<>();
//        if (sawOrderDto.getDetailFormItems() != null && sawOrderDto.getDetailFormItems().length > 0) {
//            productDetailsDTOs = Arrays.asList(sawOrderDto.getDetailFormItems());
//        } else if (StringUtils.isNotEmpty(sawOrderDto.getCodexTorchDetailItemIds())) {
//        } else {
//            throw new ServiceException("表单提交异常，表单明细项为空");
//        }
//
//        for(ProductDetailsDTO productDetailsDto : productDetailsDTOs){
//            productDetailsDto.setId("");
//
//            //非必要字段处理
//            productDetailsDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
//
//            //主子表关联ID
//            productDetailsDto.setCodexTorchMasterFormId(masterVo.getId());
//            // 业务字段管理
//            productDetailsDto.setWorkOrderNumber(masterVo.getWorkOrderNumber());
//
//            //提交
//            TorchResponse<ProductDetailsVO> detailSubmitResp = productDetailsService.saveOrUpdate(productDetailsDto);
//            ProductDetailsVO detailVo = detailSubmitResp.getData().getData();
//        }

		TorchResponse response = new TorchResponse();
//        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

    @Override
    public TorchResponse pageProduct(SawOrderProductPageDTO requestParam) {

        PageHelper.startPage(requestParam.getPage(), requestParam.getLimit());
        Page<SawOrderProductVO> sawOrderProductVOS = productListMapper.pagesawOrderProduct(requestParam);

        TorchResponse response = new TorchResponse();
        response.getData().setData(sawOrderProductVOS);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(sawOrderProductVOS.getTotal());
        return response;
    }

    @Override
    public TorchResponse finishProduct(SawOrderProductFinishDTO requestParam) {
        List<ProductList> productLists = productListMapper.selectBatchIds(requestParam.getIds());
        List<ProductList> validProductLists = productLists.stream()
                .filter(item -> StrUtil.equals(item.getSawStatus(), DicConstant.SalesOrder.SAW_ORDER_PRODUCT_LIST_STATUS_UNFINISHED))
                .collect(Collectors.toList());
        if (validProductLists.isEmpty()){
            throw new ServiceException("所选择的产品已全部完成，请选择未完成的产品！");
        }
        validProductLists.forEach(item -> {
            item.setComment(requestParam.getComment());
            item.setSawStatus(DicConstant.SalesOrder.SAW_ORDER_PRODUCT_LIST_STATUS_FINISHED);
            item.setCompleteDate(new Date(System.currentTimeMillis()));
        });
        try {
            productListService.saveOrUpdateBatch(productLists);
        }catch (Exception ex){
            throw new ServiceException("批量完成产品失败，请联系管理员");
        }
        //检查所属工单的产品是否已全部完成，如果已全部完成，修改工单状态为完成
        LambdaQueryWrapper<ProductList> queryWrapper = Wrappers.lambdaQuery(ProductList.class)
                .eq(ProductList::getSawOrderId, requestParam.getSawId())
                .eq(ProductList::getSawStatus, DicConstant.SalesOrder.SAW_ORDER_PRODUCT_LIST_STATUS_UNFINISHED);
        List<ProductList> unfinishedProductLists = productListMapper.selectList(queryWrapper);
        if (unfinishedProductLists.isEmpty()){
            LambdaUpdateWrapper<SawOrder> updateWrapper = Wrappers.lambdaUpdate(SawOrder.class)
                    .eq(SawOrder::getId, requestParam.getSawId())
                    .set(SawOrder::getStatus, DicConstant.ProductionOrder.SUPERVISOR_ACCEPTANCE_STATUS_FINISHED)
                    .set(SawOrder::getCompletionDate, DateUtil.beginOfDay(DateUtil.date()));
            sawOrderMapper.update(null, updateWrapper);
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;

    }

    @Override
    public TorchResponse finishSawOrder(SawOrderFinishDTO requestParam) {
        List<SawOrder> sawOrders = sawOrderMapper.selectBatchIds(requestParam.getIds());

        sawOrders.forEach(item -> {
            if (StrUtil.equals(item.getStatus(), DicConstant.ProductionOrder.SUPERVISOR_ACCEPTANCE_STATUS_FINISHED)){
                throw new ServiceException("工单编号为" + item.getWorkOrderNumber() + "的工单状态已完成, 请操作未完成的工单");
            }
            //先检查该工单关联的产品有没有未完成的
            LambdaQueryWrapper<ProductList> queryWrapper = Wrappers.lambdaQuery(ProductList.class)
                    .eq(ProductList::getSawOrderId, item.getId())
                    .eq(ProductList::getSawStatus, DicConstant.SalesOrder.SAW_ORDER_PRODUCT_LIST_STATUS_UNFINISHED);
            List<ProductList> productLists = productListMapper.selectList(queryWrapper);
            if (CollUtil.isNotEmpty(productLists)){
                //有未完成的产品
                throw new ServiceException("工单编号为" + item.getWorkOrderNumber() + "的工单下有未完成产品");
            }
            //更改状态和备注
            item.setStatus(DicConstant.ProductionOrder.SUPERVISOR_ACCEPTANCE_STATUS_FINISHED);
            item.setComment(requestParam.getComment());
            item.setCompletionDate(new Date(System.currentTimeMillis()));
        });
        //批量更新
        this.updateBatchById(sawOrders);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;

    }

    @Override
    public TorchResponse inStockSawOrder(List<String> ids) {
        List<SawOrderVO> sawOrders = this.selectSawOrderListByIds(ids).getData().getData();
        //过滤掉未完成状态的工单
        List<SawOrderVO> validSawOrders = sawOrders.stream()
                .filter(item -> StrUtil.equals(item.getStatus(), DicConstant.ProductionOrder.SUPERVISOR_ACCEPTANCE_STATUS_FINISHED))
                .collect(Collectors.toList());
        if (validSawOrders.isEmpty()){
            throw new ServiceException("不存在已完成状态的工单，请选择已完成的工单进行入库");
        }
        validSawOrders.forEach(item -> {
            item.setStatus(DicConstant.ProductionOrder.SUPERVISOR_ACCEPTANCE_STATUS_STORED);
            ProductInventoryDTO inventoryDTO = ProductInventoryDTO.builder()
                    .workOrderNumber(item.getWorkOrderNumber())
                    .orderNumber(item.getOrderNumber())
                    .entrustedUnit(item.getEntrustedUnitId())
                    .engineeringCode(item.getEngineeringCode())
                    .inspectionSender(item.getPrincipal())
                    .codexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()))
                    .codexTorchCreatorId(SecurityContextHolder.getCurrentUserId())
                    .codexTorchDeleted(Constant.DEFAULT_NO)
                    .codexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId())
                    .codexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()))
                    .codexTorchUpdater(SecurityContextHolder.getCurrentUserName())
                    .status(DicConstant.Warehousing.WAREHOUSING_UNSTOCK)
                    .build();
            productInventoryService.saveOrUpdate(inventoryDTO);
        });
        //批量更新监制验收工单状态
        List<SawOrder> sawOrderList = validSawOrders.stream().map(item -> BeanUtil.toBean(item, SawOrder.class)).collect(Collectors.toList());
        try {
            this.saveOrUpdateBatch(sawOrderList);
        }catch (Exception ex){
            throw new ServiceException("批量更新监制验收工单状态失败");
        }
   
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return  response;

    }

    @Override
    public TorchResponse getResponsibilities() {
        List<String> packageGroupIds = getReliabilityUsers("", UserInfoConstants.RELIABILITY_ANALYSIS_CENTER_ID);
        List<SysUserVO> users = sysUserService.getUsersByDepart(packageGroupIds);
        List<SelectOptionsVO> res = new ArrayList<>();
        res = users.stream().map(item -> {
            SelectOptionsVO selectOptionsVO = new SelectOptionsVO();
            selectOptionsVO.setLabel(item.getUserName());
            selectOptionsVO.setValue(item.getUserName());
            return selectOptionsVO;
        }).collect(Collectors.toList());

        TorchResponse response = new TorchResponse();
        response.getData().setData(res);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    /**
     * dfs查找可靠性分析中心所有group_id(包含子级)
     * @param parentId 父级部门id
     * @param curId 当前部门id
     * @return
     */
    private List<String> getReliabilityUsers(String parentId, String curId){
        List<String> ids = new ArrayList<>();
        ids.add(curId);
        LambdaQueryWrapper<SysGroup> queryWrapper = Wrappers.lambdaQuery(SysGroup.class)
                .eq(SysGroup::getGroupParentId, curId);
        List<SysGroup> groups = sysGroupService.getGroupsByFatherId(curId);
        List<String> childrenIds = groups.stream().map(SysGroup::getId).collect(Collectors.toList());
        for (String nextId : childrenIds){
            ids.addAll(getReliabilityUsers(curId, nextId));
        }

        return ids;
    }


}
