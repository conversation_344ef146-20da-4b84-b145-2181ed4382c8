//package com.huatek.frame.modules.business.utils.excel;
//
//import java.text.SimpleDateFormat;
//import java.util.Date;
//
//import org.apache.poi.ss.formula.functions.T;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.core.mapper.BaseMapper;
//
///**
// * @Description: 编码生成工具
// * <AUTHOR>
// * @Since JDK 1.8
// * @Version V1.0
// * @Date:2023年2月22日 上午9:59:19
// * Copyright (c) 2023, www.huatek.com All Rights Reserved.
// */
//
//@SuppressWarnings("hiding")
//public class Number<T> {
//
//	/**
//	 * 查询Mapper
//	 */
//	private BaseMapper<T> baseMapper;
//
//	/**
//	 * 依赖字段
//	 */
//	private String numberField;
//
//	/**
//	 * 序号段总位数
//	 */
//	private int seqLength;
//
//	/**
//	 * 生成编码数量
//	 */
//	private int count;
//
//	/**
//	 * 编码公共前缀
//	 */
//	private StringBuffer noPrefix;
//
//	public Number(BaseMapper<T> baseMapper, String numberField, int seqLength, int count) {
//		this.baseMapper = baseMapper;
//		this.numberField = numberField;
//		this.seqLength = seqLength;
//		this.count = count;
//		this.noPrefix = new StringBuffer();
//	}
//
//	public Number<T> append(String str){
//		this.noPrefix.append(str);
//		return this;
//	}
//
//	public Number<T> append(Part part) {
//		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//		String dt = sdf.format(new Date());
//		if(part == Part.Date) {
//			this.noPrefix.append(dt.substring(0, 8));
//		} else if(part == Part.Time) {
//			this.noPrefix.append(dt.substring(8));
//		} else if(part == Part.DateTime) {
//			this.noPrefix.append(dt);
//		} else if(part == Part.YYYY) {
//			this.noPrefix.append(dt.substring(0, 4));
//		} else if(part == Part.YY) {
//			this.noPrefix.append(dt.substring(2, 4));
//		} else if(part == Part.MM) {
//			this.noPrefix.append(dt.substring(4, 6));
//		} else if(part == Part.DD) {
//			this.noPrefix.append(dt.substring(6, 8));
//		}
//		return this;
//	}
//
//	public String[] numbers() {
//		String[] res = new String[count];
////		int c = baseMapper.selectCount(new QueryWrapper<T>().like(numberField, this.noPrefix.toString()));
//		for(int i = 0; i < count; i++) {
//			String numberIndex = "000000000000000000000000000" + (c + i + 1);
//			res[i] = this.noPrefix.toString() + numberIndex.substring(numberIndex.length() - seqLength);
//		}
//		return res;
//	}
//
//	/**
//	 * 编码组成公共部分
//	 * <AUTHOR>
//	 *
//	 */
//	public enum Part{
//		/**
//		 * 日期yyyyMMDD
//		 */
//		Date,
//		/**
//		 * 时间 HHmmss
//		 */
//		Time,
//		/**
//		 * 日期+时间
//		 */
//		DateTime,
//		/**
//		 * 四位年份
//		 */
//		YYYY,
//		/**
//		 * 两位年份
//		 */
//		YY,
//		/**
//		 * 月份
//		 */
//		MM,
//		/**
//		 * 日
//		 */
//		DD
//	}
//}
//
//
