package com.huatek.frame.modules.system.service.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.huatek.frame.insite.message.handler.PushMessageWebsocketHandler;
import com.huatek.frame.modules.system.service.WebSocketMessageService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Set;

@Service
@RefreshScope
@DubboService(version = "1.0.0", group = "websocket")
public class WebSocketMessageServiceImpl implements WebSocketMessageService {

    @Autowired
    private PushMessageWebsocketHandler pushMessageWebsocketHandler;

    @Override
    public boolean sendToUser(String userId, String message) {
        return pushMessageWebsocketHandler.sendToUser(userId, message);
    }

    @Override
    public int sendToUsers(Set<String> userIds, String message) {
        return pushMessageWebsocketHandler.sendToUsers(userIds, message);
    }

    @Override
    public void broadcast(String message) {
        pushMessageWebsocketHandler.broadcast(message);
    }


}
