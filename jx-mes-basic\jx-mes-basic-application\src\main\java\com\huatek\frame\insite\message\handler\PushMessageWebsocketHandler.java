package com.huatek.frame.insite.message.handler;


import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.huatek.frame.insite.message.service.IWbNoticeMessageService;
import com.huatek.frame.insite.message.service.impl.WxService;
import com.huatek.frame.modules.system.mapper.SysUserMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 消息推送 WebSocket Handler
 */
@Component
@DubboService
//@CacheConfig(cacheNames = "user")
@RefreshScope
public class    PushMessageWebsocketHandler extends TextWebSocketHandler {

    @Autowired
    private IWbNoticeMessageService wbNoticeMessageService;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private WxService wxService;

    private static final Logger logger = LoggerFactory.getLogger(PushMessageWebsocketHandler.class);

    // 存储所有连接的会话
    private final Set<WebSocketSession> sessions = Collections.synchronizedSet(new HashSet<>());

    // 存储用户ID和session的映射
    private final ConcurrentMap<String, WebSocketSession> userSessions = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String userId = getUserIdFromSession(session);
        if (userId != null) {
            userSessions.put(userId, session);
            logger.info("用户 {} WebSocket连接建立", userId);
        }
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        JSONObject jsonObject = JSONObject.parseObject(payload);

        String type = jsonObject.getString("type");
        if ("ping".equalsIgnoreCase(type)) {

            //session.sendMessage(new TextMessage("{\"type\":\"pong\"}"));
            return;
        }

        if ("refresh".equalsIgnoreCase(type)) {
            broadcast(payload);
            return;
        }

        // 获取用户信息
        String userId = (String) session.getAttributes().get("userId");
        String nickName = (String) session.getAttributes().get("nickName");

        // 提取data所需的字段
        JSONObject data = jsonObject.getJSONObject("data");
        String title = data.getString("title");
        String content = data.getString("content");
        Long receiverId = data.getLong("receiverId");
        String receiverName = data.getString("receiverName");

        // 1. 保存消息到数据库
        //String receiverWxId = saveMessages(userId, nickName, title, content, receiverId, receiverName);

        // 2. 广播给所有在线客户端
        broadcast(payload);

    }


    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String userId = getUserIdFromSession(session);
        if (userId != null) {
            userSessions.remove(userId);
            logger.info("用户 {} WebSocket连接关闭", userId);
        }
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        exception.printStackTrace();
        sessions.remove(session);
        if (session.isOpen()) {
            session.close();
        }
    }

    /**
     * 广播消息到所有在线用户
     */
    public void broadcast(String payload) {
        for (WebSocketSession s : sessions) {
            if (s.isOpen()) {
                try {
                    s.sendMessage(new TextMessage(payload));
                } catch (IOException e) {
                    System.out.println("广播消息失败："+e);
                }
            }
        }
    }


    /**
     * 移除WebSocket会话
     */
    public void removeSession(WebSocketSession session) {
        sessions.remove(session);
    }

    /**
     * 获取所有活跃会话数量
     */
    public int getActiveSessionCount() {
        return (int) sessions.stream().filter(WebSocketSession::isOpen).count();
    }
    /**
     * 发送消息给指定用户
     * @param userId 用户ID
     * @param payload 消息内容
     * @return 是否发送成功
     */
    public boolean sendToUser(String userId, String payload) {
        return sendToUser(userId, payload, null);
    }

    /**
     * 发送消息给指定用户（带异常处理器）
     * 基于userSessions映射（ConcurrentMap<String, WebSocketSession>）进行优化
     * @param userId 用户ID
     * @param payload 消息内容
     * @param exceptionHandler 异常处理器
     * @return 是否发送成功
     */
    public boolean sendToUser(String userId, String payload, ExceptionHandler exceptionHandler) {
        // 参数校验
        if (StringUtils.isBlank(userId)) {
            logger.warn("发送消息失败: 用户ID不能为空");
            return false;
        }
        if (StringUtils.isBlank(payload)) {
            logger.warn("发送消息给用户 {} 失败: 消息内容不能为空", userId);
            return false;
        }

        // 直接从userSessions映射中获取用户的会话
        WebSocketSession session = userSessions.get(userId);

        if (session == null) {
            logger.debug("用户 {} 没有活跃的WebSocket会话", userId);
            return false;
        }

        if (!session.isOpen()) {
            logger.warn("用户 {} 的WebSocket会话已关闭，正在清理", userId);
            userSessions.remove(userId);
            return false;
        }

        try {
            session.sendMessage(new TextMessage(payload));
            logger.debug("消息成功发送给用户: {}", userId);
            return true;

        } catch (IOException e) {
            logger.warn("发送消息给用户 {} 失败: {}", userId, e.getMessage());

            // 处理异常
            handleException(session, e, exceptionHandler);

            // 如果发生异常，清理无效会话
            if (!session.isOpen()) {
                userSessions.remove(userId);
                logger.info("已清理用户 {} 的无效会话", userId);
            }

            return false;
        } catch (Exception e) {
            logger.error("发送消息给用户 {} 时发生未知异常", userId, e);
            return false;
        }
    }
    /**
     * 发送消息给多个用户（简化版）
     */
    public int sendToUsers(Set<String> userIds, String payload) {
        return sendToUsers(userIds, payload, null);
    }
    /**
     * 发送消息给多个用户
     * @param userIds 用户ID集合
     * @param payload 消息内容
     * @param exceptionHandler 异常处理器
     * @return 成功发送的用户数量
     */
    public int sendToUsers(Set<String> userIds, String payload, ExceptionHandler exceptionHandler) {
        if (CollectionUtils.isEmpty(userIds)) {
            logger.warn("发送消息失败: 用户ID集合为空");
            return 0;
        }
        if (StringUtils.isBlank(payload)) {
            logger.warn("发送消息失败: 消息内容不能为空");
            return 0;
        }

        int successCount = 0;
        int totalCount = userIds.size();

        for (String userId : userIds) {
            boolean sent = sendToUser(userId, payload, exceptionHandler);
            if (sent) {
                successCount++;
            }
        }

        logger.info("批量消息发送完成: 成功 {}/{}", successCount, totalCount);
        return successCount;
    }


    /**
     * 判断会话是否属于目标用户
     */
    private boolean isTargetUser(WebSocketSession session, String userId) {
        String sessionUserId = getUserIdFromSession(session);
        return userId.equals(sessionUserId);
    }

    /**
     * 从WebSocketSession中获取用户ID
     *
     */
    private String getUserIdFromSession(WebSocketSession session) {
        // 根据你的系统实际情况选择一种方式：

        // 方式1：从attributes获取（推荐）
        String userId = (String) session.getAttributes().get("userId");
        if (userId != null) {
            return userId;
        }

        // 方式2：从principal获取
        if (session.getPrincipal() != null) {
            return session.getPrincipal().getName();
        }

        // 方式3：日志记录无法获取用户ID的情况
        logger.warn("无法从WebSocketSession中获取用户ID，sessionId: {}", session.getId());
        return null;
    }

    /**
     * 异常处理
     */
    private void handleException(WebSocketSession session, IOException e, ExceptionHandler exceptionHandler) {
        if (exceptionHandler != null) {
            exceptionHandler.handle(session, e);
        } else {
            // 默认异常处理
            System.err.println("发送消息失败，会话ID: " + session.getId() + ", 错误: " + e.getMessage());
            // 可以选择关闭出错的会话
            try {
                session.close();
            } catch (IOException ex) {
                System.err.println("关闭会话失败: " + ex.getMessage());
            }
            removeSession(session);
        }
    }

    /**
     * 异常处理器接口
     */
    @FunctionalInterface
    public interface ExceptionHandler {
        void handle(WebSocketSession session, IOException exception);
    }



}

