package com.huatek.frame.modules.business.utils.excel;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Description: 正则工具类
 * <AUTHOR>
 * @Since JDK 1.8
 * @Version V1.0
 * @Date:2023年2月9日 上午10:32:06
 * Copyright (c) 2023, www.huatek.com All Rights Reserved. 
 */

public class RegexUtil {
	
	/**
	 * 手机号码
	 */
	public static final String PHONE_REG = "^[1][3,4,5,6,7,8,9][0-9]{9}$";
	
	/**
	 * 身份证
	 */
	public static final String IDCARD_REG = "^[1-9]\\d{5}(18|19|([23]\\d))\\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";
	
	/**
	 * 邮箱
	 */
	public static final String EMAIL_REG = "^([A-Za-z0-9_\\-\\.])+\\@([A-Za-z0-9_\\-\\.])+\\.([A-Za-z]{2,4})$";
	
	/**
	 * URL
	 */
	public static final String URL_REG = "^((https?|ftp|file):\\/\\/)?([\\da-z\\.-]+)\\.([a-z\\.]{2,6})([\\/\\w \\.-]*)*\\/?$";
	
	/**
	 * IPV4
	 */
	public static final String IPV4_REG = "^((\\d{1}|([1-9]{1}\\d{1})|(1\\d{2})|(2[0-4]{1}\\d{1})|(25[0-5]{1}))\\.){3}(\\d{1}|([1-9]{1}\\d{1})|(1\\d{2})|(2[0-4]{1}\\d{1})|(25[0-5]{1}))$";
	
	/**
	 * 日期
	 */
	public static final String DATE_REG = "^\\d{4}(\\-)\\d{1,2}\\1\\d{1,2}$";
	
	/**
	 * 日期时间
	 */
	public static final String DATE_TIME_REG = "^(\\d{1,4})(-|\\/)(\\d{1,2})\\2(\\d{1,2}) (\\d{1,2}):(\\d{1,2}):(\\d{1,2})$";
	
	/**
	 * 整数
	 */
	public static final String INT_REG = "^[-+]?((\\d{1})|([1-9]{1}\\d*))$";
	
	/**
	 * 小数
	 */
	public static final String DOUBLE_REG = "^[-\\+]?((((0\\.)|([1-9]{1}\\d*\\.))\\d+)|((\\d{1})|([1-9]{1}\\d*)))$";
	
	/**
	 * 邮政编号
	 */
	public static final String POSTALNO_REG = "^\\d{6}$";
	
	/**
	 * QQ号
	 */
	public static final String QQ_REG = "^[1-9][0-9]{4,10}$";
	
	/**
	 * 包含中文
	 */
	public static final String ZH_REG = "[\u4E00-\u9FA5]";
	
	/**
	 * 端口号
	 */
	public static final String PORT_REG = "^(([1-9]{1}\\d{0,3})|([1-5]{1}\\d{4})|(6[0-4]{1}\\d{3})|(65[0-4]{1}\\d{2})|(655[0-2]{1}\\d{1})|(6553[0-5]{1}))$";
	

	/**
	 * 检测value是否匹配regex
	 * @param value
	 * @param regex
	 * @return
	 */
	public static boolean isMatched(String value, String regex) {
		Pattern pattern = Pattern.compile(regex);
		Matcher matcher = pattern.matcher(value);
		return matcher.find();
	}
}


