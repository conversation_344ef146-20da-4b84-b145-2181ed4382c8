//package com.huatek.frame.modules.business.utils.excel;
//
//import java.lang.reflect.Field;
//import java.lang.reflect.Method;
//import java.lang.reflect.ParameterizedType;
//import java.lang.reflect.Type;
//import java.util.ArrayList;
//import java.util.List;
//
//import com.huatek.frame.annotation.FieldRules;
//import com.huatek.frame.common.exception.ServiceException;
//import com.huatek.frame.common.utils.StringUtils;
//import com.huatek.frame.enums.YesOrNo;
//
///**
// * @Description: 数据校验类
// * <AUTHOR>
// * @Since JDK 1.8
// * @Version V1.0
// * @Date:2023年2月9日 上午10:49:31
// * Copyright (c) 2023, www.huatek.com All Rights Reserved.
// */
//
//public class EntityValidator {
//
//	private static List<String> BASIC_CHAR_TYPE = new ArrayList<String>();
//
//	private static List<String> BASIC_NUM_TYPE = new ArrayList<String>();
//
//	private static List<String> BASIC_OTHER_TYPE = new ArrayList<String>();
//
//	public static final String COLLECTION_TYPE = "list";
//
//	static {
//		BASIC_CHAR_TYPE.add("string");
//		BASIC_NUM_TYPE.add("long");
//		BASIC_NUM_TYPE.add("int");
//		BASIC_NUM_TYPE.add("integer");
//		BASIC_NUM_TYPE.add("short");
//		BASIC_NUM_TYPE.add("double");
//		BASIC_NUM_TYPE.add("float");
//    	BASIC_OTHER_TYPE.add("boolean");
//    	BASIC_CHAR_TYPE.add("char");
//    	BASIC_OTHER_TYPE.add("date");
//    	BASIC_NUM_TYPE.add("bigdecimal");
//    }
//
//	@SuppressWarnings("unchecked")
//	public static void validate(Object obj) throws ServiceException{
//		Class<?> clazz = obj.getClass();
//		Field[] fields = clazz.getDeclaredFields();
//		for(Field field : fields){
//			String type = field.getType().getSimpleName().toLowerCase();
//			FieldRules fr = field.getAnnotation(FieldRules.class);
//			if(fr != null) {
//				Object value = null;
//				try {
//					Method method = clazz.getDeclaredMethod("get" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1));
//					value = method.invoke(obj);
//				} catch (Exception e) {
//				}
//				validateField(fr, value, type);
//			} else {
//				if(COLLECTION_TYPE.equals(type)) {
//					Type genericType = field.getGenericType();
//		            if (genericType != null && genericType instanceof ParameterizedType) {
//		                Type actualType = ((ParameterizedType) genericType).getActualTypeArguments()[0];
//		                String typeName = actualType.getTypeName();
//		                typeName = typeName.substring(typeName.lastIndexOf(".") + 1).toLowerCase();
//		                if(!BASIC_OTHER_TYPE.contains(typeName) && !BASIC_CHAR_TYPE.contains(typeName) && !BASIC_NUM_TYPE.contains(typeName)){
//		                	Object listObj = null;
//		                	try {
//		                		Method method = clazz.getDeclaredMethod("get" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1));
//		                		listObj = method.invoke(obj);
//							} catch (Exception e) {
//							}
//		                	if(listObj != null) {
//								List<Object> list = (List<Object>)listObj;
//								for(Object o : list) {
//									validate(o);
//								}
//							}
//		                }
//		            }
//				} else if(!BASIC_OTHER_TYPE.contains(type) && !BASIC_CHAR_TYPE.contains(type) && !BASIC_NUM_TYPE.contains(type)) {
//					Object subObj = null;
//					try {
//						Method method = clazz.getDeclaredMethod("get" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1));
//						subObj = method.invoke(obj);
//					} catch (Exception e) {
//					}
//					if(subObj != null) {
//						validate(subObj);
//					}
//				}
//			}
//		}
//	}
//
//	/**
//	 * 校验属性值
//	 * @param fr
//	 * @param value
//	 * @throws Exception
//	 */
//	private static void validateField(FieldRules fr, Object value, String type) throws ServiceException {
//		if(value == null) {
//			if(fr.allowNull() == YesOrNo.NO) {
//				throw new ServiceException(fr.fieldName() + "不能为空");
//			}
//		} else {
//			String v = value.toString().trim();
//			if(StringUtils.isEmpty(v)) {
//				if(fr.allowNull() == YesOrNo.NO) {
//					throw new ServiceException(fr.fieldName() + "不能为空");
//				}
//			} else {
//				if(BASIC_CHAR_TYPE.contains(type)) {
//					if(fr.maxLength() < v.length()) {
//						throw new ServiceException(fr.fieldName() + "最大长度为" + fr.maxLength());
//					}
//					if(fr.minLength() > v.length()) {
//						throw new ServiceException(fr.fieldName() + "最小长度为" + fr.minLength());
//					}
//					if(!StringUtils.isEmpty(fr.regex()) && !RegexUtil.isMatched(v, fr.regex())){
//						throw new ServiceException(fr.fieldName() + "不符合格式要求");
//					}
//				} else if(BASIC_NUM_TYPE.contains(type)) {
//					if(fr.maxValue() < Double.valueOf(v)) {
//						throw new ServiceException(fr.fieldName() + "最大值为" + fr.maxValue());
//					}
//					if(fr.minValue() > Double.valueOf(v)) {
//						throw new ServiceException(fr.fieldName() + "最小值为" + fr.minValue());
//					}
//				}
//			}
//		}
//	}
//}
//
//
