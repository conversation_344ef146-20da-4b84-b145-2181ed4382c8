package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;

/**
* @description 销售出库VO实体类
* <AUTHOR>
* @date 2025-08-06
**/
@Data
@ApiModel("销售出库DTO实体类")
public class SalesOutboundVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
	 * 报告编号
     **/
    @ApiModelProperty("报告编号")
    @Excel(name = "报告编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reportNumber;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;
    @ApiModelProperty("是否器件")
    @Excel(name = "是否器件",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String isQijian;
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 批次
     **/
    @ApiModelProperty("批次")
    @Excel(name = "批次",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String batch;

    /**
	 * 委托数量
     **/
    @ApiModelProperty("委托数量")
    @Excel(name = "委托数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String quantityOfCommissionedItems;

    /**
	 * 筛选数量
     **/
    @ApiModelProperty("筛选数量")
    @Excel(name = "筛选数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String quantityOfScreenedItems;

    /**
	 * 合格数量
     **/
    @ApiModelProperty("合格数量")
    @Excel(name = "合格数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String qualifiedQuantity;

    /**
	 * 不合格数量
     **/
    @ApiModelProperty("不合格数量")
    @Excel(name = "不合格数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String unqualifiedQuantity;

    /**
	 * 不合格原因
     **/
    @ApiModelProperty("不合格原因")
    @Excel(name = "不合格原因",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reasonForNonQuality;

    /**
	 * 工程代码
     **/
    @ApiModelProperty("工程代码")
    @Excel(name = "工程代码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String engineeringCode;

    /**
	 * 报告
     **/
    @ApiModelProperty("报告")
    @Excel(name = "报告",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String report;

    /**
	 * 送检人
     **/
    @ApiModelProperty("送检人")
    @Excel(name = "送检人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String inspectionSender;

    /**
	 * 客户收件人
     **/
    @ApiModelProperty("客户收件人")
    @Excel(name = "客户收件人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipientCustomer;

    /**
	 * 收件电话
     **/
    @ApiModelProperty("收件电话")
    @Excel(name = "收件电话",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipientPhone;

    /**
	 * 收件人地址
     **/
    @ApiModelProperty("收件人地址")
    @Excel(name = "收件人地址",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String recipientAddress;

    /**
	 * 快递公司
     **/
    @ApiModelProperty("快递公司")
    @Excel(name = "快递公司",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String expressCompany;

    /**
	 * 快递单号
     **/
    @ApiModelProperty("快递单号")
    @Excel(name = "快递单号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String expressWaybillNumber;

    /**
	 * 发货日期
     **/
    @ApiModelProperty("发货日期")
    @Excel(name = "发货日期",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String shippingDate;

    //备注
    @ApiModelProperty("备注")
    @Excel(name = "备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String remark;

    @ApiModelProperty("订单送检编号")
    @Excel(name = "订单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String orderInspectionNumber;

    @ApiModelProperty("工单送检编号")
    @Excel(name = "工单送检编号",
            cellType = Excel.ColumnType.STRING,
            type = Excel.Type.ALL)
    private String workOrderInspectionNumber;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}