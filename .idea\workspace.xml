<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="fix bug  7318 待办任务中，对于生产和可靠性班组长或者操作员展示流转到当前班组任务">
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/impl/DicDetailServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/java/com/huatek/frame/modules/system/service/impl/DicDetailServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-rpc/src/main/java/com/huatek/frame/modules/system/service/DicDetailService.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-rpc/src/main/java/com/huatek/frame/modules/system/service/DicDetailService.java" afterDir="false" />
    </list>
    <list id="640866c8-be23-4972-8da4-c676321340e3" name="ignore-on-commit" comment="">
      <change afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target" afterDir="true" />
      <change beforePath="$PROJECT_DIR$/.idea/compiler.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/compiler.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/encodings.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/encodings.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-admin/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/core/model/JobInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/core/model/JobInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/service/XxlJobForeignService.class" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/classes/com/huatek/job/admin/service/XxlJobForeignService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/maven-status/maven-compiler-plugin/compile/default-compile/inputFiles.lst" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/torch-job-rpc-2.4.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/huatek-torch-job/torch-job-rpc/target/torch-job-rpc-2.4.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/nacos/j-x-m-e-s/j-x-m-e-s-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/doc/torch-basic-application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-basic-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-basic/jx-mes-data-permission-sdk/src/main/java/com/huatek/frame/modules/conf/RedissonConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/doc/torch-gateway-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-gateway/src/main/resources/bootstrap-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-openapi/target/jx-mes-openapi-1.0.0.jar" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-openapi/target/jx-mes-openapi-1.0.0.jar" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/jx-mes-tool/doc/compass-tool-application-dev.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/context/SecurityContextHolder.class" root0="SKIP_INSPECTION" />
    <setting file="jar://$PROJECT_DIR$/../../../../../../../../../../tools/mavenRep/com/huatek/torch/huatek-torch-common-core/2.1.9/huatek-torch-common-core-2.1.9.jar!/com/huatek/frame/common/utils/SecurityUser.class" root0="SKIP_INSPECTION" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="C:\tools\mavenRep" />
        <option name="userSettingsFile" value="C:\tools\apache-maven-3.8.4-bin\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zJXIm52poQbdHVZYIVkXliym0G" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.huatek-torch-job [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.huatek-torch-job [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes [validate].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes-business-rpc [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.jx-mes-business-rpc [install].executor&quot;: &quot;Run&quot;,
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;1.8&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Work/C3S/compass/svn/JunXin/JunXin2025Code/trunk/02Engineering/04Implementation/jx-mes-code/jx-mes-business/jx-mes-business-application/src/main/resources&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.4&quot;,
    &quot;service.view.auto.scroll.to.source&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;editor.preferences.tabs&quot;,
    &quot;应用程序.AwaitingProductionOrderServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.BasicApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.BusinessApplication.executor&quot;: &quot;Debug&quot;,
    &quot;应用程序.DateTimeUtil.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.GatewayApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.JobAdminApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.JobExecutorApplication.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.OutputValueReportServiceImpl.executor&quot;: &quot;Run&quot;,
    &quot;应用程序.ProductionValueExcelExport.executor&quot;: &quot;Run&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\resources" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\resources\template" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\resources" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\rest" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business\service\mapping" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\resources\image" />
      <recent name="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code\jx-mes-business\jx-mes-business-application\src\main\java\com\huatek\frame\modules\business" />
    </key>
    <key name="ExtractSuperBase.RECENT_KEYS">
      <recent name="com.huatek.frame.modules.system.service.impl" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.domain.vo" />
    </key>
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.huatek.frame.modules.business.domain.vo" />
      <recent name="com.huatek.frame.modules.business.service.dto" />
      <recent name="com.huatek.frame.modules.business.conf" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="Application" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="应用程序.DateTimeUtil">
    <configuration default="true" type="Application" factoryName="Application">
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BasicApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="ALTERNATIVE_JRE_PATH" value="1.8" />
      <option name="ALTERNATIVE_JRE_PATH_ENABLED" value="true" />
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BasicApplication" />
      <module name="jx-mes-basic-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="BusinessApplication" type="Application" factoryName="Application">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.BusinessApplication" />
      <module name="jx-mes-business-application" />
      <shortenClasspath name="MANIFEST" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="DateTimeUtil" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.modules.business.utils.DateTimeUtil" />
      <module name="jx-mes-business-application" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.modules.business.utils.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="GatewayApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.frame.GatewayApplication" />
      <module name="jx-mes-gateway" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.frame.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobAdminApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.admin.JobAdminApplication" />
      <module name="torch-job-admin" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.admin.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="JobExecutorApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.huatek.job.executor.JobExecutorApplication" />
      <module name="torch-job-executor" />
      <shortenClasspath name="MANIFEST" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.huatek.job.executor.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="应用程序.BusinessApplication" />
      <item itemvalue="应用程序.DateTimeUtil" />
      <item itemvalue="应用程序.GatewayApplication" />
      <item itemvalue="应用程序.BasicApplication" />
      <item itemvalue="应用程序.JobAdminApplication" />
      <item itemvalue="应用程序.JobExecutorApplication" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="应用程序.DateTimeUtil" />
        <item itemvalue="应用程序.GatewayApplication" />
        <item itemvalue="应用程序.BasicApplication" />
        <item itemvalue="应用程序.JobExecutorApplication" />
        <item itemvalue="应用程序.JobAdminApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
          <option name="myCopyRoot" value="C:\Work\C3S\compass\svn\JunXin\JunXin2025Code\trunk\02Engineering\04Implementation\jx-mes-code" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="ece3a5c4-1e66-4c67-89e5-01661dba50b2" name="Changes" comment="" />
      <created>1751450073556</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751450073556</updated>
    </task>
    <task id="LOCAL-00147" summary="fix bug  能力评审，评审结果为“不通过”时，消息通知 订单所属客户经理 和调度，不应该通知“市场部客户经理”角色">
      <option name="closed" value="true" />
      <created>1758177580705</created>
      <option name="number" value="00147" />
      <option name="presentableId" value="LOCAL-00147" />
      <option name="project" value="LOCAL" />
      <updated>1758177580705</updated>
    </task>
    <task id="LOCAL-00148" summary="fix bug  能力评审，评审结果为“不通过”时，消息通知 订单所属客户经理 和调度，不应该通知“市场部客户经理”角色">
      <option name="closed" value="true" />
      <created>1758177595835</created>
      <option name="number" value="00148" />
      <option name="presentableId" value="LOCAL-00148" />
      <option name="project" value="LOCAL" />
      <updated>1758177595835</updated>
    </task>
    <task id="LOCAL-00149" summary="fix bug  取同设备通过完成状态过滤">
      <option name="closed" value="true" />
      <created>1758177727893</created>
      <option name="number" value="00149" />
      <option name="presentableId" value="LOCAL-00149" />
      <option name="project" value="LOCAL" />
      <updated>1758177727893</updated>
    </task>
    <task id="LOCAL-00150" summary="fix bug  更新aps状态 第一道工序开始和最后一道工序完成">
      <option name="closed" value="true" />
      <created>1758185237109</created>
      <option name="number" value="00150" />
      <option name="presentableId" value="LOCAL-00150" />
      <option name="project" value="LOCAL" />
      <updated>1758185237109</updated>
    </task>
    <task id="LOCAL-00151" summary="fix bug">
      <option name="closed" value="true" />
      <created>1758188810384</created>
      <option name="number" value="00151" />
      <option name="presentableId" value="LOCAL-00151" />
      <option name="project" value="LOCAL" />
      <updated>1758188810384</updated>
    </task>
    <task id="LOCAL-00152" summary="fix bug 产值核算:XXX工单的XXX工序核算结果为0，请注意查看！">
      <option name="closed" value="true" />
      <created>1758188979013</created>
      <option name="number" value="00152" />
      <option name="presentableId" value="LOCAL-00152" />
      <option name="project" value="LOCAL" />
      <updated>1758188979014</updated>
    </task>
    <task id="LOCAL-00153" summary="fix bug 追加共用工单">
      <option name="closed" value="true" />
      <created>1758195555686</created>
      <option name="number" value="00153" />
      <option name="presentableId" value="LOCAL-00153" />
      <option name="project" value="LOCAL" />
      <updated>1758195555686</updated>
    </task>
    <task id="LOCAL-00154" summary="fix bug 适用设备类型编码">
      <option name="closed" value="true" />
      <created>1758196002773</created>
      <option name="number" value="00154" />
      <option name="presentableId" value="LOCAL-00154" />
      <option name="project" value="LOCAL" />
      <updated>1758196002774</updated>
    </task>
    <task id="LOCAL-00155" summary="fix bug">
      <option name="closed" value="true" />
      <created>1758200221439</created>
      <option name="number" value="00155" />
      <option name="presentableId" value="LOCAL-00155" />
      <option name="project" value="LOCAL" />
      <updated>1758200221439</updated>
    </task>
    <task id="LOCAL-00156" summary="fix bug 能力评审">
      <option name="closed" value="true" />
      <created>1758263102065</created>
      <option name="number" value="00156" />
      <option name="presentableId" value="LOCAL-00156" />
      <option name="project" value="LOCAL" />
      <updated>1758263102065</updated>
    </task>
    <task id="LOCAL-00157" summary="fix bug 扫码枪">
      <option name="closed" value="true" />
      <created>1758264314365</created>
      <option name="number" value="00157" />
      <option name="presentableId" value="LOCAL-00157" />
      <option name="project" value="LOCAL" />
      <updated>1758264314365</updated>
    </task>
    <task id="LOCAL-00158" summary="fix bug 扫码枪">
      <option name="closed" value="true" />
      <created>1758270763534</created>
      <option name="number" value="00158" />
      <option name="presentableId" value="LOCAL-00158" />
      <option name="project" value="LOCAL" />
      <updated>1758270763534</updated>
    </task>
    <task id="LOCAL-00159" summary="fix bug 删除前置工序执行顺序赋值  这个字段已作废">
      <option name="closed" value="true" />
      <created>1758275974699</created>
      <option name="number" value="00159" />
      <option name="presentableId" value="LOCAL-00159" />
      <option name="project" value="LOCAL" />
      <updated>1758275974699</updated>
    </task>
    <task id="LOCAL-00160" summary="fix bug 关联工单的前置工序多选">
      <option name="closed" value="true" />
      <created>1758276727384</created>
      <option name="number" value="00160" />
      <option name="presentableId" value="LOCAL-00160" />
      <option name="project" value="LOCAL" />
      <updated>1758276727384</updated>
    </task>
    <task id="LOCAL-00161" summary="fix bug 生产厂家">
      <option name="closed" value="true" />
      <created>1758421789400</created>
      <option name="number" value="00161" />
      <option name="presentableId" value="LOCAL-00161" />
      <option name="project" value="LOCAL" />
      <updated>1758421789400</updated>
    </task>
    <task id="LOCAL-00162" summary="fix bug scanner_gun_number忽略大小写匹配">
      <option name="closed" value="true" />
      <created>1758421894941</created>
      <option name="number" value="00162" />
      <option name="presentableId" value="LOCAL-00162" />
      <option name="project" value="LOCAL" />
      <updated>1758421894941</updated>
    </task>
    <task id="LOCAL-00163" summary="fix bug 客户工序id 新增反馈">
      <option name="closed" value="true" />
      <created>1758439533343</created>
      <option name="number" value="00163" />
      <option name="presentableId" value="LOCAL-00163" />
      <option name="project" value="LOCAL" />
      <updated>1758439533343</updated>
    </task>
    <task id="LOCAL-00164" summary="fix bug 7147">
      <option name="closed" value="true" />
      <created>1758444070756</created>
      <option name="number" value="00164" />
      <option name="presentableId" value="LOCAL-00164" />
      <option name="project" value="LOCAL" />
      <updated>1758444070756</updated>
    </task>
    <task id="LOCAL-00165" summary="fix bug 7147">
      <option name="closed" value="true" />
      <created>1758444266433</created>
      <option name="number" value="00165" />
      <option name="presentableId" value="LOCAL-00165" />
      <option name="project" value="LOCAL" />
      <updated>1758444266433</updated>
    </task>
    <task id="LOCAL-00166" summary="fix bug 未找到多工序PDA计算规则对应的生产任务">
      <option name="closed" value="true" />
      <created>1758506571246</created>
      <option name="number" value="00166" />
      <option name="presentableId" value="LOCAL-00166" />
      <option name="project" value="LOCAL" />
      <updated>1758506571246</updated>
    </task>
    <task id="LOCAL-00167" summary="fix bug &#10;新增外协 aps &#10;任务 外协暂停  &#10;&#10;外协审批通过 已外协  任务更新为已外协，aps 更新计划为进行中&#10;&#9;&#9;&#9;&#9;&#9;&#10;外协验收   完成  更新任务为完成 如当前任务为最后一个任务，计划也更新为完成">
      <option name="closed" value="true" />
      <created>1758519011089</created>
      <option name="number" value="00167" />
      <option name="presentableId" value="LOCAL-00167" />
      <option name="project" value="LOCAL" />
      <updated>1758519011089</updated>
    </task>
    <task id="LOCAL-00168" summary="fix bug 产值结算  结算单位">
      <option name="closed" value="true" />
      <created>1758520131073</created>
      <option name="number" value="00168" />
      <option name="presentableId" value="LOCAL-00168" />
      <option name="project" value="LOCAL" />
      <updated>1758520131073</updated>
    </task>
    <task id="LOCAL-00169" summary="fix bug 产值计算不合格数量 合格数量不合格工序从实验结果里取">
      <option name="closed" value="true" />
      <created>1758522365734</created>
      <option name="number" value="00169" />
      <option name="presentableId" value="LOCAL-00169" />
      <option name="project" value="LOCAL" />
      <updated>1758522365734</updated>
    </task>
    <task id="LOCAL-00170" summary="fix bug 导入板卡的bug">
      <option name="closed" value="true" />
      <created>1758524702582</created>
      <option name="number" value="00170" />
      <option name="presentableId" value="LOCAL-00170" />
      <option name="project" value="LOCAL" />
      <updated>1758524702583</updated>
    </task>
    <task id="LOCAL-00171" summary="fix bug 导出功能，检查导出文件内容，字段存在如下问题：1、缺少字段：订单送检编号、工单送检编号 ；2、多余字段：更新人">
      <option name="closed" value="true" />
      <created>1758525233700</created>
      <option name="number" value="00171" />
      <option name="presentableId" value="LOCAL-00171" />
      <option name="project" value="LOCAL" />
      <updated>1758525233700</updated>
    </task>
    <task id="LOCAL-00172" summary="fix bug 【需求变更】待入库列表需增加”入库时间“字段；已入库列表需增加”确认入库时间“字段。">
      <option name="closed" value="true" />
      <created>1758526105076</created>
      <option name="number" value="00172" />
      <option name="presentableId" value="LOCAL-00172" />
      <option name="project" value="LOCAL" />
      <updated>1758526105077</updated>
    </task>
    <task id="LOCAL-00173" summary="fix bug 【需求变更】针对从能力核验、能力评审模块”转开发“来的数据，在修改页面，其”基本信息“板块的内容，除”任务描述“、”适用试验类型“、”适用设备类型“三个字段外，其余字段均不允许进行修改，应该回填来源数据值。">
      <option name="closed" value="true" />
      <created>1758527196679</created>
      <option name="number" value="00173" />
      <option name="presentableId" value="LOCAL-00173" />
      <option name="project" value="LOCAL" />
      <updated>1758527196679</updated>
    </task>
    <task id="LOCAL-00174" summary="fix bug 产值计算批不用乘以数量">
      <option name="closed" value="true" />
      <created>1758529436295</created>
      <option name="number" value="00174" />
      <option name="presentableId" value="LOCAL-00174" />
      <option name="project" value="LOCAL" />
      <updated>1758529436295</updated>
    </task>
    <task id="LOCAL-00175" summary="fix bug">
      <option name="closed" value="true" />
      <created>1758530361120</created>
      <option name="number" value="00175" />
      <option name="presentableId" value="LOCAL-00175" />
      <option name="project" value="LOCAL" />
      <updated>1758530361121</updated>
    </task>
    <task id="LOCAL-00176" summary="fix bug   下发订单评审">
      <option name="closed" value="true" />
      <created>1758531362687</created>
      <option name="number" value="00176" />
      <option name="presentableId" value="LOCAL-00176" />
      <option name="project" value="LOCAL" />
      <updated>1758531362687</updated>
    </task>
    <task id="LOCAL-00177" summary="fix bug   测评订单，订单评审页面，针对仅产生一条能力评审记录的数据，当其“评审结果”为“通过”时，“处理结果”字段显示内容未翻译。">
      <option name="closed" value="true" />
      <created>1758531972040</created>
      <option name="number" value="00177" />
      <option name="presentableId" value="LOCAL-00177" />
      <option name="project" value="LOCAL" />
      <updated>1758531972040</updated>
    </task>
    <task id="LOCAL-00178" summary="fix bug   系统自动评审通过的数据，列表字段“评审人”、“评审时间”显示为空。">
      <option name="closed" value="true" />
      <created>1758532367851</created>
      <option name="number" value="00178" />
      <option name="presentableId" value="LOCAL-00178" />
      <option name="project" value="LOCAL" />
      <updated>1758532367852</updated>
    </task>
    <task id="LOCAL-00179" summary="fix bug   客户核算价格">
      <option name="closed" value="true" />
      <created>1758536112980</created>
      <option name="number" value="00179" />
      <option name="presentableId" value="LOCAL-00179" />
      <option name="project" value="LOCAL" />
      <updated>1758536112980</updated>
    </task>
    <task id="LOCAL-00180" summary="fix bug   需求变更】产值计算明细页面，列表“试验项目”字段名称改为“标准工序名称”；增加“客户工序名称”字段、查询条件增加：标准工序名称、客户工序名称。">
      <option name="closed" value="true" />
      <created>1758543212720</created>
      <option name="number" value="00180" />
      <option name="presentableId" value="LOCAL-00180" />
      <option name="project" value="LOCAL" />
      <updated>1758543212720</updated>
    </task>
    <task id="LOCAL-00181" summary="fix bug   experimentProject">
      <option name="closed" value="true" />
      <created>1758543268557</created>
      <option name="number" value="00181" />
      <option name="presentableId" value="LOCAL-00181" />
      <option name="project" value="LOCAL" />
      <updated>1758543268557</updated>
    </task>
    <task id="LOCAL-00182" summary="fix bug   PDA计算任务点击开始提示任务开始失败，期望任务开始后状态为进行中，报工页面上展示pda计算规则和pda计算的结果。具体如下图，提交后不审批。">
      <option name="closed" value="true" />
      <created>1758594303599</created>
      <option name="number" value="00182" />
      <option name="presentableId" value="LOCAL-00182" />
      <option name="project" value="LOCAL" />
      <updated>1758594303599</updated>
    </task>
    <task id="LOCAL-00183" summary="fix bug   mes call aps 导出">
      <option name="closed" value="true" />
      <created>1758612480304</created>
      <option name="number" value="00183" />
      <option name="presentableId" value="LOCAL-00183" />
      <option name="project" value="LOCAL" />
      <updated>1758612480304</updated>
    </task>
    <task id="LOCAL-00184" summary="fix bug   注释扫码 的权限">
      <option name="closed" value="true" />
      <created>1758612705079</created>
      <option name="number" value="00184" />
      <option name="presentableId" value="LOCAL-00184" />
      <option name="project" value="LOCAL" />
      <updated>1758612705079</updated>
    </task>
    <task id="LOCAL-00185" summary="fix bug   aps调用报错">
      <option name="closed" value="true" />
      <created>1758614307191</created>
      <option name="number" value="00185" />
      <option name="presentableId" value="LOCAL-00185" />
      <option name="project" value="LOCAL" />
      <updated>1758614307191</updated>
    </task>
    <task id="LOCAL-00186" summary="fix bug">
      <option name="closed" value="true" />
      <created>1758615126699</created>
      <option name="number" value="00186" />
      <option name="presentableId" value="LOCAL-00186" />
      <option name="project" value="LOCAL" />
      <updated>1758615126699</updated>
    </task>
    <task id="LOCAL-00187" summary="fix bug  gtm+8">
      <option name="closed" value="true" />
      <created>1758619162997</created>
      <option name="number" value="00187" />
      <option name="presentableId" value="LOCAL-00187" />
      <option name="project" value="LOCAL" />
      <updated>1758619163048</updated>
    </task>
    <task id="LOCAL-00188" summary="fix bug   实际开始时间">
      <option name="closed" value="true" />
      <created>1758619272467</created>
      <option name="number" value="00188" />
      <option name="presentableId" value="LOCAL-00188" />
      <option name="project" value="LOCAL" />
      <updated>1758619272467</updated>
    </task>
    <task id="LOCAL-00189" summary="fix bug  7401 报工页面上完成时间需增加时间，可选择时分。">
      <option name="closed" value="true" />
      <created>1758621433591</created>
      <option name="number" value="00189" />
      <option name="presentableId" value="LOCAL-00189" />
      <option name="project" value="LOCAL" />
      <updated>1758621433591</updated>
    </task>
    <task id="LOCAL-00190" summary="fix bug  7004">
      <option name="closed" value="true" />
      <created>1758680321015</created>
      <option name="number" value="00190" />
      <option name="presentableId" value="LOCAL-00190" />
      <option name="project" value="LOCAL" />
      <updated>1758680321015</updated>
    </task>
    <task id="LOCAL-00191" summary="fix bug  合格数量不合格已完成都默认为null">
      <option name="closed" value="true" />
      <created>1758681211140</created>
      <option name="number" value="00191" />
      <option name="presentableId" value="LOCAL-00191" />
      <option name="project" value="LOCAL" />
      <updated>1758681211140</updated>
    </task>
    <task id="LOCAL-00192" summary="fix bug  增加工单id">
      <option name="closed" value="true" />
      <created>1758681840991</created>
      <option name="number" value="00192" />
      <option name="presentableId" value="LOCAL-00192" />
      <option name="project" value="LOCAL" />
      <updated>1758681840991</updated>
    </task>
    <task id="LOCAL-00193" summary="fix bug  分单">
      <option name="closed" value="true" />
      <created>1758683239545</created>
      <option name="number" value="00193" />
      <option name="presentableId" value="LOCAL-00193" />
      <option name="project" value="LOCAL" />
      <updated>1758683239545</updated>
    </task>
    <task id="LOCAL-00194" summary="fix bug  分单">
      <option name="closed" value="true" />
      <created>1758683923244</created>
      <option name="number" value="00194" />
      <option name="presentableId" value="LOCAL-00194" />
      <option name="project" value="LOCAL" />
      <updated>1758683923244</updated>
    </task>
    <task id="LOCAL-00195" summary="fix bug  7318 待办任务中，对于生产和可靠性班组长或者操作员展示流转到当前班组任务">
      <option name="closed" value="true" />
      <created>1758693683251</created>
      <option name="number" value="00195" />
      <option name="presentableId" value="LOCAL-00195" />
      <option name="project" value="LOCAL" />
      <updated>1758693683251</updated>
    </task>
    <option name="localTasksCounter" value="196" />
    <servers />
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="fix bug 产值计算不合格数量 合格数量不合格工序从实验结果里取" />
    <MESSAGE value="fix bug 导入板卡的bug" />
    <MESSAGE value="fix bug 导出功能，检查导出文件内容，字段存在如下问题：1、缺少字段：订单送检编号、工单送检编号 ；2、多余字段：更新人" />
    <MESSAGE value="fix bug 【需求变更】待入库列表需增加”入库时间“字段；已入库列表需增加”确认入库时间“字段。" />
    <MESSAGE value="fix bug 【需求变更】针对从能力核验、能力评审模块”转开发“来的数据，在修改页面，其”基本信息“板块的内容，除”任务描述“、”适用试验类型“、”适用设备类型“三个字段外，其余字段均不允许进行修改，应该回填来源数据值。" />
    <MESSAGE value="fix bug 产值计算批不用乘以数量" />
    <MESSAGE value="fix bug   下发订单评审" />
    <MESSAGE value="fix bug   测评订单，订单评审页面，针对仅产生一条能力评审记录的数据，当其“评审结果”为“通过”时，“处理结果”字段显示内容未翻译。" />
    <MESSAGE value="fix bug   系统自动评审通过的数据，列表字段“评审人”、“评审时间”显示为空。" />
    <MESSAGE value="fix bug   客户核算价格" />
    <MESSAGE value="fix bug   需求变更】产值计算明细页面，列表“试验项目”字段名称改为“标准工序名称”；增加“客户工序名称”字段、查询条件增加：标准工序名称、客户工序名称。" />
    <MESSAGE value="fix bug   experimentProject" />
    <MESSAGE value="fix bug   PDA计算任务点击开始提示任务开始失败，期望任务开始后状态为进行中，报工页面上展示pda计算规则和pda计算的结果。具体如下图，提交后不审批。" />
    <MESSAGE value="fix bug   mes call aps 导出" />
    <MESSAGE value="fix bug   注释扫码 的权限" />
    <MESSAGE value="fix bug   aps调用报错" />
    <MESSAGE value="fix bug" />
    <MESSAGE value="fix bug  gtm+8" />
    <MESSAGE value="fix bug   实际开始时间" />
    <MESSAGE value="fix bug  7401 报工页面上完成时间需增加时间，可选择时分。" />
    <MESSAGE value="fix bug  7004" />
    <MESSAGE value="fix bug  合格数量不合格已完成都默认为null" />
    <MESSAGE value="fix bug  增加工单id" />
    <MESSAGE value="fix bug  分单" />
    <MESSAGE value="fix bug  7318 待办任务中，对于生产和可靠性班组长或者操作员展示流转到当前班组任务" />
    <option name="LAST_COMMIT_MESSAGE" value="fix bug  7318 待办任务中，对于生产和可靠性班组长或者操作员展示流转到当前班组任务" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/huatek-torch-job/torch-job-core/src/main/java/com/huatek/job/core/executor/XxlJobExecutor.java</url>
          <line>67</line>
          <option name="timeStamp" value="23" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/huatek-torch-job/torch-job-executor/src/main/java/com/huatek/job/executor/service/jobhandler/SampleXxlJob.java</url>
          <line>56</line>
          <option name="timeStamp" value="24" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/common/utils/ProductionValueExcelExport.java</url>
          <line>104</line>
          <option name="timeStamp" value="42" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>3587</line>
          <option name="timeStamp" value="65" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1989</line>
          <option name="timeStamp" value="126" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1982</line>
          <option name="timeStamp" value="128" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>1339</line>
          <option name="timeStamp" value="145" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/CapabilityReviewServiceImpl.java</url>
          <line>561</line>
          <option name="timeStamp" value="153" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/CapabilityReviewServiceImpl.java</url>
          <line>584</line>
          <option name="timeStamp" value="155" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductListServiceImpl.java</url>
          <line>861</line>
          <option name="timeStamp" value="164" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>1049</line>
          <option name="timeStamp" value="203" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/CapabilityReviewServiceImpl.java</url>
          <line>663</line>
          <option name="timeStamp" value="232" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/CapabilityReviewServiceImpl.java</url>
          <line>688</line>
          <option name="timeStamp" value="234" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/SalesOutboundController.java</url>
          <line>67</line>
          <option name="timeStamp" value="248" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/ProductInventoryController.java</url>
          <line>110</line>
          <option name="timeStamp" value="249" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/rest/ProductInventoryController.java</url>
          <line>130</line>
          <option name="timeStamp" value="250" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>1495</line>
          <option name="timeStamp" value="278" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionValueCalculationServiceImpl.java</url>
          <line>1233</line>
          <option name="timeStamp" value="281" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductListServiceImpl.java</url>
          <line>1030</line>
          <option name="timeStamp" value="282" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductListServiceImpl.java</url>
          <line>1063</line>
          <option name="timeStamp" value="283" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductListServiceImpl.java</url>
          <line>1090</line>
          <option name="timeStamp" value="284" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionValueCalculationServiceImpl.java</url>
          <line>1030</line>
          <option name="timeStamp" value="285" />
        </line-breakpoint>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionValueCalculationServiceImpl.java</url>
          <line>1122</line>
          <option name="timeStamp" value="286" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionValueCalculationServiceImpl.java</url>
          <line>1115</line>
          <option name="timeStamp" value="287" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionValueCalculationServiceImpl.java</url>
          <line>807</line>
          <option name="timeStamp" value="290" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>2054</line>
          <option name="timeStamp" value="321" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>2578</line>
          <option name="timeStamp" value="322" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1155</line>
          <option name="timeStamp" value="327" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/AwaitingProductionOrderServiceImpl.java</url>
          <line>2590</line>
          <option name="timeStamp" value="329" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1199</line>
          <option name="timeStamp" value="330" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1265</line>
          <option name="timeStamp" value="331" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/jx-mes-business/jx-mes-business-application/src/main/java/com/huatek/frame/modules/business/service/impl/ProductionTaskServiceImpl.java</url>
          <line>1266</line>
          <option name="timeStamp" value="333" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
    <watches-manager>
      <configuration name="Application">
        <watch expression="QueryWrapper wrapper = new QueryWrapper();&#10;&#9;&#9;&#9;wrapper.eq(&quot;work_order&quot;,productionOrderDTO.getId());&#10;&#9;&#9;&#9;CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);&#10;&#9;&#9;&#9;CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();&#10;&#9;&#9;&#9;customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());&#10;&#9;&#9;&#9;wrapper.eq(&quot;CODEX_TORCH_MASTER_FORM_ID&quot;,scheme.getId());&#10;&#9;&#9;&#9;List&lt;CustomerExperimentProjectVO&gt; customerEperimentProjects= customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);&#10;&#9;&#9;&#9;" />
        <watch expression="该记录已加入能力资产，不能进行完成操作" />
        <watch expression="   /**&#10;//&#9; * 能力编号&#10;//     **/&#10;//    @ApiModelProperty(&quot;能力编号&quot;)&#10;//    @Excel(name = &quot;能力编号&quot;,&#10;//        cellType = Excel.ColumnType.STRING,&#10;//        type = Excel.Type.ALL)&#10;//    private String capabilityNumber;" />
      </configuration>
    </watches-manager>
  </component>
</project>