package com.huatek.frame.modules.business.utils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;

public class DateTimeUtil {

    // 格式化器（接口传过来的格式：yyyy-MM-dd HH:mm:ss）
    private static final DateTimeFormatter FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    // 解析接口时间（无时区，按东八区解析）
    public static LocalDateTime parseAsShanghai(String timeStr) {
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr, FORMATTER);
        // 指定东八区，避免被当成UTC
        return ZonedDateTime.of(localDateTime, ZoneId.of("Asia/Shanghai")).toLocalDateTime();
    }

    // 转换为 Timestamp 存 MySQL
    public static Timestamp toTimestamp(String timeStr) {
        LocalDateTime localDateTime = parseAsShanghai(timeStr);
        return Timestamp.valueOf(localDateTime);
    }

    // 从数据库读出来（Timestamp → String）
    public static String formatFromDb(Timestamp ts) {
        if (ts == null) return null;
        return ts.toLocalDateTime().format(FORMATTER);
    }

    // 转换为 UTC 格式，便于接口传输（可选）
    public static String toUtcString(String timeStr) {
        LocalDateTime localDateTime = parseAsShanghai(timeStr);
        Instant instant = localDateTime.atZone(ZoneId.of("Asia/Shanghai")).toInstant();
        return instant.toString(); // e.g. 2025-09-20T00:30:00Z
    }

    //校验日期格式
    public static boolean isValidDate(String dateStr, String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        sdf.setLenient(false);
        try {
            sdf.parse(dateStr);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    public static void main(String[] args) {
        String rawTime = "2025-09-20 08:30:00";

        // 解析并存库
        Timestamp ts = DateTimeUtil.toTimestamp(rawTime);
        System.out.println("存库时间 = " + ts); // 保持 2025-09-20 08:30:00

        // 从库里读出
        String dbTime = DateTimeUtil.formatFromDb(ts);
        System.out.println("数据库读出 = " + dbTime);

        // 转UTC（可选）
        String utcStr = DateTimeUtil.toUtcString(rawTime);
        System.out.println("UTC字符串 = " + utcStr);
    }
}
