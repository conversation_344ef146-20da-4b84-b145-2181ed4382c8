package com.huatek.frame.modules.business.service.impl;


import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Time;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.modules.bpm.constant.ApprovalStatus;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.bpm.dto.ProcessFormDTO;
import com.huatek.frame.modules.bpm.service.ProcessInstanceProxyService;
import com.huatek.frame.modules.bpm.service.ProcessPrivilegeService;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.business.utils.DocxToPdfConverter;
import com.huatek.frame.modules.business.utils.IDCardExcelExporter;
import com.huatek.frame.modules.business.utils.excel.ExcelUtil;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.modules.system.domain.SysGroup;
import com.huatek.frame.modules.system.domain.SysProcessRecord;
import com.huatek.frame.modules.system.domain.vo.*;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.system.service.*;
import com.huatek.frame.modules.system.service.SysProcessRecordService;
import com.huatek.tool.modules.poi.ReportDocxTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.util.StringUtil;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.servlet.http.HttpServletResponse;
import java.net.URLDecoder;


import static com.huatek.frame.modules.constant.DicConstant.ProductionOrder.NON_POWER_AGING;
import static com.huatek.frame.modules.constant.DicConstant.ProductionOrder.NON_SCREENED;


/**
 * 待制工单 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "awaitingProductionOrder")
//@RefreshScope
@Slf4j
public class AwaitingProductionOrderServiceImpl implements AwaitingProductionOrderService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

	private String processBusinessKey = "待制工单";

	@DubboReference
	private ProcessInstanceProxyService processInstanceProxyService;
	@DubboReference
	private ProcessPrivilegeService processPrivilegeService;
	@Autowired
	private CustomerInformationManagementMapper customerInformationManagementMapper;
	@Autowired
	private ProductionOrderOperationHistoryMapper productionOrderOperationHistoryMapper;
	@Autowired
    private ProductListMapper productListMapper;
	@Autowired
	private ProductManagementMapper productManagementMapper;
	@DubboReference
    private SysGroupService sysGroupService;
	@DubboReference
	private DicDetailService dicDetailService;
	@Autowired
	private CapabilityReviewService capabilityReviewService;
	@DubboReference
	private CommonFileService commonFileService;
	@Autowired
	private EvaluationOrderMapper evaluationOrderMapper;
	@Autowired
	private CustomerProcessSchemeMapper customerProcessSchemeMapper;
	@Autowired
	private CustomerExperimentProjectMapper customerExperimentProjectMapper;
	@Autowired
	private CustomerExperimentProjectDataMapper customerExperimentProjectDataMapper;
	@Autowired
	private StandardProcessManagementMapper standardProcessManagementMapper;
	@Autowired
	private CodeManagementService codeManagementService;
	@Autowired
	private ExperimentProjectMapper experimentProjectMapper;
	@Autowired
	private ExperimentProjectDataMapper experimentProjectDataMapper;
	@Autowired
	private WorkstationMapper workstationMapper;
	@Autowired
	private ProductInformationManagementMapper productInformationManagementMapper;

	@Autowired
	private OutsourcingService outsourcingService;
	@DubboReference
	private SysProcessRecordService sysProcessRecordService;
	@Autowired
	private ProductionTaskService productionTaskService;
	@Autowired
	private ProductionOrderResultService productionOrderResultService;
	@Autowired
	private ProductInventoryService productInventoryService;
	@Autowired
	private ProductInventoryMapper productInventoryMapper;
	@Autowired
	private ProductionOrderProcessTestMapper productionOrderProcessTestMapper;
	@Autowired
	private FileReviewService fileReviewService;
	@Autowired
	private EquipmentInventoryMapper equipmentInventoryMapper;
	@Autowired
	private DeviceTraceabilityMapper deviceTraceabilityMapper;
	@Autowired
	private ReportManagementMapper reportManagementMapper ;
	@Autowired
	private StandardSpecificationMapper standardSpecificationMapper;
	@DubboReference
	private SysUserService sysUserService;
	@Autowired
	private AttachmentService attachmentService;
	@Autowired
	private ProductionOrderResultMapper productionOrderResultMapper ;
	@Autowired
	private OutsourcingMapper outsourcingMapper;
	@Autowired
	private MessageManagementService messageManagementService;

	@Autowired
	private ProdTaskOpHistMapper prodTaskOpHistMapper;

	@DubboReference
	private RoleService roleService;

	@Autowired
	private ProductionTaskMapper productionTaskMapper;
	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();




	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ProductionOrderVO>> findAwaitingProductionOrderPage(ProductionOrderDTO dto) {
		String currentUser = SecurityContextHolder.getCurrentUserName();
		//Codex - 流程人员流程查看数据权限控制
		if(!processPrivilegeService.hasViewProcessPrivilege(dto.getId(),currentUser,processBusinessKey)){
			log.info("用户无权限查看流程, currentUser:{},processBusinessKey:{}",currentUser,processBusinessKey);
			TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
			response.getData().setData(new ArrayList<>());
			response.setStatus(Constant.REQUEST_SUCCESS);
			response.getData().setCount(0L);
			return response;
		}
		//查询当前用户角色
		List<String> roles = awaitingProductionOrderMapper.selectCurrentUserRoles(SecurityContextHolder.getCurrentUserId());
		if(dto.getMenuType().equals("awaiting")&& !currentUser.equals(ProcessConstant.SUPER_USER)){
			if(!roles.contains(DicConstant.Role.ROLE_KEKAOXING_DIAODU)&& !roles.contains(DicConstant.Role.ROLE_KEKAOXING_ZHIDAN)
			&&!roles.contains(DicConstant.Role.ROLE_SHENGCHAN_DIAODU)&& !roles.contains(DicConstant.Role.ROLE_SHENGCHAN_ZHIDAN)){
				dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserId());
			}
		}
		if(dto.getMenuType().equals("complete")&& !currentUser.equals(ProcessConstant.SUPER_USER)){
			if(!roles.contains(DicConstant.Role.ROLE_KEKAOXING_DIAODU)&& !roles.contains(DicConstant.Role.ROLE_KEKAOXING_GENERATEREPORT)
			&&!roles.contains(DicConstant.Role.ROLE_SHENGCHAN_DIAODU)&& !roles.contains(DicConstant.Role.ROLE_SHENGCHAN_GENERATEREPORT)){
				dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserId());
			}
		}
		if(dto.getMenuType().equals("production")&& !currentUser.equals(ProcessConstant.SUPER_USER)){
			if(!roles.contains(DicConstant.Role.ROLE_KEKAOXING_DIAODU)&& !roles.contains(DicConstant.Role.ROLE_SHENGCHAN_DIAODU)){
				dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserId());
			}
		}

		//Codex - 流程人员流程查看数据权限控制
		 if (dto.getWorkflowQueryRole().equals("applicant")){
			if(currentUser.equals(ProcessConstant.SUPER_USER)){
				dto.setCodexTorchApplicant("");
			}else{
				dto.setCodexTorchApplicant(currentUser);
			}
		}else if(dto.getWorkflowQueryRole().equals("approver")){
			 //角色权限
			 String currentUserId = SecurityContextHolder.getCurrentUserId();
			 //===========新增代码============
			 List<RoleVO> roleUserVoList = roleService.findUserRoles(currentUserId);
			 String[] currentUserRolesArray = new String[roleUserVoList.size()];
			 int i = 0;
			 for(RoleVO roleUserVo :roleUserVoList){
				 currentUserRolesArray[i++] =roleUserVo.getRole();
			 }
			 String currentUserRoles = Arrays.stream(Optional.of(currentUserRolesArray).orElse(new String[]{""})).collect(Collectors.joining(", "));
			 //==============================
			//多审批人处理
			dto.setCodexTorchApprover(null);
			dto.setCodexTorchApprovers(currentUserId);
			 dto.setCodexTorchApproverRole(currentUserRolesArray[0]);
			 dto.setCodexTorchApproverRoles(currentUserRolesArray[0]);
			if(StringUtils.isEmpty(dto.getCodexTorchApprovalStatus()) || currentUser.equals(ProcessConstant.SUPER_USER)) {
				dto.setCodexTorchApprovalStatus("待审批|已审批|已驳回");
			}else {
				dto.setCodexTorchApprovalStatus(dto.getCodexTorchApprovalStatus());
			}
		}
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ProductionOrderVO> awaitingProductionOrders = awaitingProductionOrderMapper.selectAwaitingProductionOrderPage(dto);
		awaitingProductionOrders.stream().forEach(item -> {
			//处理标准规范
			if(!ObjectUtils.isEmpty(item.getStandardSpecificationId())){
				String[]  ids = item.getStandardSpecificationId().split(",");
				List<StandardSpecification> list = new ArrayList<>();
				for(String id : ids){
					StandardSpecification standardSpecification = standardSpecificationMapper.selectById(id);
					list.add(standardSpecification);
				}
				item.setStandardSpecifications(list);
			}
			//处理前置工单
			List<String> preOrdes = new ArrayList<>();
			if(StringUtils.isNotEmpty(item.getPredecessorWorkOrder())){
				String[] preOrderIds = item.getPredecessorWorkOrder().split(",");
				for(String preOrderId:preOrderIds){
					ProductionOrder preOrder = awaitingProductionOrderMapper.selectById(preOrderId);
					preOrdes.add(preOrder.getWorkOrderNumber());
				}
			}
			item.setPredecessorWorkOrderNumber(String.join(",",preOrdes));
		});
		//CodeX - 多级审批
		updateApproveStatusForCurrentUser(awaitingProductionOrders,SecurityContextHolder.getCurrentUserId());
		TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
		response.getData().setData(awaitingProductionOrders);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount(awaitingProductionOrders.getTotal());
		return response;
	}

	/**
	 * 多级审批，不同人看到的审批状态不同
	 *
	 * @param updateStatusVOVOList
	 * @param currentUser
	 */
	private void updateApproveStatusForCurrentUser(List<ProductionOrderVO> updateStatusVOVOList,String currentUser){
		for(ProductionOrderVO updateStatusVO : updateStatusVOVOList){
			String currentApprover = updateStatusVO.getCodexTorchApprover();
			String approvers = updateStatusVO.getCodexTorchApprovers();
			//===========新增代码============
			String currentApproverRole = updateStatusVO.getCodexTorchApproverRole();
			//============================
			if(StringUtils.isEmpty(approvers)){
				continue;
			}

			//当前用户不在审批人之列，不能处理记录
			if(!approvers.contains(currentUser)){
				continue;
			}

			//当前用户是当前审批人,审批状态是待审批，不需要更新状态
			if(currentApprover.contains(currentUser)
					&& updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
				continue;
			}

			//===========新增代码============
			//当前用户有审批角色,审批状态是待审批，不需要更新状态
			String currentUserId = SecurityContextHolder.getCurrentUserId();
			List<RoleVO> roleUserVoList = roleService.findUserRoles(currentUserId);
			if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())&& StringUtils.isNotEmpty(currentApproverRole)) {
				boolean currentUserHasApprovedRole = false;
				for (RoleVO roleUserVO : roleUserVoList) {
					String currentRole = roleUserVO.getRole();
					if (currentApproverRole.equals(currentRole)) {
						currentUserHasApprovedRole = true;
						break;
					}
				}

				if (currentUserHasApprovedRole) {
					continue;
				}
			}
			//================

			//如果当前用户处在当前审批者之前，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && (approvers.indexOf(currentApprover) > approvers.indexOf(currentUser))){
				if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
					updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
				}
			}

			//如果当前用户在已审批用户中但不是最后一个审批用户，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && !approvers.endsWith(currentUser)) {
				if (approvers.contains(currentUser)) {
					if (updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
						updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
					}
				}
			}
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ProductionOrderDTO awaitingProductionOrderDto) throws ParseException {

//        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(awaitingProductionOrderDto.getCodexTorchDeleted())) {
            awaitingProductionOrderDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
		String id = awaitingProductionOrderDto.getId();
		if (HuatekTools.isEmpty(id)) {
			throw new ServiceException("工单主键不能为空");
		}
		ProductionOrder entity = awaitingProductionOrderMapper.selectById(id);

        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		if(StringUtils.isNotEmpty(awaitingProductionOrderDto.getEstimatedCompletionTime())){
			entity.setEstimatedCompletionTime(sdf.parse(awaitingProductionOrderDto.getEstimatedCompletionTime()));
		}else{
			entity.setEstimatedCompletionTime(null);
		}
		entity.setPredecessorWorkOrder(awaitingProductionOrderDto.getPredecessorWorkOrder());
		entity.setRelatedWorkOrder(awaitingProductionOrderDto.getRelatedWorkOrder());
		entity.setPda(awaitingProductionOrderDto.getPda());
		entity.setPackageForm(awaitingProductionOrderDto.getPackageForm());
		entity.setWhetherToIncludeInScheduling(awaitingProductionOrderDto.getWhetherToIncludeInScheduling());
		entity.setQuantity(awaitingProductionOrderDto.getQuantity());
		entity.setAttachment(awaitingProductionOrderDto.getAttachment());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(entity);
		UpdateWrapper updateWrapper = new UpdateWrapper();
		updateWrapper.eq("id", entity.getId());
		updateWrapper.set("estimated_completion_time", entity.getEstimatedCompletionTime());
		awaitingProductionOrderMapper.update(entity,updateWrapper);
		//如果有绑定的工序方案 则同步修改工单工序方案的pda和封装形式
		CustomerProcessScheme sheme = customerProcessSchemeMapper.selectByProductionOrder(entity.getId());
		if(!ObjectUtils.isEmpty(sheme)){
			sheme.setPda(awaitingProductionOrderDto.getPda());
			sheme.setPackageForm(awaitingProductionOrderDto.getPackageForm());
			customerProcessSchemeMapper.updateById(sheme);
		}
		TorchResponse response = new TorchResponse();
        ProductionOrderVO vo = new ProductionOrderVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionOrderVO> findAwaitingProductionOrder(String id) {
		ProductionOrderVO vo = new ProductionOrderVO();
		if (!HuatekTools.isEmpty(id)) {
			vo= awaitingProductionOrderMapper.selectProductionOrderById(id);
			ProductionOrderViewVO viewVO =new ProductionOrderViewVO();
			//处理前置工单
			List<SelectOptionsVO> preOrdes = new ArrayList<>();
			if(StringUtils.isNotEmpty(vo.getPredecessorWorkOrder())){
				String[] preOrderIds = vo.getPredecessorWorkOrder().split(",");
				for(String preOrderId:preOrderIds){
					ProductionOrder preOrder = awaitingProductionOrderMapper.selectById(preOrderId);
					SelectOptionsVO optionsVO = new SelectOptionsVO();
					optionsVO.setValue(preOrderId);
					optionsVO.setLabel(preOrder.getWorkOrderNumber());
					preOrdes.add(optionsVO);
				}
			}
			vo.setPredecessorWorkOrders(preOrdes);

			//处理试验项目
			if(!ObjectUtils.isEmpty(vo.getExperimentProject())){
				this.execuateExperimentProject(vo,viewVO);
				vo.setExperimentProject(viewVO.getExperimentProject());
			}

			//处理标准规范
			if(!ObjectUtils.isEmpty(vo.getStandardSpecificationId())){
				this.execuateStandardSpecification(vo,viewVO);
				vo.setStandardSpecifications(viewVO.getStandardSpecifications());
			}

			if(HuatekTools.isEmpty(vo)) {
				throw new ServiceException("查询失败");
			}
			//BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ProductionOrderVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ProductionOrderViewVO> findAwaitingProductionOrderView(String id) {
		ProductionOrderViewVO vo = new ProductionOrderViewVO();
		if (!HuatekTools.isEmpty(id)) {
			ProductionOrderVO ordervo= awaitingProductionOrderMapper.selectProductionOrderById(id);
			BeanUtils.copyProperties(ordervo,vo);
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("work_order_number", ordervo.getSourceOrder());
			ProductionOrder sourceOrder = awaitingProductionOrderMapper.selectOne(wrapper);
			if(!ObjectUtils.isEmpty(sourceOrder)) {
				vo.setSourceOrderId(sourceOrder.getId());
			}
			//处理前置工单
			List<SelectOptionsVO> preOrdes = new ArrayList<>();
			if(StringUtils.isNotEmpty(ordervo.getPredecessorWorkOrder())){
				String[] preOrderIds = ordervo.getPredecessorWorkOrder().split(",");
				for(String preOrderId:preOrderIds){
					ProductionOrder preOrder = awaitingProductionOrderMapper.selectById(preOrderId);
					SelectOptionsVO optionsVO = new SelectOptionsVO();
					optionsVO.setValue(preOrderId);
					optionsVO.setLabel(preOrder.getWorkOrderNumber());
					preOrdes.add(optionsVO);
				}
			}
			vo.setPredecessorWorkOrders(preOrdes);
			this.execuateExperimentProject(ordervo,vo);
			//处理标准规范
			this.execuateStandardSpecification(ordervo,vo);
			wrapper.clear();
			wrapper.eq("work_order_number", ordervo.getWorkOrderNumber());
			wrapper.eq("report_status",DicConstant.ReportManagement.REPORT_STATUS_APPROVED);
			ReportManagement reportManagement = reportManagementMapper.selectOne(wrapper);
			if(!ObjectUtils.isEmpty(reportManagement)){
				vo.setReport(reportManagement.getReportNumber());
				vo.setReportPath(reportManagement.getAttachment());
			}
			//查询操作历史
			List<ProductionOrderOperationHistoryVO> histories = productionOrderOperationHistoryMapper.selectByProductOrder(ordervo.getId());
			vo.setOperatHistory(histories);
			//查询工单任务数据
			List<ProductionTaskViewVO> taskVOS = productionTaskService.selectProductionTaskByProductionOrder(ordervo.getWorkOrderNumber());
			if(!CollectionUtils.isEmpty(taskVOS)){
				vo.setExperimentProjects(taskVOS);
			}
		}
		TorchResponse<ProductionOrderViewVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	private void execuateStandardSpecification(ProductionOrderVO ordervo, ProductionOrderViewVO vo) {
		if(StringUtils.isNotEmpty(ordervo.getStandardSpecificationId())){
			String[]  ids = ordervo.getStandardSpecificationId().split(",");
			List<StandardSpecification> list = new ArrayList<>();
			for(String id : ids){
				StandardSpecification standardSpecification = standardSpecificationMapper.selectById(id);
				list.add(standardSpecification);
			}
			vo.setStandardSpecifications(list);
		}
	}

	private void execuateExperimentProject(ProductionOrderVO ordervo, ProductionOrderViewVO vo) {
		if(!StringUtils.isEmpty(ordervo.getDpaTestProject())){
			String[] testProject = ordervo.getDpaTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_dpaTestProject");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
		if(!StringUtils.isEmpty(ordervo.getSpecialAnalysisTestProject())){
			String[] testProject = ordervo.getSpecialAnalysisTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_specialAnalysisTestProject");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
		if(!StringUtils.isEmpty(ordervo.getInspectionTestProject())){
			String[] testProject = ordervo.getInspectionTestProject().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_inspectionTestProject");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
		if(!StringUtils.isEmpty(ordervo.getQualityConsistencyTestItems())){
			String[] testProject = ordervo.getQualityConsistencyTestItems().split(",");
			TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("product_list_qualityConsistencyTestItems");
			List<Map<String,String>> dicDetails = response.getData().getData();
			StringBuilder resultBuilder = new StringBuilder();
			for(String dicCode : testProject){
				String trimmedCode = dicCode.trim();
				for(Map<String,String> detail : dicDetails){
					if(trimmedCode.equals(detail.get("value"))){
						if(resultBuilder.length() > 0){
							resultBuilder.append(",");
						}
						resultBuilder.append(detail.get("label"));
					}
				}
			}
			vo.setExperimentProject(resultBuilder.toString());
		}
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ProductionOrder> awaitingProductionOrderList = awaitingProductionOrderMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionOrder awaitingProductionOrder : awaitingProductionOrderList) {
            awaitingProductionOrder.setCodexTorchDeleted(Constant.DEFAULT_YES);
            awaitingProductionOrderMapper.updateById(awaitingProductionOrder);
        }
		//awaitingProductionOrderMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("orderNumber",awaitingProductionOrderMapper::selectOptionsByOrderNumber);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("entrustedUnit",awaitingProductionOrderMapper::selectOptionsByEntrustedUnit);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("productModel",awaitingProductionOrderMapper::selectOptionsByProductModel);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("predecessorWorkOrder",awaitingProductionOrderMapper::selectOptionsByPredecessorWorkOrder);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("relatedWorkOrder",awaitingProductionOrderMapper::selectOptionsByRelatedWorkOrder);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("responsiblePerson",awaitingProductionOrderMapper::selectOptionsByResponsiblePerson);
			selectOptionsFuncMap.put("processCode3",awaitingProductionOrderMapper::selectOptionsByProcessCode3);
//			selectOptionsFuncMap.put("assoWoPredProc",assoWoPredProc->awaitingProductionOrderMapper.selectOptionsByAssoWoPredProc(orderId));
			selectOptionsFuncMap.put("workstation",awaitingProductionOrderMapper::selectOptionsByWorkstation);
			selectOptionsFuncMap.put("deviceType",awaitingProductionOrderMapper::selectOptionsByDeviceType);
			selectOptionsFuncMap.put("productInformation1",awaitingProductionOrderMapper::selectOptionsByProductInformation1);
//			selectOptionsFuncMap.put("testingTeam",awaitingProductionOrderMapper::selectOptionsByTestingTeam);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }
  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}

	public TorchResponse getRelateOrderPreProcess(String orderIds){
		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse();
		ProductionOrder order = awaitingProductionOrderMapper.selectById(orderIds);
		if(!StringUtils.isEmpty(order.getRelatedWorkOrder())){
			Page<SelectOptionsVO>  selectOptionsVOs = awaitingProductionOrderMapper.selectOptionsByAssoWoPredProc(order.getRelatedWorkOrder());
			response.getData().setData(selectOptionsVOs);
			response.getData().setCount(selectOptionsVOs.getTotal());
		}
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse<ProductionOrderInfoVO> getDetailByOrderNumber(String productionWorkOrderNumber) {
		//查询工单相关信息
		ProductionOrderInfoVO productionOrderInfoVO = awaitingProductionOrderMapper.getDetailByOrderNumber(productionWorkOrderNumber);
		if (productionOrderInfoVO == null){
			throw new ServiceException(String.format("工单编号为 %s 的生产工单不存在", productionWorkOrderNumber));
		}
		//查询工单工序相关信息
		List<ProductionTaskInfoVO> processes = productionTaskMapper.findProcessesByWorkOrderNumber(productionWorkOrderNumber);

		//回填processes的outsourcingProcess字段
		processes.forEach(item -> {
			String outsourcingProcess = item.getCustomerProcessName() + "_" + item.getDisplayNumber();
			item.setOutsourcingProcess(outsourcingProcess);
		});
		productionOrderInfoVO.setProcesses(processes);
		TorchResponse<ProductionOrderInfoVO> response = new TorchResponse<>();
		response.getData().setData(productionOrderInfoVO);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional
	public TorchResponse approve(FormApprovalDTO formApprovalDTO, String token) {
		log.info("表单审批,formApprovalDTO:{}",formApprovalDTO);
		String operate ="";
		ProcessFormDTO processFormDTO = new ProcessFormDTO();
		BeanUtils.copyProperties(formApprovalDTO,processFormDTO);
		processFormDTO.setBusinessKey(processBusinessKey);
		SysProcessRecordVO processRecordResp = processInstanceProxyService.approve(processFormDTO,token);

		ProductionOrder oldOrder = awaitingProductionOrderMapper.selectById(processRecordResp.getFormId());

		ProductionOrder updateProcessStatusEntity = new ProductionOrder();
		updateProcessStatusEntity.setId(processRecordResp.getFormId());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());
		//======新增代码========
		updateProcessStatusEntity.setCodexTorchApproverRole(processRecordResp.getApproverRole());
		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
		if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.PENDING_REAPPLY.getName()))
			updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT);
			operate="工单审批驳回";
		if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.APPROVED.getName())){
			if(StringUtils.isEmpty(oldOrder.getWhetherToIncludeInScheduling())){
				updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
				operate="工单审批通过";
			}else{
				if(oldOrder.getWhetherToIncludeInScheduling().equals(DicConstant.CommonDic.DIC_YES)){
					updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED_SCHEDUL);
					operate="工单审批通过(待排产)";
				}else{
					updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
					operate="工单审批通过)";
				}
			}
		}


		updateProcessStatusEntity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		updateProcessStatusEntity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(updateProcessStatusEntity);

		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(updateProcessStatusEntity.getId());
		history.setOperate(operate);
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		log.info("表单审批完成,processRecordResp:{}",processRecordResp);
		//审批通过后将该工单绑定的工序方案设置为可查询
		CustomerProcessScheme customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(updateProcessStatusEntity.getId());
		customerProcessScheme.setStatus(DicConstant.CommonDic.DIC_YES);
		customerProcessSchemeMapper.updateById(customerProcessScheme);
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
//	@Transactional(rollbackFor = Exception.class)
	public TorchResponse apply(ProductionOrderDTO awaitingProductionOrderDto, String token) {
		log.info("表单提交申请,ProductionOrderDto:{}",awaitingProductionOrderDto);
		ProductionOrder order = awaitingProductionOrderMapper.selectById(awaitingProductionOrderDto.getId());
		JSONObject json = securityUser.currentUser(token);
		String currentUser = json.getString("userName");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE);
		order.setPreparedBy(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchApplicant(currentUser);
		order.setCodexTorchApprovalStatus(ApprovalStatus.PENDING_APPROVAL.getName());
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		//CodeX 表单工作流绑定
		ProcessFormDTO processFormDTO = new ProcessFormDTO();
		processFormDTO.setProcessDefinitionKey("SimpleApprovalProcess");
		processFormDTO.setBusinessKey(processBusinessKey);
		processFormDTO.setFormId(awaitingProductionOrderDto.getId());
		SysProcessRecordVO processRecordResp = processInstanceProxyService.startProcessByKey(processFormDTO,token);
		ProductionOrder updateProcessStatusEntity = new ProductionOrder();
		updateProcessStatusEntity.setId(awaitingProductionOrderDto.getId());
		updateProcessStatusEntity.setCodexTorchApplicant(processRecordResp.getApplicant());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());

		//更新审批人列表
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());
//		updateProcessStatusEntity.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE);
		//=====新增代码=========
		updateProcessStatusEntity.setCodexTorchApproverRole(processRecordResp.getApproverRole());
		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());

		awaitingProductionOrderMapper.updateById(updateProcessStatusEntity);
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
//		return updateVoResp;
	}

	@Override
	public TorchResponse getExperimentProjectByPlan(ExperimentProjectDTO dxperimentProjectDTO) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",dxperimentProjectDTO.getCodexTorchMasterFormId());
		List<ExperimentProject> list = experimentProjectMapper.selectList(wrapper);
		List<ExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			ExperimentProjectVO vo = new ExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessId());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			StandardSpecification standardSpecification=standardSpecificationMapper.selectById(x.getProductInformation1());
			SysGroup group = sysGroupService.findGroupsByCode(x.getTestingTeam());
			wrapper.clear();
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",x.getId());
			List<ExperimentProjectData> experimentProjectDatas = experimentProjectDataMapper.selectList(wrapper);
			List<ExperimentProjectDataVO> dataVOList = new ArrayList<>();
			experimentProjectDatas.stream().forEach(y->{
				ExperimentProjectDataVO dataVO = new ExperimentProjectDataVO();
				BeanUtils.copyProperties(y,dataVO);
				dataVOList.add(dataVO);
			});
			vo.setProjectDataItems(dataVOList);
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(standardSpecification))
				vo.setProductInformation1Name(standardSpecification.getSpecificationName());
			if(!ObjectUtils.isEmpty(group))
				vo.setTestingTeamName(group.getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}
	@Override
	public TorchResponse getExperimentProjectByCustomerPlan(CustomerExperimentProjectDTO customerExperimentProjectDTO) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",customerExperimentProjectDTO.getCodexTorchMasterFormId());
		List<CustomerExperimentProject> list = customerExperimentProjectMapper.selectList(wrapper);
		List<CustomerExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			CustomerExperimentProjectVO vo = new CustomerExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessId());
			CustomerExperimentProject customerExperimentProject =customerExperimentProjectMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			StandardSpecification standardSpecification=standardSpecificationMapper.selectById(x.getProductInformation1());
			SysGroup group = sysGroupService.findGroupsByCode(x.getTestingTeam());
			wrapper.clear();
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",x.getId());
			List<CustomerExperimentProjectData> dataList = customerExperimentProjectDataMapper.selectList(wrapper);
			List<CustomerExperimentProjectDataVO> dataVOList = new ArrayList<>();
			dataList.stream().forEach(s->{
				CustomerExperimentProjectDataVO dataVO = new CustomerExperimentProjectDataVO();
                BeanUtils.copyProperties(s,dataVO);
				dataVOList.add(dataVO);
			});
			vo.setProjectDataItems(dataVOList);
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(customerExperimentProject))
				vo.setAssoWoPredProcName(customerExperimentProject.getCustomerProcessName());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(standardSpecification))
				vo.setProductInformation1Name(standardSpecification.getSpecificationName());
			if(!ObjectUtils.isEmpty(group))
				vo.setTestingTeamName(group.getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}

	@Override
	public TorchResponse getExperimentProjectByProcess(String[] ids) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("id",ids);
		List<StandardProcessManagement> list = standardProcessManagementMapper.selectList(wrapper);
		List<StandardProcessManagementVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			StandardProcessManagementVO vo = new StandardProcessManagementVO();
			BeanUtils.copyProperties(x,vo);
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			SysGroup group = sysGroupService.findGroupsByCode(x.getTestingTeam());
			vo.setProcessCode3Name(x.getProcessName2());
			vo.setProcessCode3(x.getStepNumber());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(group))
				vo.setTestingTeamName(group.getGroupName());
			voList.add(vo);
		});
		TorchResponse response = new TorchResponse();
		response.getData().setData(voList);
		return response;
	}

	@Override
	public TorchResponse restoreProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE))
			throw new ServiceException("工单不是暂停状态不能进行恢复操作");
		//判断是否有外协，外协是驳回的才能恢复
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("order_id",order.getId());
		wrapper.eq("entire_or_process",DicConstant.ProductionOrder.OUTSOURCING_TYPE_ENTIRE);
		Outsourcing outsourcing = outsourcingMapper.selectOne(wrapper);
		if(!ObjectUtils.isEmpty(outsourcing) && !outsourcing.getStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_REJECT)){
			throw new ServiceException("工单已外协，且外协申请未驳回不能恢复");
		}
		//查询工单操作历史
		wrapper.clear();
		wrapper.eq("production_order",order.getId());
		wrapper.orderByDesc("operate_time");
		List<ProductionOrderOperationHistory> operationHistories = productionOrderOperationHistoryMapper.selectList(wrapper);

		// 查找暂停操作记录
		Optional<ProductionOrderOperationHistory> pauseOperation = operationHistories.stream()
				.filter(history -> "暂停".equals(history.getOperate()) || "外协申请".equals(history.getOperate()))
				.findFirst();

		String previousStatus = null;

		if (pauseOperation.isPresent()) {
			ProductionOrderOperationHistory pauseHistory = pauseOperation.get();
			int pauseIndex = operationHistories.indexOf(pauseHistory);

			// 从暂停操作之后开始向前查找，找到第一个有效的操作记录
			for (int i = pauseIndex + 1; i < operationHistories.size(); i++) {
				ProductionOrderOperationHistory currentHistory = operationHistories.get(i);
				String operate = currentHistory.getOperate();

				// 根据操作类型确定对应的状态
				switch (operate) {
					case "绑定工序方案":
						previousStatus = DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT;
						break;
					case "工单审批通过":
						previousStatus = DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED;
						break;
					case "工单审批通过(待排产)":
						previousStatus = DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED_SCHEDUL;
						break;
					case "工单审批驳回":
						previousStatus = DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT;
						break;
					case "任务下发":
						previousStatus = DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS;
						break;
					default:
						continue; // 跳过不相关的操作
				}

				// 找到有效状态后跳出循环
				if (previousStatus != null) {
					break;
				}
			}
		}
		// 如果没有找到有效的前置状态，默认设置为准备状态
		if (previousStatus == null) {
			previousStatus = DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION;
		}
		order.setWorkOrderStatus(previousStatus);
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("暂停恢复");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse issueProductionOrderTask(ProductionOrderDTO productionOrderDTO) {
		List<ProductionTaskDTO> productionTaskList = new ArrayList<>();
		for(String id : productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)){
				throw new ServiceException("只有审批通过的工单才允许任务下发");
			}
			ProductList productList = productListMapper.selectById(order.getProduct());
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("work_order",productionOrderDTO.getOrderIds()[0]);
			CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(wrapper);
			CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();
			customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme.getId());
			List<CustomerExperimentProjectVO> customerEperimentProjects= customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);

			//查询当前工单的关联工单的工序方案
//			wrapper.clear();
//			List<CustomerExperimentProject> relateExperimentProject;
//			if(!StringUtils.isEmpty(order.getRelatedWorkOrder())){
//				wrapper.eq("work_order",order.getRelatedWorkOrder());
//				CustomerProcessScheme scheme1 = customerProcessSchemeMapper.selectOne(wrapper);
//				wrapper.clear();
//				wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme1.getId());
//				relateExperimentProject =customerExperimentProjectMapper.selectList(wrapper);
//			} else {
//				relateExperimentProject = new ArrayList<>();
//			}

			customerEperimentProjects.stream().forEach(x->{
				ProductionTaskDTO task = new ProductionTaskDTO();
				task.setWorkOrderNumber(order.getWorkOrderNumber());
				task.setInspectionQuantity2(x.getSampleQuantity12());
				task.setProcessName2(x.getProcessCode3Name());
				task.setProcessCode(x.getProcessCode3());
				task.setTestBasis(x.getTestBasis());
				task.setOrderNumber(order.getOrderNumber());
				task.setDisplayNumber(x.getDisplayNumber());
				task.setResponsiblePerson(order.getResponsiblePerson());
				task.setCustomerProcessName(x.getCustomerProcessName());
				task.setTestConditions(x.getTestConditions());
				task.setJudgmentCriteria(x.getJudgmentCriteria());
				task.setTicketLevel(productList.getTaskLevel());
				task.setScheduledStartTime(x.getEstimatedStartTime());
				task.setScheduledEndTime(x.getEstimatedEndTime());
				task.setProductName(productList.getProductName());
				task.setWorkstation(x.getWorkstation());
				task.setProductModel(productList.getProductModel());
				task.setProductCategory(productList.getProductCategory());
				task.setManufacturer(productList.getManufacturer());
				task.setBatchNumber(productList.getProductionBatch());
				task.setEntrustedUnit(scheme.getEntrustedUnit());
				task.setGrouping(x.getGrouping());
				task.setTestMethodology(x.getTestMethodology());
				task.setTestType(productList.getTestType());
				task.setExecutionSequence(x.getExecutionSequence());
				task.setCustomerExperimentProjectId(x.getId());
				//查询这个工单的关联工单工序方案
//				if(relateExperimentProject.size()>0){
//					Optional<CustomerExperimentProject> firstMatchedProject = 	relateExperimentProject.stream().filter(r->r.getAssoWoPredProc() !=null && r.getAssoWoPredProc().equals(x.getAssoWoPredProc())).findFirst();
//					if(firstMatchedProject.isPresent()){
//						task.setPreExecutionSequence(firstMatchedProject.get().getExecutionSequence());
//					}
//				}

				task.setProductInformation1(x.getProductInformation1());

				//获取是否审批
				StandardProcessManagement currentStandardProcessManagement = standardProcessManagementMapper.selectById(x.getProcessId());
				if(!ObjectUtils.isEmpty(currentStandardProcessManagement)){
					task.setIsApproval(currentStandardProcessManagement.getIsApproval());
				}
				task.setAssoWoPredProc(x.getAssoWoPredProc());
				task.setPredecessorWorkOrder(order.getPredecessorWorkOrder());
//				StandardProcessManagement standardProcessManagement = standardProcessManagementMapper.selectById(x.getAssoWoPredProc());
//				if(!ObjectUtils.isEmpty(standardProcessManagement)){
//					task.setAssoWoPredProc(standardProcessManagement.getProcessName2());
//				}
				ProductionOrder relateOrder = awaitingProductionOrderMapper.selectById(order.getRelatedWorkOrder());
				if(!ObjectUtils.isEmpty(relateOrder))
					task.setRelatedWorkOrder(order.getRelatedWorkOrder());
				task.setBelongingTeam2(x.getTestingTeam());
				if(StringUtils.isNotEmpty(x.getTestingTeam())){
					SysGroup testGroup = sysGroupService.findGroupsByCode(x.getTestingTeam());
					TorchResponse<SysGroupVO> group =sysGroupService.findGroup(testGroup.getGroupParentId());
					task.setDepartment(group.getData().getData().getId());
				}
				productionTaskList.add(task);
			});
			//保存操作历史
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("任务下发");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
            order.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_PRODUCTION);
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
		}
		productionTaskService.saveBatch(productionTaskList);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse getBindProcessSchemePlan(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		CustomerProcessScheme  customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(productionOrderDTO.getId());
		if(ObjectUtils.isEmpty(customerProcessScheme)){
			TorchResponse response = new TorchResponse();
			response.setStatus(Constant.REQUEST_SUCCESS);
			return response;
		}
		CustomerProcessSchemeVO schemeVO = new CustomerProcessSchemeVO();
		schemeVO.setPda(customerProcessScheme.getPda());
		schemeVO.setPackageForm(customerProcessScheme.getPackageForm());
		schemeVO.setComment(customerProcessScheme.getComment());
		schemeVO.setReportComment(customerProcessScheme.getReportComment());
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",customerProcessScheme.getId());
		List<CustomerExperimentProject> list = customerExperimentProjectMapper.selectList(wrapper);
		List<CustomerExperimentProjectVO> voList = new ArrayList<>();
		list.stream().forEach(x->{
			CustomerExperimentProjectVO vo = new CustomerExperimentProjectVO();
			BeanUtils.copyProperties(x,vo);
			if(null ==vo.getSampleQuantity12()){
				vo.setSampleQuantity12(order.getQuantity());
			}
			StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(x.getProcessId());
			CustomerExperimentProject customerExperimentProject =customerExperimentProjectMapper.selectById(x.getAssoWoPredProc());
			Workstation station = workstationMapper.selectById(x.getWorkstation());
			ProductInformationManagement productInformationManagement=productInformationManagementMapper.selectById(x.getProductInformation1());
			StandardSpecification standardSpecification=standardSpecificationMapper.selectById(x.getProductInformation1());
			SysGroup group = sysGroupService.findGroupsByCode(x.getTestingTeam());
			wrapper.clear();
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",x.getId());
			List<CustomerExperimentProjectData> dataList =customerExperimentProjectDataMapper.selectList(wrapper);
			List<CustomerExperimentProjectDataVO> dataVOList =new ArrayList<>();
			dataList.stream().forEach(f->{
				CustomerExperimentProjectDataVO dataVO = new CustomerExperimentProjectDataVO();
				BeanUtils.copyProperties(f,dataVO);
				dataVOList.add(dataVO);
			});
			vo.setProjectDataItems(dataVOList);
			if(!ObjectUtils.isEmpty(standardProcessManagement))
				vo.setProcessCode3Name(standardProcessManagement.getProcessName2());
			if(!ObjectUtils.isEmpty(customerExperimentProject))
				vo.setAssoWoPredProcName(customerExperimentProject.getCustomerProcessName());
			if(!ObjectUtils.isEmpty(station))
				vo.setWorkstationName(station.getWorkstationName());
			if(!ObjectUtils.isEmpty(productInformationManagement))
				vo.setProductInformation1Name(productInformationManagement.getFileName());
			if(!ObjectUtils.isEmpty(standardSpecification))
				vo.setProductInformation1Name(standardSpecification.getSpecificationName());
			if(!ObjectUtils.isEmpty(group))
				vo.setTestingTeamName(group.getGroupName());
			voList.add(vo);
		});
		schemeVO.setCustomerExperimentProjectItems(voList);
		TorchResponse response = new TorchResponse();
		response.getData().setData(schemeVO);
		return response;
	}

	@Override
	public TorchResponse getStandProcessForCustomerPlan(StandardProcessManagementDTO standardProcessManagementDTO) {
		StandardProcessManagement standardProcessManagement =standardProcessManagementMapper.selectById(standardProcessManagementDTO.getId());
		TorchResponse response = new TorchResponse();
		response.getData().setData(standardProcessManagement);
		return response;
	}

	@Override
	public TorchResponse copyProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(order.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
			throw new ServiceException("生产部工单不能复制");
		}
		//查询当前工单号拆分的最后一个工单
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.like("work_order_number",order.getWorkOrderNumber().split("-")[0]);
		wrapper.orderByDesc("work_order_number");
		List<ProductionOrder> orderlist =awaitingProductionOrderMapper.selectList(wrapper);
		if(productionOrderDTO.getCopyType().equals(DicConstant.ProductionOrder.COPY_TYPE_SAME)){
			// 同级复制：只有带有后缀的工单才能进行同级复制
			if (!order.getWorkOrderNumber().contains("-")) {
				throw new ServiceException("基础工单不能进行同级复制，只能进行子级复制");
			}
			order.setWorkOrderNumber(generateSameLevelNumber(orderlist, order));
		}
		if(productionOrderDTO.getCopyType().equals(DicConstant.ProductionOrder.COPY_TYPE_CHILD)){
			// 子级复制：所有工单都可以进行子级复制
			order.setWorkOrderNumber(generateChildLevelNumber(orderlist, order));
		}
		order.setId(null);
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION);
		order.setSoruceOrder(order.getWorkOrderNumber());
		order.setCodexTorchApplicant(null);
		order.setCodexTorchApprovers(null);
		order.setCodexTorchApprovalStatus(null);
		order.setCodexTorchApprover(null);
		order.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.insert(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("复制");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}


	// 同级复制生成编号方法
	private String generateSameLevelNumber(List<ProductionOrder> orderList, ProductionOrder sourceOrder) {
		String sourceNumber = sourceOrder.getWorkOrderNumber();
		String[] sourceParts = sourceNumber.split("-");

		// 获取父级前缀（去掉最后一级数字）
		String parentPrefix = String.join("-", Arrays.copyOf(sourceParts, sourceParts.length - 1));

		// 查找同一父级下的最大同级编号
		int maxSibling = 0;
		for (ProductionOrder order : orderList) {
			String workOrderNumber = order.getWorkOrderNumber();
			if (workOrderNumber.startsWith(parentPrefix + "-")) {
				String[] parts = workOrderNumber.split("-");
				// 确保是同一层级（与源工单层级相同）
				if (parts.length == sourceParts.length) {
					try {
						int currentNum = Integer.parseInt(parts[parts.length - 1]);
						maxSibling = Math.max(maxSibling, currentNum);
					} catch (NumberFormatException e) {
						// 忽略非数字后缀
					}
				}
			}
		}

		return parentPrefix + "-" + (maxSibling + 1);
	}

	// 子级复制生成编号方法
	private String generateChildLevelNumber(List<ProductionOrder> orderList, ProductionOrder sourceOrder) {
		String sourceNumber = sourceOrder.getWorkOrderNumber();

		// 查找当前工单下的最大子级编号
		int maxChild = 0;
		String childPrefix = sourceNumber + "-";

		for (ProductionOrder order : orderList) {
			String workOrderNumber = order.getWorkOrderNumber();
			if (workOrderNumber.startsWith(childPrefix)) {
				String[] parts = workOrderNumber.split("-");
				// 确保是直接子级（比源工单多一级）
				if (parts.length == sourceNumber.split("-").length + 1) {
					try {
						int currentNum = Integer.parseInt(parts[parts.length - 1]);
						maxChild = Math.max(maxChild, currentNum);
					} catch (NumberFormatException e) {
						// 忽略非数字后缀
					}
				}
			}
		}

		return sourceNumber + "-" + (maxChild + 1);
	}
	@Override
	@Transactional
	public TorchResponse splitProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		ProductList productlist = productListMapper.selectById(order.getProduct());
		if(productlist.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST) || productlist.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)){
			throw new ServiceException("试验类型为鉴定、质量一致性的工单不能进行拆分");
		}
		if(order.getTestsMethodology().equals(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING) || productlist.getTestType().equals(DicConstant.ProductionOrder.TEST_METHODLOGY_CANNOT_SCREENED)){
			throw new ServiceException("试验方式为外协、不可筛的工单不能进行拆分");
		}
		if(order.getTestsMethodology().equals(DicConstant.ProductionOrder.TEST_METHODLOGY_SELF)&& (order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)
		        || order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE)
				|| order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT)
				|| order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL)
				|| order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED)
				|| order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION)
				|| order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED_SCHEDUL))
		){
			throw new ServiceException("试验方式为自产且工单状态为草稿、待提交、待审批、驳回、取消、已外协、审批通过(待排产)的工单不能进行拆分");
		}
		List<SplitOrderProductionTaskDTO> splitList = new ArrayList<>();
		//将工单的数量设置为原始工单数量
		order.setQuantity(productionOrderDTO.getOldQquantity());
		//查询当前工单号拆分的最后一个工单
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.like("work_order_number",order.getWorkOrderNumber().split("-")[0]+"-");
		wrapper.orderByDesc("work_order_number");
		List<ProductionOrder> orderlist =awaitingProductionOrderMapper.selectList(wrapper);
		ProductionOrder lastOrder =order;
		if(!ObjectUtils.isEmpty(orderlist) && orderlist.size()>0){
			lastOrder = orderlist.get(0);
		}
		String lastOrderNumber = lastOrder.getWorkOrderNumber();
		String orderNumber="";

		SplitOrderProductionTaskDTO dto = new SplitOrderProductionTaskDTO();
		dto.setIsSplit(DicConstant.CommonDic.DEFAULT_ZERO);
		dto.setWorkOrderNumber(order.getWorkOrderNumber());
		dto.setInspectionQuantity(productionOrderDTO.getOldQquantity());
		splitList.add(dto);
		for(int i=0;i<productionOrderDTO.getSplitQuantity().length;i++){
			if(i==0){
				if(lastOrderNumber.contains("-")){
					orderNumber=lastOrderNumber.split("-")[0]+"-"+(Integer.valueOf(lastOrderNumber.split("-")[1])+1);
				}else{
					orderNumber=lastOrderNumber.split("-")[0]+"-1";
				}
			}else{
				orderNumber=orderNumber.split("-")[0]+"-"+(Integer.valueOf(orderNumber.split("-")[1])+1);
			}
			ProductionOrder newOrder = new ProductionOrder();
			BeanUtils.copyProperties(order,newOrder);
			newOrder.setId(null);
			newOrder.setQuantity(productionOrderDTO.getSplitQuantity()[i]);
			newOrder.setWorkOrderNumber(orderNumber);
			newOrder.setSoruceOrder(order.getWorkOrderNumber());
			awaitingProductionOrderMapper.insert(newOrder);
			//复制保存原工单绑定的方案
			CustomerProcessScheme  customerProcessScheme = customerProcessSchemeMapper.selectByProductionOrder(order.getId());
			QueryWrapper wrapper1 = new QueryWrapper();
			wrapper1.eq("CODEX_TORCH_MASTER_FORM_ID",customerProcessScheme.getId());
			List<CustomerExperimentProject> listproject = customerExperimentProjectMapper.selectList(wrapper1);
			CustomerProcessScheme newScheme = new CustomerProcessScheme();
			BeanUtils.copyProperties(customerProcessScheme,newScheme);
			newScheme.setWorkOrder(newOrder.getId());
			newScheme.setId(null);
			customerProcessSchemeMapper.insert(newScheme);
			listproject.stream().forEach(x->{
				CustomerExperimentProject newproject = new CustomerExperimentProject();
				BeanUtils.copyProperties(x,newproject);
				newproject.setId(null);
				newproject.setCodexTorchMasterFormId(newScheme.getId());
				customerExperimentProjectMapper.insert(newproject);
				wrapper1.clear();
				wrapper1.eq("CODEX_TORCH_MASTER_FORM_ID",x.getId());
				List<CustomerExperimentProjectData> listprojectData =customerExperimentProjectDataMapper.selectList(wrapper1);
				listprojectData.stream().forEach(s->{
					CustomerExperimentProjectData newprojectdata = new CustomerExperimentProjectData();
					BeanUtils.copyProperties(s,newprojectdata);
					newprojectdata.setId(null);
					newprojectdata.setCodexTorchMasterFormId(newproject.getId());
					customerExperimentProjectDataMapper.insert(newprojectdata);
				});
			});
			//调用工单任务分单接口拆分工单任务
			SplitOrderProductionTaskDTO newdto = new SplitOrderProductionTaskDTO();
			newdto.setIsSplit(DicConstant.CommonDic.DEFAULT_ONE);
			newdto.setWorkOrderNumber(orderNumber);
			newdto.setInspectionQuantity(productionOrderDTO.getSplitQuantity()[i]);
			splitList.add(newdto);
		}
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		productionTaskService.splitOrder(splitList);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("分单");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse cancelPdaWarning(ProductionOrderDTO productionOrderDTO) {
		for(String id :productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			order.setWmfcnr(DicConstant.CommonDic.DEFAULT_ZERO);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
			//保存操作历史
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("取消pda预警");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse pauseProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PROGRESS))
			throw new ServiceException("订单状态不是进行中不能进行暂停操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		//保存操作历史
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("暂停");
		history.setReason(productionOrderDTO.getOperatReason());
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse cancelProductionOrder(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED)||
		   order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_WAITAPPROVE)
		)
			throw new ServiceException("订单处于取消、完成、已外协、待审批状态不能进行取消操作");
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL);
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("取消");
		history.setReason(productionOrderDTO.getOperatReason());
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional
	public TorchResponse productionOrderInstore(ProductionOrderDTO productionOrderDTO) {
		/**
		 *一个工单产品和报告不允许重复入库。勾选多条，点击确定按钮时需要给出提示：本次入库内容存在重复入库。
		 * 鉴定和质量一致性工单选择一个工单后，与该工单前缀相同的工单做合并入库。合并规则：不合格品数量和不合格原因、不合格工序汇总或拼接，筛选数量取-1工单的数量，入库记录中工单编号不显示后缀。
		 * 其他试验类型不做合并入库。只做批量入库。合格数量取 最后一个工序的合格数量
		 *
		 * 勾选多个工单点击入库按钮，若勾选的工单有鉴定或者质量一致性工单则提示：鉴定或质量一致性工单会将所有子工单自动合并入库。
		 */
		//判断是否重复入库
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("id", productionOrderDTO.getOrderIds());
		List<ProductionOrderVO> orderVOs = awaitingProductionOrderMapper.selectAwaitingProductionOrderListByIds(Arrays.asList(productionOrderDTO.getOrderIds()));
		//鉴定和质量一致性工单合并
		List<String> testConsistenyOrder = new ArrayList<>();
		List<String> autoList = new ArrayList<>();
		for (ProductionOrderVO order : orderVOs) {
			if(order.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)||
					order.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)){
				String workOrderNumber = order.getWorkOrderNumber().split("-")[0];
				if(!testConsistenyOrder.contains(workOrderNumber))
					testConsistenyOrder.add(workOrderNumber);
			}else{
				autoList.add(order.getWorkOrderNumber());
			}
		}
		List<String> orderNumbers = new ArrayList<>();
		orderNumbers.addAll(testConsistenyOrder);orderNumbers.addAll(autoList);
		if(productionOrderDTO.getWhetherToEnterDocuments().equals(DicConstant.CommonDic.DEFAULT_ONE)){
			wrapper.clear();
			wrapper.eq("report",DicConstant.CommonDic.DEFAULT_ONE);
			wrapper.in("work_order_number", orderNumbers);
			List<ProductInventory> inventories = productInventoryMapper.selectList(wrapper);
			if(!CollectionUtils.isEmpty(inventories)&& inventories.size()>0){
				List<String> inventoryNumbers = inventories.stream().map(ProductInventory::getWorkOrderNumber).collect(Collectors.toList());
				throw  new ServiceException("本次入库"+String.join(",", inventoryNumbers)+"内容存在重复入库");
			}
			wrapper.clear();
			wrapper.in("work_order_number", orderNumbers);
			wrapper.eq("report_status",DicConstant.ReportManagement.REPORT_STATUS_APPROVED);
			wrapper.eq("codex_torch_deleted",DicConstant.CommonDic.DIC_NO);
			List<ReportManagement> reportManagements = reportManagementMapper.selectList(wrapper);
			Set<String> reprotOrderNumberSet = reportManagements.stream()
					.map(ReportManagement::getWorkOrderNumber)
					.collect(Collectors.toSet());				// 找出缺失的工单号
			List<String> missingOrderNumbers = orderNumbers.stream()
					.filter(orderNumber -> !reprotOrderNumberSet.contains(orderNumber))
					.collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(missingOrderNumbers)) {
				String missingNumbersStr = String.join(",", missingOrderNumbers);
				throw new ServiceException(String.format("以下工单未找到审批通过的报告: %s。不能进行入库操作",missingNumbersStr));
			}
		}
		if(productionOrderDTO.getWhetherToEnterComponents().equals(DicConstant.CommonDic.DEFAULT_ONE)){
			wrapper.clear();
			wrapper.eq("is_qijian",DicConstant.CommonDic.DEFAULT_ONE);
			wrapper.in("work_order_number", orderNumbers);
			List<ProductInventory> inventories = productInventoryMapper.selectList(wrapper);
			if(!CollectionUtils.isEmpty(inventories)&& inventories.size()>0){
				List<String> inventoryNumbers = inventories.stream().map(ProductInventory::getWorkOrderNumber).collect(Collectors.toList());
				throw  new ServiceException("本次入库"+String.join(",", inventoryNumbers)+"内容存在重复入库");
			}
		}
		this.instoreProductionOrderNoMerge(autoList,productionOrderDTO);
		this.instoreProductionOrderMerage(testConsistenyOrder,productionOrderDTO);

		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	private void instoreProductionOrderMerage(List<String> testConsistenyOrder, ProductionOrderDTO productionOrderDTO) {
		for(String orderNumber : testConsistenyOrder){
			//查询这个订单前缀一致的所有工单
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.likeRight("work_order_number", orderNumber);
			wrapper.orderByAsc("work_order_number");
			List<ProductionOrder> orders =awaitingProductionOrderMapper.selectList(wrapper);
			String childNumber=orderNumber+"-1";
			ProductionOrder childOrder = orders.stream().filter(x->x.getWorkOrderNumber().equals(childNumber)).findFirst().get();
			ProductInventoryDTO dto =createProductInventory(orderNumber,childOrder,productionOrderDTO.getWhetherToEnterDocuments(),productionOrderDTO.getWhetherToEnterComponents());
			String unProcessName = "";
			String unReason = "";
			Integer unqualifiedQuantitun=0;
			for(ProductionOrder order : orders){
				ProductInventoryDTO tempdto = new ProductInventoryDTO();
				getProductionProcessTestData(order.getWorkOrderNumber(),tempdto);
				unProcessName+=tempdto.getNonQualityProcess();
				unReason+=tempdto.getReasonForNonQuality();
				unqualifiedQuantitun+=tempdto.getUnqualifiedQuantity()==null?0:tempdto.getUnqualifiedQuantity();
				saveInstoreAndUpdate(order, productionOrderDTO.getWhetherToEnterComponents());
			}
			dto.setUnqualifiedQuantity(unqualifiedQuantitun);
			dto.setNonQualityProcess(unProcessName);
			dto.setReasonForNonQuality(unReason);
			Integer qualifiedQuntity = 0;
			if(dto.getUnqualifiedQuantity() != null && dto.getQuantityOfScreenedItems() != null){
				qualifiedQuntity=dto.getQuantityOfScreenedItems()-(dto.getUnqualifiedQuantity()==null?0:dto.getUnqualifiedQuantity());
			}
			dto.setQualifiedQuantity(qualifiedQuntity);
			productInventoryService.saveOrUpdate(dto);

		}
	}

	private void instoreProductionOrderNoMerge(List<String> autoList, ProductionOrderDTO productionOrderDTO) {
		for(String orderNumber : autoList){
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("work_order_number", orderNumber);
			ProductionOrder order = awaitingProductionOrderMapper.selectOne(wrapper);

			order.setWhetherToEnterComponents(productionOrderDTO.getWhetherToEnterComponents());
			order.setWhetherToEnterDocuments(productionOrderDTO.getWhetherToEnterDocuments());
			//TODO 调用入库接口保存入库数据，组装入库DTO
			ProductInventoryDTO dto =createProductInventory(order.getWorkOrderNumber(),order,productionOrderDTO.getWhetherToEnterDocuments(),productionOrderDTO.getWhetherToEnterComponents());
			this.getProductionProcessTestData(order.getWorkOrderNumber(),dto);
			productInventoryService.saveOrUpdate(dto);
			this.saveInstoreAndUpdate(order,productionOrderDTO.getWhetherToEnterComponents());
		}
	}

	private void saveInstoreAndUpdate(ProductionOrder order, String whetherToEnterComponents) {
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if(whetherToEnterComponents.equals(DicConstant.CommonDic.DIC_YES)){
			//确认入库的时候才更新
			//order.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_INSTORE);
		}
		awaitingProductionOrderMapper.updateById(order);
		ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
		history.setProductionOrder(order.getId());
		history.setOperate("入库");
		history.setOperator(SecurityContextHolder.getCurrentUserId());
		history.setOperateTime(new Timestamp(System.currentTimeMillis()));
		productionOrderOperationHistoryMapper.insert(history);

	}


	private ProductInventoryDTO createProductInventory(String workOrderNumber,ProductionOrder order,String whetherToEnterDocuments, String whetherToEnterComponents){
		ProductList productList = productListMapper.selectById(order.getProduct());
		EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
		ProductInventoryDTO dto = new ProductInventoryDTO();
		dto.setProductModel(productList.getProductModel());
		dto.setOrderNumber(order.getOrderNumber());
		dto.setWorkOrderNumber(workOrderNumber);
		dto.setProductName(productList.getProductName());
		dto.setEntrustedUnit(evaluationOrder.getEntrustedUnit());
		dto.setBatch(productList.getProductionBatch());
		if(productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)){
			dto.setQuantityOfCommissionedItems(productList.getSampleTotalCount());
		}else{
			dto.setQuantityOfCommissionedItems(order.getQuantity());
		}
		dto.setQuantityOfScreenedItems(order.getQuantity());
		dto.setStatus(DicConstant.CommonDic.DEFAULT_ZERO);
		dto.setIsQijian(whetherToEnterComponents);
		dto.setEngineeringCode(evaluationOrder.getEngineeringCode());
		dto.setInspectionSender(evaluationOrder.getPrincipal());
		if(!whetherToEnterDocuments.equals(DicConstant.CommonDic.DEFAULT_ONE)){
			dto.setReport(DicConstant.CommonDic.DEFAULT_ZERO);
		}else{
			dto.setReport(whetherToEnterDocuments);
		}
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order_number", workOrderNumber);
		wrapper.eq("report_status",DicConstant.ReportManagement.REPORT_STATUS_APPROVED);
		ReportManagement reportManagement = reportManagementMapper.selectOne(wrapper);
		if(!ObjectUtils.isEmpty(reportManagement))
		dto.setReportNumber(reportManagement.getReportNumber());
		return dto;
	}

	private void getProductionProcessTestData(String workOrderNumber, ProductInventoryDTO dto) {
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order", workOrderNumber);
		List<ProductionOrderProcessTest> tests = productionOrderProcessTestMapper.selectList(wrapper);
		List<String> processData = tests.stream().map(ProductionOrderProcessTest::getProcessData).collect(Collectors.toList());
		List<ProductionTaskVO> taskVOS = new ArrayList<>();
		processData.stream().forEach(p->{
			taskVOS.add(JSON.parseObject(p, ProductionTaskVO.class));
		});
		List<ProductionTaskVO> unProcessData = taskVOS.stream().filter(x->x.getUnqualifiedQuantity() !=null && x.getUnqualifiedQuantity()>0).collect(Collectors.toList());
		List<Map<String, String>>  dicdetail = dicDetailService.findDicDetailByDicCode("production_task_failureMode");
		Map<String, String> failureModeMap = dicdetail.stream()
				.collect(Collectors.toMap(
						map -> map.get("value"),      // dcode字段
						map -> map.get("label"),      // dname字段
						(existing, replacement) -> existing // 处理重复键
				));
		String processNames = unProcessData.stream()
				.map(ProductionTaskVO::getProcessName2)
				.filter(name -> name != null && !name.trim().isEmpty())
				.distinct()
				.collect(Collectors.joining(","));
		String failureModes = unProcessData.stream()
				.map(ProductionTaskVO::getFailureMode)
				.filter(mode -> mode != null && !mode.trim().isEmpty())
				.map(mode -> {
					if (mode.contains(",")) {
						return Arrays.stream(mode.split(","))
								.map(code -> failureModeMap.getOrDefault(code.trim(), code.trim()))
								.collect(Collectors.joining(","));
					}
					return failureModeMap.getOrDefault(mode, mode);
				})
				.distinct()
				.collect(Collectors.joining(","));
		ProductionTaskVO lastTask = taskVOS.stream()
				.max(Comparator.comparingInt(ProductionTaskVO::getExecutionSequence))
				.orElse(null);
		if(!ObjectUtils.isEmpty(lastTask)){
			dto.setQualifiedQuantity(lastTask.getQualifiedQuantity());
		}
		ProductionOrderResultVO resultVO = productionOrderResultService.selectResultByProductionOrder(workOrderNumber);
		if(!ObjectUtils.isEmpty(resultVO))
			dto.setUnqualifiedQuantity(resultVO.getUnqualifiedQuantity());//不合格数量
		dto.setNonQualityProcess(processNames);
		dto.setReasonForNonQuality(failureModes);
	}
	@Override
	@Transactional
	public TorchResponse connotScreened(ProductionOrderDTO productionOrderDTO) {
		for(String id :productionOrderDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			order.setIrretrievableReason(productionOrderDTO.getIrretrievableReason());
			order.setNonAgingReason(productionOrderDTO.getNonAgingReason());
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_CANCEL);
			order.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_CANNOT_SCREENED);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
			//保存操作历史
			ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
			history.setProductionOrder(order.getId());
			history.setOperate("不可筛");
			history.setOperator(SecurityContextHolder.getCurrentUserId());
			history.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(history);
			//向工单对应的测评订单客户经理发送消息通知
			//查询客户经理
			ProductList productList = productListMapper.selectById(order.getProduct());
			//更新产品列表状态
			productList.setStatus(DicConstant.SalesOrder.PRODUCT_LIST_STATUS_LOCKED);
			productList.setRejectUnscreenableReason(productionOrderDTO.getIrretrievableReason());
			productListMapper.updateById(productList);
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
			if(StringUtils.isNotEmpty(evaluationOrder.getCustomerManager())){
				MessageManagementDTO dto = new MessageManagementDTO();
				dto.setSenderId(SecurityContextHolder.getCurrentUserId());
				dto.setSendTime(new Timestamp(System.currentTimeMillis()));
				dto.setUserId(evaluationOrder.getCustomerManager());
				dto.setMessageContent("测评订单为"+evaluationOrder.getOrderNumber()+"的订单中序号为："+productList.getSerialNumber()+"的产品已修改为不可筛");
				messageManagementService.saveOrUpdate(dto);
			}
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse outSorucingBack(ProductionOrderDTO productionOrderDTO) {
		ProductionOrder order = awaitingProductionOrderMapper.selectById(productionOrderDTO.getId());
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_REJECT)){
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
			//外协审批驳回后将试验方式
			order.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_SELF);
		}
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_APPROVED)){
			order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_OUTSOURCED);
            order.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_PRODUCTION);
		}
		if(productionOrderDTO.getOutSourcingStatus().equals(DicConstant.OutSourcingStatus.OUTSOURCING_ACCEPTED)){
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)){
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED);
			}else if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED_SCHEDUL)){
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED_SCHEDUL);
			}else{
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE);
			}

//			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)){
//				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE);
//			}
		}
		order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
		order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse outSourcingApply(AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO, String token) {
		for(String id :addOrUpdateOutsourcingDTO.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			if(order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)||
					order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED)
					||order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_APPROVED_SCHEDUL)
					||order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION)
					||order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT)
			){
				addOrUpdateOutsourcingDTO.setEntireOrProcess(DicConstant.CommonDic.DEFAULT_ZERO);
				addOrUpdateOutsourcingDTO.setOrderId(id);
				outsourcingService.apply(addOrUpdateOutsourcingDTO,token);
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PAUSE);
				order.setTestsMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING);
				order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
				order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
				QueryWrapper wrapper = new QueryWrapper();
				wrapper.eq("id",order.getId());
				awaitingProductionOrderMapper.update(order,wrapper);
				//保存操作历史
				ProductionOrderOperationHistory history = new ProductionOrderOperationHistory();
				history.setProductionOrder(addOrUpdateOutsourcingDTO.getOrderId());
				history.setOperate("外协申请");
				history.setOperator(SecurityContextHolder.getCurrentUserId());
				history.setOperateTime(new Timestamp(System.currentTimeMillis()));
				productionOrderOperationHistoryMapper.insert(history);
			}else{
				throw new ServiceException("只有草稿、待提交、驳回或审批通过状态的工单才能外协申请");
			}
		}

		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}


	@Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "evaluation_order":
                    data = selectDataLinkageByOrderNumber(conditionalValue);
                    break;
                case "product_list1":
                    data = selectDataLinkageByProductModel(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByOrderNumber(String order_number) {
        return awaitingProductionOrderMapper.selectDataLinkageByOrderNumber(order_number);
    }
    @Override
    public Map<String,String> selectDataLinkageByProductModel(String product_model) {
        return awaitingProductionOrderMapper.selectDataLinkageByProductModel(product_model);
    }

    @Override
    @ExcelExportConversion(tableName = "production_order", convertorFields = "reportRequirements,reportFormat,dataReqERep,dataReqsPapereport,testMethodology,whetherToIncludeInScheduling,workOrderStatus,wtstabr,whetherToEnterComponents,whetherToEnterDocuments,wmfcnr")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionOrderVO> selectAwaitingProductionOrderList(ProductionOrderDTO dto) {
		List<ProductionOrderVO> list= awaitingProductionOrderMapper.selectAwaitingProductionOrderList(dto);
		TorchResponse<List<Map<String,String>>> taskLevels = dicDetailService.findDicDetail("product_list_taskLevel");
		Map<String, String> levelMap = taskLevels.getData().getData().stream()
				.collect(Collectors.toMap(
						map -> map.get("value"),  // key: value字段
						map -> map.get("label")   // value: label字段
				));
		TorchResponse<List<Map<String,String>>> testTypes = dicDetailService.findDicDetail("test_data_dictionary_testType");

		Map<String, String> typeMap = testTypes.getData().getData().stream()
				.collect(Collectors.toMap(
						map -> map.get("value"),  // key: value字段
						map -> map.get("label")   // value: label字段
				));
		TorchResponse<List<Map<String,String>>> testMethodologys = dicDetailService.findDicDetail("experiment_project_testMethodology");
		Map<String, String> methodMap = testMethodologys.getData().getData().stream()
				.collect(Collectors.toMap(
						map -> map.get("value"),  // key: value字段
						map -> map.get("label")   // value: label字段
				));
		TorchResponse<List<Map<String,String>>> workOrderStatus = dicDetailService.findDicDetail("awaiting_production_order_workOrderStatus");
		Map<String, String> statusMap = workOrderStatus.getData().getData().stream()
				.collect(Collectors.toMap(
						map -> map.get("value"),  // key: value字段
						map -> map.get("label")   // value: label字段
				));
		TorchResponse<List<Map<String,String>>> productionStages = dicDetailService.findDicDetail("evaluation_order_productionStage");
		Map<String, String> stageMap = productionStages.getData().getData().stream()
				.collect(Collectors.toMap(
						map -> map.get("value"),  // key: value字段
						map -> map.get("label")   // value: label字段
				));
		list.stream().forEach(x->{
			x.setTaskLevel(levelMap.get(x.getTaskLevel()));
			x.setTestType(typeMap.get(x.getTestType()));
			x.setTestMethodology(methodMap.get(x.getTestMethodology()));
			x.setWorkOrderStatus(statusMap.get(x.getWorkOrderStatus()));
			x.setProductionStage(stageMap.get(x.getProductionStage()));
		});
        return list;
    }

    @Override
    public TorchResponse selectAwaitingProductionOrderListByIds(List<String> ids) {
        List<ProductionOrderVO> awaitingProductionOrderList = awaitingProductionOrderMapper.selectAwaitingProductionOrderListByIds(ids);

		TorchResponse<List<ProductionOrderVO>> response = new TorchResponse<List<ProductionOrderVO>>();
		response.getData().setData(awaitingProductionOrderList);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount((long)awaitingProductionOrderList.size());
		return response;
    }
	@Override
	@Transactional
	public TorchResponse issuedProductByIds(List<String> ids) {
		if (CollUtil.isEmpty(ids)){
			TorchResponse resp = new TorchResponse();
			resp.getData().setData(new ArrayList<ProductionOrder>());
			return resp;
		}
		//根据产品列表id查询产品信息
		List<ProductListVO> productList = productListMapper.selectProductListListByIds(ids);
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.in("product",ids);
		List<ProductionOrder> productionOrders = awaitingProductionOrderMapper.selectList(wrapper);
		if(!ObjectUtils.isEmpty(productionOrders) ||productionOrders.size()>0 ){
			throw new ServiceException("存在已下发产品，请勿重复下发");
		}
		List<ProductionOrder> list = new ArrayList<>();
		List<ProductionOrder> result = new ArrayList<>();
		Map<String,String> workordernumMap = new HashMap<>();
		for(ProductListVO vo : productList){
			//查询测评订单信息
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(vo.getEvaluationOrderId());

			String workOrder = "";
			//判断下发的数据中是否有同型号同批次
//			workordernumMap.put(vo.getProductModel(),"");
			workOrder =this.getProductionNumber(vo,workordernumMap);
			ProductionOrder productionOrder = new ProductionOrder();
			productionOrder.setWorkOrderNumber(workOrder);
			productionOrder.setEstimatedCompletionTime(vo.getDeadline());
			productionOrder.setOrderNumber(evaluationOrder.getOrderNumber());
			productionOrder.setProduct(vo.getId());
			productionOrder.setTestsMethodology(evaluationOrder.getTestMethodology());
			productionOrder.setQuantity(vo.getQuantity());
			productionOrder.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION);
			productionOrder.setProductionStage(DicConstant.ProductionOrder.PRODUCTION_STAGE_NOSTARTED);
			productionOrder.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			productionOrder.setCodexTorchGroupId(vo.getCodexTorchGroupId());
			productionOrder.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.insert(productionOrder);
			if(workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
				list.add(productionOrder);
			}
			result.add(productionOrder);
		}
		fileReviewService.issueProductCreateFileVeiw(list);
		TorchResponse<List<ProductionOrder>> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(result);
		return response;
	}

	@Override
	public TorchResponse setResponsiblePerson(ProductionOrderDTO awaitingProductionOrderDto) {
		for(String id :awaitingProductionOrderDto.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			order.setResponsiblePerson(awaitingProductionOrderDto.getResponsiblePerson());
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
			productionOrderOperationHistory.setProductionOrder(order.getId());
			productionOrderOperationHistory.setOperate("指派负责人");
			productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
			productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
			//更新任务的负责人
			UpdateWrapper updateWrapper = new UpdateWrapper();
			updateWrapper.eq("work_order_number", order.getWorkOrderNumber());
			updateWrapper.eq("codex_torch_deleted", DicConstant.CommonDic.DIC_NO);
			updateWrapper.set("responsible_person",awaitingProductionOrderDto.getResponsiblePerson());
			productionTaskMapper.update(null,updateWrapper);
			productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse judgeModelAndBatch(ProductionOrderDTO awaitingProductionOrderDto) {
		String orderNumbers ="";
		for(String id : awaitingProductionOrderDto.getOrderIds()){
			SysGroup groupKekao = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_KEKAOXING);
			//查询当前工单
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			ProductList productList = productListMapper.selectById(order.getProduct());
			if(!order.getCodexTorchGroupId().equals(groupKekao.getId())){
				TorchResponse response = new TorchResponse<>();
				response.setStatus(Constant.REQUEST_SUCCESS);
				return response;
			}
			List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productList.getProductModel(),
					productList.getProductionBatch(),productList.getManufacturer(),order.getCodexTorchGroupId(), id);
			if(!ObjectUtils.isEmpty(existOrder) && existOrder.size() > 0){
				if(orderNumbers.equals("")){
					orderNumbers+=order.getWorkOrderNumber()+",";
				}else{
					orderNumbers+=order.getWorkOrderNumber();
				}
			}
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(orderNumbers);
		return response;
	}

	private String getSameModeBatchManufacture(String productMode,String productBatch,String manufacturer,String groupId,String currentProductionOrderId){
		List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productMode,
				productBatch,manufacturer,groupId, currentProductionOrderId);
		String flag=DicConstant.CommonDic.DEFAULT_ZERO;
		if(existOrder.size()>1)flag=DicConstant.CommonDic.DEFAULT_ONE;
		return flag;
	};
	@Override
	public TorchResponse SaveWtstabr(ProductionOrderDTO dto) {
		for(String id : dto.getOrderIds()){
			ProductionOrder order = awaitingProductionOrderMapper.selectById(id);
			ProductList productList = productListMapper.selectById(order.getProduct());
			List<ProductionOrderVO> existOrder = awaitingProductionOrderMapper.selectSameModelBatchManufacture(productList.getProductModel(),
					productList.getProductionBatch(),productList.getManufacturer(),order.getCodexTorchGroupId(), id);
			if(!ObjectUtils.isEmpty(existOrder) && existOrder.size()>0){
				order.setWtstabr(dto.getWtstabr());
				awaitingProductionOrderMapper.updateById(order);
			}
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	@Transactional
	public TorchResponse saveCustomerProcessSheme(CustomerProcessSchemeDTO customerProcessSchemeDTO, String token) {
		if(customerProcessSchemeDTO.getDetailFormItems().length==0){
			throw new ServiceException("请选择方案工序");
		}
		String operate ="";
		Map<String,CustomerExperimentProjectDTO> processkillInformationMap = new HashMap<>();
		//保存前检验是否符合条件
		checkOrders(customerProcessSchemeDTO.getOrderIds(),processkillInformationMap,customerProcessSchemeDTO.getDetailFormItems());
		for(String id : customerProcessSchemeDTO.getOrderIds()){
			operate="绑定工序方案";
			//查询工单信息
			ProductionOrder order= awaitingProductionOrderMapper.selectById(id);
			order.setPda(customerProcessSchemeDTO.getPda());
			order.setPackageForm(customerProcessSchemeDTO.getPackageForm());
			ProductList productList =productListMapper.selectById(order.getProduct());
			EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
			//如果是提交,需要判断
			if(customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
				String exctpionStr ="";
				submitProductionOrder(exctpionStr,processkillInformationMap,order,operate,productList,token);
			}
			//判断工单是否已绑定方案如果已绑定，将原有方案删除新增
			CustomerProcessScheme scheme =customerProcessSchemeMapper.selectByProductionOrder(id);
			scheme=saveOrUpdateScheme(scheme,id,productList,evaluationOrder,customerProcessSchemeDTO,order);
			List<CustomerExperimentProject> savedProjects = new ArrayList<>();
			List<CustomerExperimentProject> projects = new ArrayList<>();
			//保存试验项目
			saveCustomerExperimentProject(customerProcessSchemeDTO.getDetailFormItems(),order,scheme,savedProjects,projects);
			//更新试验项目的pda计算规则
			for (CustomerExperimentProject project :savedProjects) {
				String[] mpStr = project.getMpPdaCalcRlsStr().split(",");
				List<String> ids = new ArrayList<>();
				for(String str : mpStr){
					String processid = str.split("-")[0];
					int displaynum = Integer.parseInt(str.split("-")[1]); // 转换为int
					String mpid = projects.stream() .filter(f -> f.getProcessId().equals(processid) && f.getDisplayNumber() == displaynum) // 直接比较int
							.findFirst() .get() .getId();
					ids.add(mpid);
				}
				project.setMpPdaCalcRls(String.join(",",ids));
				String totalNoncompliantCountId = project.getTotalNoncompliantCountStr();
				String processidTotal = totalNoncompliantCountId.split("-")[0];
				int displaynumProces = Integer.parseInt(totalNoncompliantCountId.split("-")[1]);
				String totalId = projects.stream().filter(f->f.getProcessId().equals(processidTotal) && f.getDisplayNumber().equals(displaynumProces)).findFirst().get().getId();
				project.setTotalNoncompliantCount(totalId);
				customerExperimentProjectMapper.updateById(project);
			}
			ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
			productionOrderOperationHistory.setProductionOrder(order.getId());
			productionOrderOperationHistory.setOperate(operate);
			productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
			productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);
			if(!customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
				order.setPreparedBy(SecurityContextHolder.getCurrentUserId());
				order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT);
				order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
				order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
				awaitingProductionOrderMapper.updateById(order);
			}
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	/**
	 * 校验工单的状态是否满足绑定工序方案的条件
	 * 判断同一方案中相同工作站的执行顺序不允许详情
	 * 判断方案中工序是否需要能力如果需要能力 产品资料不能为空
	 * @return
	 */
	private void checkOrders(String[] orderIds, Map<String, CustomerExperimentProjectDTO> processkillInformationMap, CustomerExperimentProjectDTO[] detailFormItems){
		//判断工单状态是否符合要求，
		QueryWrapper wrapper = new QueryWrapper<>();
		wrapper.in("id",orderIds);
		List<ProductionOrder> orders = awaitingProductionOrderMapper.selectList(wrapper);
		List<String> orderStatus = orders.stream().map(ProductionOrder::getWorkOrderStatus).distinct().collect(Collectors.toList());
		if(orderStatus.stream().anyMatch(status -> !Arrays.asList(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT, DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT,
				DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION).contains(status))){
			throw new ServiceException("只有草稿、待提交或驳回状态的工单才能绑定工序方案");
		}
		//工作站相同时执行顺序不允许相同
		List<CustomerExperimentProjectDTO> dtos = Arrays.asList(detailFormItems);
		boolean hasDuplicate = dtos.stream()
				.collect(Collectors.groupingBy(dto ->dto.getWorkstation() + "_" + dto.getExecutionSequence(),
						Collectors.counting())).values().stream().anyMatch(count -> count > 1);
		if (hasDuplicate) {
			throw new ServiceException("同一工作站的执行顺序不允许相同");
		}

		//工序是否需要老化或测试能力如果需要能力 产品资料就不能为空
		for(CustomerExperimentProjectDTO dto : dtos){
			StandardProcessManagement processManagement = standardProcessManagementMapper.selectById(dto.getProcessId());
			if(!StringUtils.isEmpty(processManagement.getSkill()) && StringUtils.isEmpty(dto.getProductInformation1())){
				throw new ServiceException("工序"+dto.getCustomerProcessName()+"需要老化或测试能力，产品资料不能为空");
			}
			if(!StringUtils.isEmpty(processManagement.getSkill())&& !StringUtils.isEmpty(dto.getProductInformation1())){
				processkillInformationMap.put(processManagement.getSkill(),dto);
			}
		}
	}
	private void saveCustomerExperimentProject(CustomerExperimentProjectDTO[] detailFormItems, ProductionOrder order, CustomerProcessScheme scheme, List<CustomerExperimentProject> savedProjects, List<CustomerExperimentProject> projects){
		for(CustomerExperimentProjectDTO dto:detailFormItems){
			CustomerExperimentProject customerExperimentProject = new CustomerExperimentProject();
			BeanUtils.copyProperties(dto,customerExperimentProject);
			customerExperimentProject.setId(null);
			if(customerExperimentProject.getSampleQuantity12()==null){
				customerExperimentProject.setSampleQuantity12(order.getQuantity());
			}
			//非必要字段处理
			customerExperimentProject.setCodexTorchDeleted(Constant.DEFAULT_NO);
			//主子表关联ID
			customerExperimentProject.setCodexTorchMasterFormId(scheme.getId());
			customerExperimentProjectMapper.insert(customerExperimentProject);
			if(StringUtils.isNotEmpty(customerExperimentProject.getMpPdaCalcRlsStr()))
				savedProjects.add(customerExperimentProject);
			CustomerExperimentProjectDataDTO[] list = dto.getProjectDataItems();
			if(null!=list){
				for (CustomerExperimentProjectDataDTO dataDTO : dto.getProjectDataItems()){
					CustomerExperimentProjectData customerExperimentProjectData = new CustomerExperimentProjectData();
					BeanUtils.copyProperties(dataDTO,customerExperimentProjectData);
					customerExperimentProjectData.setId(null);
					customerExperimentProjectData.setStandardProcessCode(dto.getProcessCode3());
					customerExperimentProjectData.setCodexTorchDeleted(Constant.DEFAULT_NO);
					customerExperimentProjectData.setCodexTorchMasterFormId(customerExperimentProject.getId());
					customerExperimentProjectDataMapper.insert(customerExperimentProjectData);
				}
			}
			projects.add(customerExperimentProject);
		}
	}
	private CustomerProcessScheme saveOrUpdateScheme(CustomerProcessScheme scheme, String id, ProductList productList, EvaluationOrder evaluationOrder, CustomerProcessSchemeDTO customerProcessSchemeDTO, ProductionOrder order){
		if(ObjectUtils.isEmpty(scheme)){
			scheme = new CustomerProcessScheme();
			scheme.setWorkOrder(id);
			scheme.setProductModel(productList.getProductId());
			scheme.setProductCategory(productList.getProductCategory());
			scheme.setTestType(productList.getTestType());
			scheme.setPda(customerProcessSchemeDTO.getPda());
			scheme.setPackageForm(customerProcessSchemeDTO.getPackageForm());
			scheme.setEntrustedUnit(evaluationOrder.getCustomerId());
			scheme.setStandardSpecificationNumber(productList.getStandardSpecificationId());
			scheme.setManufacturer(productList.getManufacturer());
			scheme.setQualityGrade(productList.getQualityGrade());
			scheme.setPda(order.getPda());
			if(customerProcessSchemeDTO.getCommitType().equals(DicConstant.CommonDic.DEFAULT_ONE)){
				scheme.setStatus(DicConstant.CommonDic.DIC_YES);
			}else{
				scheme.setStatus(DicConstant.CommonDic.DIC_NO);
			}
			//查询产品管理id
			QueryWrapper wrapper = new QueryWrapper<>();
			wrapper.eq("product_model",productList.getProductModel());
			wrapper.eq("product_name",productList.getProductName());
			wrapper.eq("manufacturer",productList.getManufacturer());
			ProductManagement productManagement =productManagementMapper.selectOne(wrapper);
			if(!ObjectUtils.isEmpty(productManagement))
				scheme.setProductModel(productManagement.getId());
			scheme.setPackageForm(order.getPackageForm());
			scheme.setTestPackage(customerProcessSchemeDTO.getTestPackage());
			scheme.setComment(customerProcessSchemeDTO.getComment());
			scheme.setReportComment(customerProcessSchemeDTO.getReportComment());
			scheme.setDepartment(order.getCodexTorchGroupId());
			scheme.setCodexTorchGroupId(order.getCodexTorchGroupId());
			scheme.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			scheme.setCodexTorchCreatorId(order.getCodexTorchGroupId());
			scheme.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
			customerProcessSchemeMapper.insert(scheme);
		}
		else{
			scheme.setPda(customerProcessSchemeDTO.getPda());
			scheme.setPackageForm(customerProcessSchemeDTO.getPackageForm());
			scheme.setComment(customerProcessSchemeDTO.getComment());
			scheme.setReportComment(customerProcessSchemeDTO.getReportComment());
			customerProcessSchemeMapper.updateById(scheme);
			QueryWrapper wrapper = new QueryWrapper<>();
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID",scheme.getId());
			List<CustomerExperimentProject> projects= customerExperimentProjectMapper.selectList(wrapper);
			wrapper.clear();
			List<String> ids = projects.stream().map(CustomerExperimentProject::getId).collect(Collectors.toList());
			if(!ObjectUtils.isEmpty(ids)){
				wrapper.in("CODEX_TORCH_MASTER_FORM_ID",ids);
				customerExperimentProjectDataMapper.delete(wrapper);
				wrapper.clear();
				wrapper.in("id",ids);
				customerExperimentProjectMapper.delete(wrapper);
			}
		}
		return scheme;
	}

	private void submitProductionOrder(String exctpionStr, Map<String, CustomerExperimentProjectDTO> processkillInformationMap, ProductionOrder order, String operate,ProductList productList, String token){
		//判断工序需要测试或老化能力的产品资料和能力评审的资料是否相同
		//如果是可靠性工单且是复制单 则用原始工单查询评审结果
		String workerOrderNumber = order.getWorkOrderNumber();
		if(order.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY) &&
				StringUtils.isNotBlank(order.getSoruceOrder()) &&
				(productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)||
				productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)
		)){
			//鉴定或质量一致性的原始工单是带-1的
			workerOrderNumber=order.getWorkOrderNumber().split("-")[0]+"-1";
		}
		if(order.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY) &&
				StringUtils.isNotBlank(order.getSoruceOrder()) &&
				!productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST) &&
						!productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)
				){
			//非鉴定或质量一致性工单
			workerOrderNumber=order.getWorkOrderNumber().split("-")[0];
		}
		List<CapabilityReview> reviews = capabilityReviewService.getCapabilityReviewByProductionWorkOrder(workerOrderNumber,null);
		if(reviews.size()==0){
			throw new ServiceException("能力评审未通过");
		}
		boolean allMatch = reviews.stream() .allMatch(review -> DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES
				.equals(review.getReviewResult()));
		if(!allMatch){
			exctpionStr=order.getWorkOrderNumber()+"能力评审未通过;";
		}
		Map<String, String> reviewMap = reviews.stream()
				.filter(review -> review.getProductInformation1Number() != null &&!review.getProductInformation1Number().trim().isEmpty())
				.collect(Collectors.toMap(CapabilityReview::getInspectionType,CapabilityReview::getProductInformation1Number));
		for(String procdessMap :processkillInformationMap.keySet()){
			String inspectionType = procdessMap;
			CustomerExperimentProjectDTO dto = processkillInformationMap.get(procdessMap);
			String expectedNumber = reviewMap.get(inspectionType);
			ProductInformationManagement productInformationManagement =productInformationManagementMapper.selectById(dto.getProductInformation1());
			StandardSpecification standardSpecification=standardSpecificationMapper.selectById(dto.getProductInformation1());
			String actualNumber ="";
			if(productInformationManagement!=null){
				actualNumber = productInformationManagement.getNumber();
			}else{
				actualNumber = standardSpecification.getControlledNumber();
			}
			if(StringUtils.isNotBlank(expectedNumber)){
				if (!expectedNumber.equals(actualNumber)) {
					exctpionStr+="工序"+dto.getCustomerProcessName()+"需要测试或老化能力，但产品资料和对应的能力评审中的资料不一致";
					break;
				}
			}
		}
		if(StringUtils.isNotBlank(exctpionStr)){//评审未通过
			throw new ServiceException(exctpionStr+",不能提交");
		}
		//提交审批
		ProductionOrderDTO dto = new ProductionOrderDTO();
		BeanUtils.copyProperties(order,dto);
		operate="提交工单审批";
		this.apply(dto,token);
	}
	@Override
	@Transactional
	public TorchResponse submitProductionOrderBatch(CustomerProcessSchemeDTO customerProcessSchemeDTO, String token) {

		for(String id : customerProcessSchemeDTO.getOrderIds()){
			//查询工单信息
			ProductionOrder order= awaitingProductionOrderMapper.selectById(id);
			ProductList productList =productListMapper.selectById(order.getProduct());
			if(!order.getWorkOrderStatus().equals(DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)){
				throw new ServiceException("工单"+order.getWorkOrderNumber()+"状态不是待提交");
			}
			//查询这个工单绑定的方案
			CustomerProcessScheme processScheme = customerProcessSchemeMapper.selectByProductionOrder(order.getId());
			QueryWrapper wrapper = new QueryWrapper();
			wrapper.eq("CODEX_TORCH_MASTER_FORM_ID", processScheme.getId());
			List<CustomerExperimentProject> customerExperimentProject = customerExperimentProjectMapper.selectList(wrapper);
			Map<String,CustomerExperimentProject> processkillInformationMap = new HashMap<>();
			//工序是否需要老化或测试能力如果需要能力 产品资料就不能为空
			for(CustomerExperimentProject project : customerExperimentProject){
				StandardProcessManagement processManagement = standardProcessManagementMapper.selectById(project.getProcessId());
				if(!StringUtils.isEmpty(processManagement.getSkill()) && StringUtils.isEmpty(project.getProductInformation1())){
					throw new ServiceException(order.getWorkOrderNumber()+"的工序"+project.getCustomerProcessName()+"需要老化或测试能力，产品资料不能为空");
				}
				if(!StringUtils.isEmpty(processManagement.getSkill())&& !StringUtils.isEmpty(project.getProductInformation1())){
					processkillInformationMap.put(processManagement.getSkill(),project);
				}
			}
			String exctpionStr ="";
			String workerOrderNumber = order.getWorkOrderNumber();
			if(order.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY) &&
					StringUtils.isNotBlank(order.getSoruceOrder()) &&
					(productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)||
							productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)
					)){
				//鉴定或质量一致性的原始工单是带-1的
				workerOrderNumber=order.getWorkOrderNumber().split("-")[0]+"-1";
			}
			if(order.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY) &&
					StringUtils.isNotBlank(order.getSoruceOrder()) &&
					!productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST) &&
					!productList.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)
			){
				//非鉴定或质量一致性工单
				workerOrderNumber=order.getWorkOrderNumber().split("-")[0];
			}
			//如果是提交,需要判断
			List<CapabilityReview> reviews = capabilityReviewService.getCapabilityReviewByProductionWorkOrder(workerOrderNumber,null);
			if(reviews.size()==0){
				throw new ServiceException("能力评审未通过");
			}
			boolean allMatch = reviews.stream() .allMatch(review -> DicConstant.TechnicalManagement.CAPABILITY_REVIEW_RESULT_YES
					.equals(review.getReviewResult()));
			if(!allMatch){
				exctpionStr="能力评审未通过";
			}
			Map<String, String> reviewMap = reviews.stream()
					.filter(review -> review.getProductInformation1Number() != null &&!review.getProductInformation1Number().trim().isEmpty())
					.collect(Collectors.toMap(CapabilityReview::getInspectionType,CapabilityReview::getProductInformation1Number));
			for(String procdessMap :processkillInformationMap.keySet()){
				String inspectionType = procdessMap;
				CustomerExperimentProject dto = processkillInformationMap.get(procdessMap);
				String expectedNumber = reviewMap.get(inspectionType);
				ProductInformationManagement productInformationManagement =productInformationManagementMapper.selectById(dto.getProductInformation1());
				StandardSpecification standardSpecification=standardSpecificationMapper.selectById(dto.getProductInformation1());
				String actualNumber ="";
				if(productInformationManagement!=null){
					actualNumber = productInformationManagement.getNumber();
				}else{
					actualNumber = standardSpecification.getControlledNumber();
				}
				if(StringUtils.isNotBlank(expectedNumber)){
					if (!expectedNumber.equals(actualNumber)) {
						exctpionStr+=order.getWorkOrderNumber()+"的工序"+dto.getCustomerProcessName()+"需要测试或老化能力，但产品资料和对应的能力评审中的资料不一致";
						break;
					}
				}
			}
			if(StringUtils.isNotBlank(exctpionStr)){//评审未通过
				throw new ServiceException(exctpionStr+",不能提交");
			}
			//提交审批
			ProductionOrderDTO dto = new ProductionOrderDTO();
			BeanUtils.copyProperties(order,dto);
			this.apply(dto,token);
			ProductionOrderOperationHistory productionOrderOperationHistory = new ProductionOrderOperationHistory();
			productionOrderOperationHistory.setProductionOrder(order.getId());
			productionOrderOperationHistory.setOperate("提交工单审批");
			productionOrderOperationHistory.setOperator(SecurityContextHolder.getCurrentUserId());
			productionOrderOperationHistory.setOperateTime(new Timestamp(System.currentTimeMillis()));
			productionOrderOperationHistoryMapper.insert(productionOrderOperationHistory);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

    @Override
    public TorchResponse filterWorkOrdersNotAllowOutsourcing() {
		//工序外协：试验方式是自产，并且状态未开始或暂停可以外协。完成、进行中不能外协。试验方式外协的，不能外协。
		//工单外协：试验方式是自产，并且状态草稿、审批驳回、待提交、审批通过可以外协。审批通过待排产、进行中、已外协、取消、完成状态不能外协。试验方式外协的，不能外协。

		//根据上面两个条件筛选出可外协的工单编号列表
		//查询可整单外协的生产工单
		LambdaQueryWrapper<ProductionOrder> productionOrderQueryWrapper = Wrappers.lambdaQuery(ProductionOrder.class)
				.select(ProductionOrder::getWorkOrderNumber)
				.in(ProductionOrder::getWorkOrderStatus, DicConstant.ProductionOrder.WORK_ORDER_STATUS_PREPARATION,
						DicConstant.ProductionOrder.WORK_ORDER_STATUS_REJECT,
						DicConstant.ProductionOrder.WORK_ORDER_STATUS_DRAFT)
				.and(wrappper -> wrappper.eq(ProductionOrder::getTestsMethodology, DicConstant.ProductionOrder.TEST_METHODLOGY_SELF));
		List<String> productionOrderNumbers = awaitingProductionOrderMapper.selectObjs(productionOrderQueryWrapper)
				.stream().map(obj -> (String)obj).collect(Collectors.toList());

		//查询可工序外协的工单
		LambdaQueryWrapper<ProductionTask> productionTaskQueryWrapper = Wrappers.lambdaQuery(ProductionTask.class)
				.select(ProductionTask::getWorkOrderNumber)
				.in(ProductionTask::getStatus, DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING,
						DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI)
				.and(wrapper -> wrapper.eq(ProductionTask::getTestMethodology, DicConstant.ProductionOrder.TEST_METHODLOGY_SELF))
				.groupBy(ProductionTask::getWorkOrderNumber);
		List<String> workOrderNumbers = productionTaskMapper.selectObjs(productionTaskQueryWrapper)
				.stream().map(obj -> (String) obj).collect(Collectors.toList());
		//取并集
		Set<String> orderNumbers = new HashSet<>();
		orderNumbers.addAll(productionOrderNumbers);
		orderNumbers.addAll(workOrderNumbers);

		List<String> allowableOutsourcingOrderNos = new ArrayList<>(orderNumbers);
		List<SelectOptionsVO> options = allowableOutsourcingOrderNos.stream()
				.map(orderNo -> {
					SelectOptionsVO vo = new SelectOptionsVO();
					vo.setValue(orderNo);
					vo.setLabel(orderNo);
					return vo;
				})
				.collect(Collectors.toList());
		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<>();
		response.getData().setData(options);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

    @Override
    public TorchResponse downloadProductionOrder(String workOrderNumber, HttpServletResponse response, String QRCode) throws IOException{
		//获取 生产工单 相关信息
		ProductionOrderInfoVO productionOrderInfoVO = this.getDetailByOrderNumber(workOrderNumber).getData().getData();
		// 获取生产任务相关信息
		List<ProductionTaskViewVO> productionTaskViewVOs = productionTaskService.selectProductionTaskByProductionOrder(workOrderNumber);
		Map<String, String> taskNumber2Eq = new HashMap<>();
		// 获取生产任务相关设备信息
		for (ProductionTaskViewVO productionTaskViewVO : productionTaskViewVOs){
			String productionTaskNumber = productionTaskViewVO.getTaskNumber();
			ProductionTaskVO productionTaskVO = productionTaskService.selectProductionTaskByNumber(productionTaskNumber).getData().getData();
			List<ProdTaskEqInfoVO> prodTaskEqInfos = productionTaskVO.getProdTaskEqInfoList();
			//将设备编号逗号拼接存在taskNumber2Eq map中
			StringBuilder eqNumber = new StringBuilder();
			for(ProdTaskEqInfoVO prodTaskEqInfo : prodTaskEqInfos){
				if (eqNumber.length() > 0){
					eqNumber.append(",");
				}
				eqNumber.append(prodTaskEqInfo.getDeviceSerialNumber());
			}
			taskNumber2Eq.put(productionTaskNumber, eqNumber.toString());
		}
		//通过表单号查找对应流程记录
		String orderCreateTime = ""; //制单时间
		String approveTime = ""; //审批时间
		SysProcessRecordVO processRecordVO = sysProcessRecordService.findSysProcessRecord(productionOrderInfoVO.getOrderId(), "").getData().getData();
		if (processRecordVO != null){
			Timestamp createTime = processRecordVO.getCreateTime();
			Timestamp approveDatetime = processRecordVO.getApproveDatetime();
			if (createTime != null){
				orderCreateTime = DateUtil.format(createTime, "yyyy-MM-dd HH:mm:ss");
			}
			if (approveDatetime != null){
				approveTime = DateUtil.format(approveDatetime, "yyyy-MM-dd HH:mm:ss");
			}
		}
		//筛选流程实体
		List<ProcessFilterVO> processFilterVOS = new ArrayList<>();
		productionTaskViewVOs.forEach(item -> {
			//获取技术员（操作类型为审批通过-6的创建人id）
			LambdaQueryWrapper<ProdTaskOpHist> queryWrapper = Wrappers.lambdaQuery(ProdTaskOpHist.class)
					.eq(ProdTaskOpHist::getTaskNumber, item.getTaskNumber())
					.eq(ProdTaskOpHist::getOperationType, DicConstant.ProductionOrder.OPERATION_TYPE_WANCHENG)
					.eq(ProdTaskOpHist::getCodexTorchDeleted, Constant.DEFAULT_NO);
			ProdTaskOpHist prodTaskOpHist = prodTaskOpHistMapper.selectOne(queryWrapper);
			String operator = "";
			if (prodTaskOpHist != null){
				String operatorId = prodTaskOpHist.getCodexTorchCreatorId();
				//根据id查名字
				operator = sysUserService.findUser(operatorId).getData().getData().getUserName();
			}
			ProcessFilterVO filterVO = ProcessFilterVO.builder()
					.displayNumber(item.getDisplayNumber())
					.experimentProject(productionOrderInfoVO.getExperimentProject())
					.testConditions(item.getTestConditions())
					.deviceSerialNumber(taskNumber2Eq.getOrDefault(item.getTaskNumber(), ""))
					.qualifiedQuantity(item.getQualifiedQuantity())
					.unqualifiedQuantity(item.getUnqualifiedQuantity())
					.failureMode(item.getFailureMode())
					.operator(operator)
					.reporter4(item.getReporter4())
					.actualStartTime(DateUtil.format(item.getActualStartTime(), "yyyy-MM-dd HH:mm:ss"))
					.build();
			processFilterVOS.add(filterVO);
		});
		//根据生产工单id查询对应的客户工序方案备注
		LambdaQueryWrapper<CustomerProcessScheme> processSchemeQueryWrapper = Wrappers.lambdaQuery(CustomerProcessScheme.class)
				.eq(CustomerProcessScheme::getWorkOrder, productionOrderInfoVO.getOrderId());
		CustomerProcessScheme customerProcessScheme = customerProcessSchemeMapper.selectOne(processSchemeQueryWrapper);
		String comment = customerProcessScheme.getComment();
		//构建工单下载返回的ProductionOrderDownloadVO实体信息
		ProductionOrderDownloadVO orderDownloadVO = ProductionOrderDownloadVO.builder()
				.workOrderNumber(productionOrderInfoVO.getWorkOrderNumber())
				.productionName(productionOrderInfoVO.getProductName())
				.productionBatch(productionOrderInfoVO.getProductionBatch())
				.customerNumber(productionOrderInfoVO.getCustomerNumber())
				.qualityGrade(productionOrderInfoVO.getQualityGrade())
				.deadline(DateUtil.format(productionOrderInfoVO.getDeadline(), "yyyy-MM-dd HH:mm:ss"))
				.productModel(productionOrderInfoVO.getProductModel())
				.manufacturer(productionOrderInfoVO.getManufacturer())
				.quantity(productionOrderInfoVO.getQuantity())
				.completionTime(DateUtil.format(productionOrderInfoVO.getCompletionTime(), "yyyy-MM-dd HH:mm:ss"))
				.processFilterVOList(processFilterVOS)
				.orderCreator(productionOrderInfoVO.getApprover())
				.approver(productionOrderInfoVO.getCodexTorchApplicant())
				.orderCreateTime(orderCreateTime)
				.approveTime(approveTime)
				.comment(comment)
				.imageUrl("E:\\phototest\\test.png")
				.build();

		//调用Excel工具类
		exportMerge(response, orderDownloadVO, "test.xlsx");
		TorchResponse resp = new TorchResponse();
		resp.setStatus(Constant.REQUEST_SUCCESS);
		return resp;
    }
	private void exportMerge(HttpServletResponse response, ProductionOrderDownloadVO orderDownloadVO, String fileName) throws IOException{
		Map<String, Object> datas = new HashMap<>();
		datas.put("workOrderNumber", orderDownloadVO.getWorkOrderNumber());
		datas.put("productionName", orderDownloadVO.getProductionName());
		datas.put("productionBatch", orderDownloadVO.getProductionBatch());
		datas.put("customerNumber", orderDownloadVO.getCustomerNumber());
		datas.put("qualityGrade", orderDownloadVO.getQualityGrade());
		datas.put("deadline", orderDownloadVO.getDeadline());
		datas.put("productModel", orderDownloadVO.getProductModel());
		datas.put("manufacturer", orderDownloadVO.getManufacturer());
		datas.put("quantity", orderDownloadVO.getQuantity());
		datas.put("completionTime", orderDownloadVO.getCompletionTime());
		datas.put("processFilterVOList", orderDownloadVO.getProcessFilterVOList());
		datas.put("orderCreator", orderDownloadVO.getOrderCreator());
		datas.put("approver", orderDownloadVO.getApprover());
		datas.put("orderCreateTime", orderDownloadVO.getOrderCreateTime());
		datas.put("approveTime", orderDownloadVO.getApproveTime());
		datas.put("comment", orderDownloadVO.getComment());
		datas.put("imageUrl", orderDownloadVO.getImageUrl());
		String tmplateName = "下载工单模版.xlsx";
		ClassPathResource resource = new ClassPathResource("/template/" + tmplateName);
		response.setCharacterEncoding("UTF-8");
		response.setContentType("application/vnd.ms-excel");
		String fileNameStr = URLEncoder.encode(fileName,"UTF-8") ;
		response.setHeader("Content-Disposition", "attachment;filename=" +
				new String(fileNameStr.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));
		response.setHeader("filename", fileNameStr);
		File temp = resource.getFile();
		String dataStrs = JSON.toJSONString(datas, SerializerFeature.WriteMapNullValue);
		ExcelUtil.writeFileExcel(temp.getPath(), response.getOutputStream(), dataStrs);

	}

    private String getProductionNumber(ProductListVO vo, Map<String, String> workordernumMap){
		SysGroup groupKekao = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_KEKAOXING);
		SysGroup groupShengchan = sysGroupService.findGroupsByCode(DicConstant.Group.GROUP_SHENGCHAN);
		String workOrder = "";

		// 鉴定试验和质量一致性试验特殊处理
		if (vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)) {
			TorchResponse response = codeManagementService.getOrderNumber(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY);
			workOrder = response.getData().getData().toString() + "-1";
			vo.setCodexTorchGroupId(groupKekao.getId());
			return workOrder;
		}

		// 确定工单前缀和所属组别
		String prefix = "";
		if (vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_SPECIAL_ANALYSIS)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_FAILURE_ANALYSIS)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_OTHER_TEST)) {
			prefix = DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY;
		} else if (vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ONE)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_TWO)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_ENVIRONMENT)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_REINSPECTION)
				|| vo.getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_MICROWAVE_QUALIFICATION_TEST)) {
			prefix = DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING;
		}

		// 使用 "前缀+产品型号" 作为key，确保K和S类型独立编号
		String productModelKey = prefix + vo.getProductModel();

		// 查询是否有同型号同前缀的工单
		ProductionOrder oldProductionOrder = awaitingProductionOrderMapper.selectProductionOrderByModelAndBatch(
				vo.getEvaluationOrderId(), vo.getProductModel(),prefix);

		// 如果已有同型号同前缀工单，记录到map中
		if (!ObjectUtils.isEmpty(oldProductionOrder) && oldProductionOrder.getWorkOrderNumber().startsWith(prefix)) {
			workordernumMap.put(productModelKey, oldProductionOrder.getWorkOrderNumber());
		}

		// 如果map中没有该型号该前缀的记录，生成新工单号
		if (workordernumMap.size() == 0 || !workordernumMap.containsKey(productModelKey)
				|| StringUtils.isEmpty(workordernumMap.get(productModelKey))) {

			TorchResponse response = codeManagementService.getOrderNumber(prefix);
			workOrder = response.getData().getData().toString();
			workordernumMap.put(productModelKey, workOrder);

			// 设置组别
			if ( DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY.equals(prefix)) {
				vo.setCodexTorchGroupId(groupKekao.getId());
			} else if ( DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING.equals(prefix)) {
				vo.setCodexTorchGroupId(groupShengchan.getId());
			}
		} else {
			// 已有同型号同前缀工单，生成连续编号
			String existingWorkOrder = workordernumMap.get(productModelKey);
			String[] nums = existingWorkOrder.split("-");

			if (nums.length == 1) {
				workOrder = nums[0] + "-1";
			} else {
				int sequence = Integer.valueOf(nums[1]) + 1;
				workOrder = nums[0] + "-" + sequence;
			}
			workordernumMap.put(productModelKey, workOrder);
			// 设置组别基于工单前缀
			if (workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)) {
				vo.setCodexTorchGroupId(groupKekao.getId());
			} else if (workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)) {
				vo.setCodexTorchGroupId(groupShengchan.getId());
			}
		}
		return workOrder;
	}


	@Override
	public TorchResponse completeProductionOrder(String workOrderNumber){
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order_number",workOrderNumber);
		ProductionOrder order = awaitingProductionOrderMapper.selectOne(wrapper);
		order.setWorkOrderStatus(DicConstant.ProductionOrder.WORK_ORDER_STATUS_COMPLETE);
		order.setCompletionTime(new Timestamp(System.currentTimeMillis()));
		awaitingProductionOrderMapper.updateById(order);
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}
	@Override
	public TorchResponse updateProductionOrdrePdaWarning(ProductionOrderDTO dto){
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order_number",dto.getWorkOrderNumber());
		ProductionOrder order = awaitingProductionOrderMapper.selectOne(wrapper);
		if(order==null){
			throw new ServiceException("此工单不存在,工单编号:"+dto.getWorkOrderNumber());
		}
		if(order.getPda()!=null && order.getPda().compareTo(dto.getPda())<0){
			order.setWtstabr(DicConstant.CommonDic.DEFAULT_ONE);
			order.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
			order.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
			awaitingProductionOrderMapper.updateById(order);
		}
		TorchResponse response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

    @Override
    public TorchResponse<AbnormalfeedbackVO> getInfoByOrderNumber(String orderNumber) {
        AbnormalfeedbackVO productionOrder = awaitingProductionOrderMapper.getInfoByOrderNumber(orderNumber);

		TorchResponse<AbnormalfeedbackVO> response = new TorchResponse<>();
		response.getData().setData(productionOrder);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

	/**
	 * 1,测筛有合合并工单报告(可以多选）
	 * 2，可靠性只能选单挑数据（但鉴定和质量一致性会自动合并）
	 * @param productionOrderResultDTO
	 * @return
	 * @throws IOException
	 */
	@Override
	@Transactional
	public TorchResponse createProductionOrderReport(ProductionOrderDTO productionOrderResultDTO) throws IOException {

		TorchResponse response = new TorchResponse<>();
		//查询工单的报告状态,如果报告状态不是空或者草稿和驳回状态则不能生成报告
		List<ProductionOrderVO> productionOrders = awaitingProductionOrderMapper.selectAwaitingProductionOrderListByIds(Arrays.asList(productionOrderResultDTO.getOrderIds()));
		List<String> reportStatuses = productionOrders.stream().map(ProductionOrderVO::getReportStatus).collect(Collectors.toList());
		for(String status : reportStatuses){
			if(StringUtils.isNotBlank(status) && !status.equals(DicConstant.CommonDic.DRAFT) && !status.equals(DicConstant.CommonDic.REJECT)){
				throw new ServiceException("工单报告已生成或工单报告状态不是草稿和驳回状态则不能生成报告!");
			}
		}
		List<String> workOrders = productionOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.toList());
		boolean hasKStart = workOrders.stream().filter(workOrder -> workOrder != null).anyMatch(workOrder -> workOrder.startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY));
		if(hasKStart && productionOrderResultDTO.getOrderIds().length>1){
			throw new ServiceException("可靠性工单不能合并生成报告!");
		}
		List<String> orderNumbers = productionOrders.stream().map(ProductionOrderVO::getOrderNumber).distinct().collect(Collectors.toList());
		List<String> models = productionOrders.stream().map(ProductionOrderVO::getProductModel).distinct().collect(Collectors.toList());
		List<String> batchs = productionOrders.stream().map(ProductionOrderVO::getProductionBatch).distinct().collect(Collectors.toList());
		List<String> testTyes = productionOrders.stream().map(ProductionOrderVO::getTestType).distinct().collect(Collectors.toList());

		if(orderNumbers.size()>1 || models.size()>1 || batchs.size()>1 || testTyes.size()>1){
			throw new ServiceException("选中的工单不是同订单、同型号、同批次、同试验类型不能合并生成报告!");
		}
		//定义模板路径
		String template ="template/" ;

		ReportDocxTool rt = new ReportDocxTool();
		// 判断类型，如果订单状态为不可筛，则生成不可筛报告
		long unscreenableCount = productionOrders.stream()
				.filter(po -> po.getTestMethodology().equals(DicConstant.ProductionOrder.TEST_METHODLOGY_CANNOT_SCREENED))
				.count();

		if (unscreenableCount > 0) {
			if (productionOrders.size() > 1) {
				throw new ServiceException("生成不可筛报告不可合并生成，请重新勾选单个");
			}
			ProductionOrderVO firstOrder = productionOrders.get(0);
			if (firstOrder.getWorkOrderNumber() != null && firstOrder.getWorkOrderNumber().matches("^K\\d+.*")) {
				// 以K开头且后面跟着数字的处理逻辑
				throw new ServiceException(firstOrder.getWorkOrderNumber()+"错误，无法生成不可筛报告");
			}
			// 假设列表中唯一的订单就是符合条件的不可筛订单
			template+="noAbleReport.docx";
			// 方法二：获取资源文件的绝对路径（需处理URL编码）
			String templatePath = getClass().getClassLoader().getResource(template).getPath();
			// 注意：如果路径包含特殊字符（如空格或中文），需要解码：
			templatePath = URLDecoder.decode(templatePath, "UTF-8");
			XWPFDocument document = createUnscreenableReport(productionOrders.get(0),templatePath);
			createReport(productionOrders,document,"pdf");
			response.setStatus(Constant.REQUEST_SUCCESS);
			return response;
		}
		//生成报告记录确保报告记录可以生成
		XWPFDocument document =new XWPFDocument();
		createReport(productionOrders,document,"docx");
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
		//判断生成类型
//		if(productionOrders.get(0).getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
//			try {
//				//测筛工单
//				JSONObject jsonData = this.getReprotData(productionOrders);
//				System.out.println(JSON.toJSONString(jsonData));
//				template+="生产部报告.docx";
//				//生成报告
//				document = rt.createDOCXDocument(template, jsonData);
////				rt.writeDOCX(template, jsonData,"D:/资料/君信/生产_" + System.currentTimeMillis() + ".docx");
//				createReport(productionOrders.get(0),document,"docx");
//				response.setStatus(Constant.REQUEST_SUCCESS);
//				return response;
//			}catch (Exception e){
//				throw new ServiceException("生成报告失败");
//			}
//		}
//		//可靠性工单,DPA报告
//		if(productionOrders.get(0).getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)
//				&& productionOrders.get(0).getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_DPA)){
//			try{
//				JSONObject jsonData = this.getDpaReportData(productionOrders.get(0));
//				System.out.println(JSON.toJSONString(jsonData));
//				template+="可靠性DPA分析报告.docx";
//				rt.writeDOCX(template, jsonData,"D:/资料/君信/DPA_" + System.currentTimeMillis() + ".docx");
//				document = rt.createDOCXDocument(template, jsonData);
//				createReport(productionOrders.get(0),document,"docx");
//			}catch (Exception e){
//				throw new ServiceException("生成报告失败");
//			}
//		}
//		response.setStatus(Constant.REQUEST_SUCCESS);
//		return response;
	}

	private XWPFDocument  createUnscreenableReport(ProductionOrderVO productionOrderVO,String template) {
		ReportDocxTool rt = new ReportDocxTool();
		JSONObject jsonData = new JSONObject();
		jsonData.put("p0", productionOrderVO.getEntrustedUnit());
		jsonData.put("p1", productionOrderVO.getProductName());//产品名称
		jsonData.put("p2", productionOrderVO.getProductModel());//产品型号
		jsonData.put("p3", productionOrderVO.getManufacturer());//生产批次
		jsonData.put("p4", createNotTestReason(productionOrderVO,NON_SCREENED));//	不能进行测试原因
		jsonData.put("p7", createNotTestReason(productionOrderVO,NON_POWER_AGING));//不能进行老化原因
		System.out.println(JSON.toJSONString(jsonData));
		XWPFDocument document = rt.createDOCXDocument(template, jsonData);
		return document;
	}

	private void createReport(List<ProductionOrderVO> workOrders, XWPFDocument document, String fileType){
		try {
			String fileName = workOrders.get(0).getWorkOrderNumber();
			byte[] fileBytes = new byte[0];
			if(fileType.equals("pdf")){
				fileName += ".pdf";
				fileBytes = DocxToPdfConverter.createAndConvertToPdfBytesStreaming(document);
			}else if(fileType.equals("docx")){
				fileName += ".pdf";
				fileBytes = DocxToPdfConverter.convertXWPFDocumentToBytes(document);
			}
			String uploadUrl = commonFileService.uploadWordDocument(fileBytes,fileName);
			String reportWorkOrderNumber =workOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.joining(","));
			//更新工单的报告状态
			QueryWrapper wrapper = new QueryWrapper();
			if(workOrders.get(0).getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALIFICATION_TEST)
					|| workOrders.get(0).getTestType().equals(DicConstant.ProductionOrder.TEST_TYPE_QUALITY_CONSISTENCY)){
				wrapper.likeLeft("work_order_number", workOrders.get(0).getWorkOrderNumber().split("-")[0]);
				reportWorkOrderNumber=workOrders.get(0).getWorkOrderNumber().split("-")[0];
				List<ProductionOrder> productionOrders = awaitingProductionOrderMapper.selectList(wrapper);
				for (ProductionOrder productionOrder : productionOrders) {
					productionOrder.setReportStatus(DicConstant.ReportManagement.REPORT_STATUS_DRAFT);
					awaitingProductionOrderMapper.updateById(productionOrder);
				}
			}else{
				for(ProductionOrderVO vo :workOrders){
					ProductionOrder workOrder = new ProductionOrder();
					workOrder.setId(vo.getId());
					workOrder.setReportStatus(DicConstant.ReportManagement.REPORT_STATUS_DRAFT);
					awaitingProductionOrderMapper.updateById(workOrder);
				}
			}
			//保存报告
			ReportManagement entity = new ReportManagement();
			entity.setWorkOrderNumber(reportWorkOrderNumber);
			TorchResponse reportResponse = codeManagementService.getOrderNumber("GDBG");
			entity.setReportNumber(reportResponse.getData().getData().toString());
			entity.setCompilationTime(new Timestamp(System.currentTimeMillis()));
			entity.setPreparerOfTheReport(SecurityContextHolder.getCurrentUserId());
			entity.setReportStatus(DicConstant.CommonDic.DRAFT);
			entity.setAttachment(uploadUrl);
			entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
			entity.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
			reportManagementMapper.insert(entity);

		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	//获取不能测试原因
	private String createNotTestReason(ProductionOrderVO productionOrderVO, String typeCode) {
		// 获取不能进行测试的原因列表
		List<Map<String, String>> reasons = dicDetailService.findDicDetailByDicCode(typeCode);
		JSONArray result = new JSONArray();

		// 根据类型获取对应的原因字符串（可能是逗号拼接的多个值）
		String reasonStr = typeCode.equals(NON_SCREENED)
				? productionOrderVO.getIrretrievableReason()
				: productionOrderVO.getNonAgingReason();

		// 确定要使用的字段
		String primaryField = typeCode.equals(NON_SCREENED) ? "p6" : "p9";
		String checkField = typeCode.equals(NON_SCREENED) ? "p5" : "p8";

		for (Map<String, String> reason : reasons) {
			JSONObject item = new JSONObject();
			String dicValue = reason.get("value");
			String dicLabel = reason.get("label");

			// 设置主要字段（显示原因文本）
			item.put(primaryField, dicLabel);

			// 检查当前字典值是否包含在原因字符串中（处理逗号分隔的情况）
			boolean isSelected = false;
			if (!StringUtils.isEmpty(reasonStr) && !StringUtils.isEmpty(dicValue)) {
				// 分割原因字符串为集合
				Set<String> reasonSet = new HashSet<>(Arrays.asList(reasonStr.split(",")));
				// 判断是否包含当前字典值
				isSelected = reasonSet.contains(dicValue.trim());
			}

			// 设置选中状态
			item.put(checkField, isSelected ? "☑" : "□");

			result.add(item);
		}

		return result.toString();
	}


	private JSONObject getDpaReportData(ProductionOrderVO productionOrder) {
		JSONObject jsonObject = new JSONObject();
		String workOrderNumber = productionOrder.getWorkOrderNumber();
//		List<String> workOrderNumbers = productionOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.toList());
//		Collections.sort(workOrderNumbers);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order", workOrderNumber);
		ProductionOrderResult ordertestResult = productionOrderResultMapper.selectOne(wrapper);
		List<ProductionOrderProcessTest> tests = productionOrderProcessTestMapper.selectList(wrapper);
		//第一页数据
		jsonObject.put("entrustedUnit", productionOrder.getEntrustedUnit());
		jsonObject.put("productModel", productionOrder.getProductModel());
		jsonObject.put("productName", productionOrder.getProductName());
		jsonObject.put("manufacturer", productionOrder.getManufacturer());
		jsonObject.put("analyticalForm", "DPA分析");
		if(!ObjectUtils.isEmpty(ordertestResult))
		jsonObject.put("analysisConclusion", ordertestResult.getInspectionResult());
		//第二页数据
		jsonObject.put("workOrderNumber", workOrderNumber);
		//第三页数据
		//查询当前登录人的签名
		JSONObject image= new JSONObject();
		TorchResponse<SysUserVO> userResponse = sysUserService.findUser(SecurityContextHolder.getCurrentUserId());
		if(StringUtils.isNotBlank(userResponse.getData().getData().getElectronicSignature())) {
			image.put("dataType", "image");
			image.put("content", readImage(userResponse.getData().getData().getElectronicSignature()));
			image.put("w", 80);
			image.put("h", 50);

		}
		jsonObject.put("preparedBy", image);
//		jsonObject.put("preparedBy", "admin");
		jsonObject.put("preparedDate", sdf.format(new Date()));
//		jsonObject.put("reviewer", SecurityContextHolder.getCurrentUserName());
//		jsonObject.put("reviewDate", sdf.format(new Date()));
//		jsonObject.put("approver", SecurityContextHolder.getCurrentUserName());
//		jsonObject.put("approveDate", sdf.format(new Date()));
		//第四页数据
		//查询工单的提交时间
		List<String> orderIds = new ArrayList<>();
		orderIds.add(productionOrder.getId());
		List<SysProcessRecord> processRecodes = sysProcessRecordService.selectListByFromId(orderIds);
		Optional<String> earliestTime = processRecodes.stream()
				.map(SysProcessRecord::getCreateTime)
				.filter(Objects::nonNull)
				.min(Comparator.naturalOrder())
				.map(timestamp -> {
					// 使用 DateTimeFormatter 进行格式化
					return timestamp.toLocalDateTime()
							.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
				});
		String lastTime ="";
		if(!ObjectUtils.isEmpty(productionOrder.getCompletionTime()))
		lastTime = productionOrder.getCompletionTime().toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
		String analysisOverview="受"+productionOrder.getEntrustedUnit()+"委托，对"+productionOrder.getProductModel()
				+"型"+productionOrder.getProductName()+"进行DPA分析，本次分析从"+earliestTime.get()+"开始至"+lastTime+"结束";
		jsonObject.put("analysisOverview", analysisOverview);
		//查询所有工序对应的产品资料或标准规范
		Map<String, List<String>> groupedMap = tests.stream()
				.collect(Collectors.groupingBy(ProductionOrderProcessTest::getWorkOrder,
						Collectors.mapping(ProductionOrderProcessTest::getProcessCode,
								Collectors.toList())));
		List<SelectOptionsVO> options= customerExperimentProjectMapper.selectStandardSpecificationByProcessCodes(productionOrder.getId(),groupedMap.get(workOrderNumber));

		List<SelectOptionsVO> distinctOptions = options.stream()
				.collect(Collectors.toMap(
						vo -> vo.getValue() + "|" + vo.getLabel(),
						vo -> vo,
						(existing, replacement) -> existing
				)).values().stream().collect(Collectors.toList());
		String basisOfAnalysis="";
		for(int i=0;i<distinctOptions.size();i++){
			basisOfAnalysis+=i+1+"."+distinctOptions.get(i).getValue()+" "+distinctOptions.get(i).getLabel()+"\n";
		}
		jsonObject.put("basisOfAnalysis", basisOfAnalysis);
		jsonObject.put("productionBatch", productionOrder.getProductionBatch()==null?"/":productionOrder.getProductionBatch());
		//查询工单对应产品中的样品数量和样品总数，质量等级，
		ProductList productList = productListMapper.selectById(productionOrder.getProductListId());
		jsonObject.put("samplingSize", productList.getQuantity());
		jsonObject.put("totalSampleSize", productList.getSampleTotalCount());
		jsonObject.put("qualityGrades", productList.getQualityGrade());
		jsonObject.put("sampleSoruce", productList.getSampleTotalCount()>productList.getQuantity()?"随机抽样":"客户送样");
		jsonObject.put("analysisConclusion", ordertestResult.getInspectionConclusion());
		jsonObject.put("otherRemark", ordertestResult.getOthereExplanationRemark()==null?"/":ordertestResult.getOthereExplanationRemark());
		SimpleDateFormat sf = new SimpleDateFormat("yyyy年MM月dd日");
		jsonObject.put("yearMonthDay",sf.format(new Date()));
		//第五页数据
		JSONObject tab = new JSONObject();
		List<List<JSONObject>> theadrow = new ArrayList<>();
		List<JSONObject> thead = new ArrayList<>();
		JSONArray tbody = new JSONArray();
		tab.put("dataType", "table");
		JSONObject th1 = new JSONObject(); th1.put("value", "序号");
		JSONObject th2 = new JSONObject(); th2.put("value", "分析项目");
		JSONObject th3 = new JSONObject(); th3.put("value", "样品数");
		JSONObject th4 = new JSONObject(); th4.put("value", "合格数");
		JSONObject th5 = new JSONObject(); th5.put("value", "不合格数");
		JSONObject th6 = new JSONObject(); th6.put("value", "备注");
		thead.add(th1);thead.add(th2);thead.add(th3);thead.add(th4);thead.add(th5);thead.add(th6);

		theadrow.add(thead);

		List<String> processDates = tests.stream().map(ProductionOrderProcessTest::getProcessData).collect(Collectors.toList());
		List<ProductionTaskVO> taskVOS = new ArrayList<>();
		processDates.stream().forEach(p->{
			taskVOS.add(JSON.parseObject(p, ProductionTaskVO.class));
		});
		int xuhao =1;
		for (ProductionTaskVO t : taskVOS) {
			List<JSONObject> indexThead = new ArrayList<>();
			JSONObject tr1 = new JSONObject(); tr1.put("value",String.valueOf(xuhao));
			JSONObject tr2 = new JSONObject(); tr2.put("value",t.getProcessName2());
			JSONObject tr3 = new JSONObject(); tr3.put("value",t.getInspectionQuantity2());
			JSONObject tr4 = new JSONObject(); tr4.put("value",t.getQualifiedQuantity());
			JSONObject tr5 = new JSONObject(); tr5.put("value",t.getUnqualifiedQuantity());
			JSONObject tr6 = new JSONObject();
			if(StringUtils.isNotEmpty(t.getReportWorkRemarks())){
				tr6.put("value", t.getReportWorkRemarks());
			}else{
				tr6.put("value", "/");
			}

			indexThead.add(tr1);indexThead.add(tr2);indexThead.add(tr3);indexThead.add(tr4);indexThead.add(tr5);indexThead.add(tr6);
			theadrow.add(indexThead);
			xuhao++;
		}
		tab.put("rows", theadrow);
		jsonObject.put("analysisProjectAndResults", tab);
		//第6页数据
		JSONArray records = new JSONArray();
		tests.forEach(t->{
			JSONObject processRecord = new JSONObject();
			processRecord.put("productName", productionOrder.getProductName());
			processRecord.put("productModel", productionOrder.getProductModel());
			processRecord.put("productionBatch", productionOrder.getProductionBatch()==null?"/":productionOrder.getProductionBatch());
			ProductionTaskVO taskVO = JSON.parseObject(t.getProcessData(), ProductionTaskVO.class);
			processRecord.put("processName",taskVO.getProcessName2());
			processRecord.put("temperatureAnHumidity",taskVO.getTemperature()+"\n"+taskVO.getHumidity());
			List<ProdTaskEqInfoVO> deviceList = JSON.parseArray(t.getDeviceData(), ProdTaskEqInfoVO.class);
			List<EquipmentInventoryVO> equipmentInventoryVOS = new ArrayList<>();
			//根据设备编号查询设备信息
			deviceList.stream().forEach(x->{
				wrapper.clear();
				wrapper.eq("device_serial_number",x.getDeviceSerialNumber());
				EquipmentInventory equipment = equipmentInventoryMapper.selectOne(wrapper);
				if(null!= equipment){
					//查询设备的有效期
					String validityPeriod2 = deviceTraceabilityMapper.selectValidityPeriod(equipment.getId(),taskVO.getActualEndTime());
					EquipmentInventoryVO vo = new EquipmentInventoryVO();
					BeanUtils.copyProperties(equipment,vo);
					vo.setTracebackValidityPeriod(validityPeriod2);
					equipmentInventoryVOS.add(vo);
				}
			});
			List<JSONObject> JsonDeviceList = new ArrayList<>();
			JSONObject device = new JSONObject();
			String deviceName="主要分析仪器名称及型号";
			String deviceCode="设备编号";
			String validityDate="计量有效期至";
			int index = 1;
			for(EquipmentInventoryVO x : equipmentInventoryVOS){
				String[] strs = new String[6];
				if(index==1){
					strs[0]=deviceName;
					strs[2]=deviceCode;
					strs[4]=validityDate;
				}else{
					strs[0]=null;
					strs[2]=null;
					strs[4]=null;
				}
				strs[1]=x.getDeviceName();
				strs[3]=x.getDeviceSerialNumber();
				strs[5]=x.getTracebackValidityPeriod();
				device.put("cells",strs);
				JsonDeviceList.add(device);
			};
			JSONObject devicerow = new JSONObject();
			devicerow.put("rows",JsonDeviceList);
			processRecord.put("deviceResult",devicerow);
			processRecord.put("analyticalMethod",taskVO.getJudgmentCriteria());
			processRecord.put("sampleCount",taskVO.getInspectionQuantity2());
			processRecord.put("judgmentCriteria",taskVO.getTestBasis());
			//判断工序是否有试验结果总结
			if(StringUtils.isNotBlank(taskVO.getTestResultSummary())){
				JSONObject defaultResult = new JSONObject();
				JSONObject config = new JSONObject();
				config.put("height", 300);
				config.put("margin", new int[]{5, 5, 5, 5});
				defaultResult.put("config",config);
				defaultResult.put("content",taskVO.getTestResultSummary());
				processRecord.put("defaultResult",defaultResult);
			}
			//查询工序的原始数据
			TorchResponse<List<AttachmentVO>> attachmentResponse = attachmentService.selectAttachmentListByWorkOrderAndProcess(
					workOrderNumber, taskVO.getProcessCode());
			if(StringUtils.isBlank(taskVO.getTestResultSummary()) &&
					attachmentResponse.getStatus() == Constant.REQUEST_SUCCESS && !CollectionUtils.isEmpty(attachmentResponse.getData().getData())){

				JSONObject row = new JSONObject();
				JSONObject config = new JSONObject();
				config.put("cellSpans", new int[]{3, 3});
				config.put("margin", new int[]{3, 3, 3, 3});
				config.put("rowHeights", new Integer[]{150, 15});
				row.put("config", config);
				JSONArray datas = new JSONArray();
				List<AttachmentVO> attachmentVOS = attachmentResponse.getData().getData();
				datas.add(this.createReportImgData(attachmentVOS));
				row.put("datas", datas);
				processRecord.put("result", row);
			}
			processRecord.put("reportResult","本项目分析"+taskVO.getQualifiedQuantity()+"只合格，"+taskVO.getUnqualifiedQuantity()+"只不合格");
			processRecord.put("reportWorkRemarks",taskVO.getReportWorkRemarks()==null?"/":taskVO.getReportWorkRemarks());
			processRecord.put("reprotWorkers",taskVO.getReporter4());

			if(!ObjectUtils.isEmpty(taskVO.getReportingTime0())){
				String reportDate = taskVO.getReportingTime0().toLocalDateTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
				processRecord.put("reprotDate",reportDate);
			}else{
				processRecord.put("reprotDate","/");
			}
			records.add(processRecord);
		});
		jsonObject.put("records", records);
		return  jsonObject;
	}

	private List<JSONObject> createReportImgData(List<AttachmentVO> attachmentVOS) {
		List<JSONObject> imgData = new ArrayList<>();
		attachmentVOS.forEach(x->{
			JSONObject data = new JSONObject();
			JSONArray dr = new JSONArray();

			JSONObject image = new JSONObject(); dr.add(image);
			image.put("dataType", "image");
			if(StringUtil.isNotBlank(x.getFilePath())) {
				image.put("content", readImage(x.getFilePath()));
				image.put("w", 200);
				image.put("h", 150);
			}
			JSONObject explan = new JSONObject(); dr.add(explan);
			explan.put("dataType", "string");
			explan.put("height", 30);
			explan.put("content", x.getDescription());
			data.put("rows", dr);
			imgData.add(data);
		});
		return imgData;
	}


	private JSONObject getReprotData(List<ProductionOrderVO> productionOrders) {
		List<String> workOrderNumbers = productionOrders.stream().map(ProductionOrderVO::getWorkOrderNumber).collect(Collectors.toList());
		Collections.sort(workOrderNumbers);
		JSONObject reportVO = new JSONObject();
		reportVO.put("workOrderNumber", workOrderNumbers.get(0));
		reportVO.put("entrustedUnit",productionOrders.get(0).getEntrustedUnit());
		reportVO.put("productModel",productionOrders.get(0).getProductModel());
		reportVO.put("productionBatch",productionOrders.get(0).getProductionBatch());
		Integer quantity =productionOrders.stream().map(ProductionOrderVO::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
		reportVO.put("quantity",quantity);
		reportVO.put("productName",productionOrders.get(0).getProductName());
		reportVO.put("manufacturer",productionOrders.get(0).getManufacturer());
		//查询测评订单
		ProductList productlist = productListMapper.selectById(productionOrders.get(0).getProductListId());
		EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productlist.getEvaluationOrderId());
		reportVO.put("qualityGrade",productlist.getQualityGrade());
		reportVO.put("engineeringCode",evaluationOrder.getEngineeringCode());
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		QueryWrapper wrapper = new QueryWrapper();
		wrapper.eq("work_order",workOrderNumbers.get(0));
		ProductionOrderResult orderResult = productionOrderResultMapper.selectOne(wrapper);
		if(!ObjectUtils.isEmpty(orderResult)){
			reportVO.put("screeningBasis",orderResult.getTestSpecifications());
		}else{
			reportVO.put("screeningBasis","/");
		}
		reportVO.put("contact",evaluationOrder.getPrincipal());
		reportVO.put("phone",evaluationOrder.getPrincipalsPhoneNumber());

		// 获取最大的完成时间（Timestamp类型）
		Optional<Timestamp> maxCompletionTime = productionOrders.stream()
				.map(ProductionOrderVO::getCompletionTime)
				.filter(Objects::nonNull)
				.max(Timestamp::compareTo);

		if (maxCompletionTime.isPresent()) {
			// 将Timestamp转换为Date，然后格式化为String
			Date date = new Date(maxCompletionTime.get().getTime());
			reportVO.put("completionDate",sdf.format(date));
		} else {
			// 处理没有完成时间的情况
			reportVO.put("completionDate","");
		}
		if(null !=productionOrders.get(0).getEntrustmentDate()){
			reportVO.put("entrustmentDate",sdf.format(productionOrders.get(0).getEntrustmentDate()));
		}else{
			reportVO.put("entrustmentDate","/");
		}

		wrapper.clear();
		wrapper.in("work_order", workOrderNumbers);
		List<ProductionOrderProcessTest> tests = productionOrderProcessTestMapper.selectList(wrapper);
		List<String> processDates = tests.stream().map(ProductionOrderProcessTest::getProcessData).collect(Collectors.toList());

		List<ProductionTaskVO> taskVOS = new ArrayList<>();

		processDates.stream().forEach(p->{
			taskVOS.add(JSON.parseObject(p, ProductionTaskVO.class));
		});

		//组装试验项目数据
		//查询失效模式的字典名称
		TorchResponse<List<Map<String,String>>> response= dicDetailService.findDicDetail("production_task_failureMode");
		Map<String, String> failureModeDictMap = new HashMap<>();
		if (response != null && response.getData() != null) {
			failureModeDictMap = response.getData().getData().stream()
					.filter(map -> map.get("value") != null && map.get("label") != null)
					.collect(Collectors.toMap(
							map -> map.get("value").toString(),
							map -> map.get("label").toString(),
							(existing, replacement) -> existing
					));
		}
		Map<String, List<ProductionTaskVO>> grouped = taskVOS.stream()
				.collect(Collectors.groupingBy(ProductionTaskVO::getProcessName2));
		final Map<String, String> finalFailureModeDictMap = failureModeDictMap;
//		JSONObject objectResult = new JSONObject();
//		List<List<JSONObject>> rows = new ArrayList<>();
//		JSONObject aggregated = new JSONObject();

		JSONObject tab = new JSONObject();
		List<List<JSONObject>> theadrow = new ArrayList<>();

		JSONArray tbody = new JSONArray();
		tab.put("dataType", "table");
//		JSONObject th1 = new JSONObject(); th1.put("value", "序号");
//		JSONObject th2 = new JSONObject(); th2.put("value", "分析项目");
//		JSONObject th3 = new JSONObject(); th3.put("value", "样品数");
//		JSONObject th4 = new JSONObject(); th4.put("value", "合格数");
//		JSONObject th5 = new JSONObject(); th5.put("value", "不合格数");
//		JSONObject th6 = new JSONObject(); th6.put("value", "备注");
//		thead.add(th1);thead.add(th2);thead.add(th3);thead.add(th4);thead.add(th5);thead.add(th6);

//		theadrow.add(thead);


		Integer xuhao = 1;
		for (Map.Entry<String, List<ProductionTaskVO>> entry : grouped.entrySet()) {
			List<JSONObject> thead = new ArrayList<>();
			List<ProductionTaskVO> list = entry.getValue();
			ProductionTaskVO first = list.get(0);
			// 创建新的聚合对象

			JSONObject th1 = new JSONObject(); th1.put("value", String.valueOf(xuhao));
			// 复制基准属性（以第一个记录为基准）
//			strs[0] =String.valueOf(xuhao);
			JSONObject th2 = new JSONObject(); th2.put("value", first.getProcessName2().isEmpty()? "/" :first.getProcessName2());
//			strs[1] = first.getProcessName2();
			String conditions = list.stream()
					.map(ProductionTaskVO::getTestConditions)
					.filter(condition -> condition != null && !condition.trim().isEmpty())
					.distinct()
					.collect(Collectors.joining(";"));
			JSONObject th3 = new JSONObject(); th3.put("value",conditions.isEmpty()? "/" :conditions);
			// 计算合计数量
			Integer totalQualified = list.stream()
					.map(ProductionTaskVO::getQualifiedQuantity)
					.filter(q -> q != null)
					.reduce(0, Integer::sum);
			Integer totalUnqualified = list.stream()
					.map(ProductionTaskVO::getUnqualifiedQuantity)
					.filter(q -> q != null)
					.reduce(0, Integer::sum);
			JSONObject th4 = new JSONObject(); th4.put("value", String.valueOf(totalQualified).isEmpty()? "/" : String.valueOf(totalQualified));
//			strs[3] =String.valueOf(totalQualified);
			JSONObject th5 = new JSONObject(); th5.put("value", String.valueOf(totalUnqualified).isEmpty()? "/" : String.valueOf(totalUnqualified));
//			strs[4] =String.valueOf(totalUnqualified);

			// 合并试验条件（去重）

//			strs[2] =list.stream()
//					.map(ProductionTaskVO::getTestConditions)
//					.filter(condition -> condition != null && !condition.trim().isEmpty())
//					.distinct()
//					.collect(Collectors.joining(";"));

			// 失效模式转换和合并
			String mergedFailureMode = list.stream()
					.map(ProductionTaskVO::getFailureMode)
					.filter(mode -> mode != null && !mode.trim().isEmpty())
					.flatMap(mode -> Arrays.stream(mode.split("[；;，,]"))
							.map(String::trim)
							.filter(code -> !code.isEmpty()))
					.distinct()
					.map(code -> finalFailureModeDictMap.getOrDefault(code, code)) // 使用getOrDefault更简洁
					.collect(Collectors.joining(";"));
			JSONObject th6 = new JSONObject(); th6.put("value", mergedFailureMode.isEmpty() ? "/" : mergedFailureMode);
//			strs[5] =mergedFailureMode.isEmpty() ? null : mergedFailureMode;
//			aggregated.put("cells",strs);
			thead.add(th1);thead.add(th2);thead.add(th3);thead.add(th4);thead.add(th5);thead.add(th6);
			theadrow.add(thead);
			xuhao++;
		}

		tab.put("rows",theadrow);
		reportVO.put("result",tab);
//		reportVO.setExperimentProjects(result);
		List<ProductionOrderResult> ordertestResults = productionOrderResultMapper.selectList(wrapper);
		List<String> resultRemarks = ordertestResults.stream().map(ProductionOrderResult::getOthereExplanationRemark).collect(Collectors.toList());
		reportVO.put("remark", resultRemarks.stream().filter(Objects::nonNull).distinct().collect(Collectors.joining(";")));
		//组装设备数据
//		List<EquipmentInventoryVO> equipmentInventoryVOS = new ArrayList<>();
		JSONObject eqtab = new JSONObject();
		List<List<JSONObject>> tqTheadrow = new ArrayList<>();

		eqtab.put("dataType", "table");
//		JSONObject th1 = new JSONObject(); th1.put("value", "序号");
//		JSONObject th2 = new JSONObject(); th2.put("value", "分析项目");
//		JSONObject th3 = new JSONObject(); th3.put("value", "样品数");
//		JSONObject th4 = new JSONObject(); th4.put("value", "合格数");
//		JSONObject th5 = new JSONObject(); th5.put("value", "不合格数");
//		JSONObject th6 = new JSONObject(); th6.put("value", "备注");
//		eqthead.add(th1);eqthead.add(th2);eqthead.add(th3);eqthead.add(th4);eqthead.add(th5);eqthead.add(th6);
//
//		tqTheadrow.add(eqthead);
		for(ProductionOrderProcessTest proccessTest : tests){
			//查询工单工序任务
			ProductionTaskVO  taskVO = productionTaskService.selectProductionTaskByWorkOrderAndProcss(proccessTest.getWorkOrder(),proccessTest.getProcessCode());
			List<ProdTaskEqInfoVO> list = JSON.parseArray(proccessTest.getDeviceData(), ProdTaskEqInfoVO.class);
			//根据设备编号查询设备信息
			int eqxuhao  = 1;
			for (ProdTaskEqInfoVO x : list){
				List<JSONObject> eqthead = new ArrayList<>();

				wrapper.clear();
				wrapper.eq("device_serial_number",x.getDeviceSerialNumber());
				EquipmentInventory equipment = equipmentInventoryMapper.selectOne(wrapper);
				if(null!= equipment){
					//查询设备的有效期
					String validityPeriod1 = deviceTraceabilityMapper.selectValidityPeriod(equipment.getId(),taskVO.getActualStartTime());
					String validityPeriod2 = deviceTraceabilityMapper.selectValidityPeriod(equipment.getId(),taskVO.getActualEndTime());
					JSONObject th1 = new JSONObject(); th1.put("value", String.valueOf(eqxuhao));
					JSONObject th2 = new JSONObject(); th2.put("value", equipment.getDeviceName());
					JSONObject th3 = new JSONObject(); th3.put("value", equipment.getSpecificationModel());
					JSONObject th4 = new JSONObject(); th4.put("value", equipment.getDeviceSerialNumber());
					JSONObject th5 = new JSONObject(); th5.put("value", validityPeriod1+","+validityPeriod2);
					eqthead.add(th1);eqthead.add(th2);eqthead.add(th3);eqthead.add(th4);eqthead.add(th5);
					tqTheadrow.add(eqthead);
				}
				eqxuhao++;
			}
			eqtab.put("rows",tqTheadrow);
			reportVO.put("eqresult",eqtab);
//			reportVO.setEquipments(equipmentInventoryVOS);
		}
		//取最后一个工序任务的合个数量
		Map<String, List<ProductionTaskVO>> groupedByWorkOrder = taskVOS.stream()
				.collect(Collectors.groupingBy(ProductionTaskVO::getWorkOrderNumber));
		List<ProductionTaskVO> filteredRecords = new ArrayList<>();
		for (List<ProductionTaskVO> group : groupedByWorkOrder.values()) {
			Optional<ProductionTaskVO> maxDisplayRecord = group.stream()
					.max(Comparator.comparing(vo ->
							vo.getDisplayNumber() != null ? vo.getDisplayNumber() : Integer.MIN_VALUE));

			maxDisplayRecord.ifPresent(filteredRecords::add);
		}
		int totalQualifiedQuantity = filteredRecords.stream()
				.map(ProductionTaskVO::getQualifiedQuantity)
				.filter(Objects::nonNull)
				.mapToInt(Integer::intValue)
				.sum();
		reportVO.put("qualifiedQuantity", totalQualifiedQuantity);
		int totalUnqualifiedQuantity = ordertestResults.stream()
				.filter(Objects::nonNull) // 过滤掉null对象
				.map(ProductionOrderResult::getUnqualifiedQuantity)
				.filter(Objects::nonNull) // 过滤掉null的unqualifiedQuantity
				.mapToInt(Integer::intValue)
				.sum();
		reportVO.put("unqualifiedQuantity", totalUnqualifiedQuantity);

		reportVO.put("defectiveRate", calculateUnqualifiedRate(quantity,totalUnqualifiedQuantity));
		reportVO.put("preparedBy", SecurityContextHolder.getCurrentUserName());
		reportVO.put("preparedDate", sdf.format(new Date()));
		reportVO.put("reviewer", "/");
		reportVO.put("reviewDate", "/");
		reportVO.put("approver", "/");
		reportVO.put("approveDate", "/");
		return reportVO;
	}

	public BigDecimal calculateUnqualifiedRate(Integer quantity, Integer unqualifiedQuantity) {

//		Integer quantity = productionOrders.stream().map(ProductionOrderVO::getQuantity).filter(Objects::nonNull).reduce(0, Integer::sum);
//		Integer unqualifiedQuantity = reportVO.getUnqualifiedQuantity();

		// 检查空值
		if (quantity == null || unqualifiedQuantity == null) {
			return BigDecimal.ZERO;
		}

		// 检查分母为零的情况
		if (quantity == 0) {
			return BigDecimal.ZERO;
		}

		// 转换为BigDecimal进行计算
		BigDecimal quantityBD = new BigDecimal(quantity);
		BigDecimal unqualifiedBD = new BigDecimal(unqualifiedQuantity);

		// 计算不合格率: (unqualified / quantity) * 100%
		BigDecimal rate = unqualifiedBD
				.divide(quantityBD, 6, RoundingMode.HALF_UP) // 中间计算保留6位小数
				.multiply(new BigDecimal("100"))
				.setScale(2, RoundingMode.HALF_UP); // 最终结果保留2位小数

		return rate;
	}

	private static byte[] readImage(String img) {
		try {
			File file = new File(img);
			FileInputStream fis = new FileInputStream(file);
			byte[] bs = new byte[(int)file.length()];
			fis.read(bs);
			fis.close();
			return bs;
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("文件未找到");
		}
		return new byte[0];
	}

    @Override
    public void exportCard(HttpServletResponse response, List<String> ids) {
        exportCard(response, ids, 1); // 默认每行3张卡片
    }

    /**
     * 导出标识卡Excel（可自定义每行卡片数量）
     * @param response HTTP响应
     * @param ids 工单ID列表
     * @param cardsPerRow 每行显示的卡片数量（1=单列，2=双列，3=三列等）
     */
    public void exportCard(HttpServletResponse response, List<String> ids, int cardsPerRow) {
        try {
            // 查询标识卡数据
            List<Object> cardDataList = getIDCardDataByIds(ids);

            // 导出Excel
            IDCardExcelExporter.exportIDCards(response, cardDataList, cardsPerRow);

        } catch (Exception e) {
            log.error("标识卡导出失败", e);
            throw new ServiceException("标识卡导出失败: " + e.getMessage());
        }
    }

    /**
     * 根据工单ID列表获取标识卡数据
     *
     * @param ids 工单ID列表
     * @return 标识卡数据列表
     */
    private List<Object> getIDCardDataByIds(List<String> ids) {
        List<Object> cardDataList = new ArrayList<>();
        Map<String, String> failureModeDict = getFailureModeDictionary();
        for (String id : ids) {
            // 查询工单基础信息
            ProductionOrderVO productionOrder = awaitingProductionOrderMapper.selectProductionOrderById(id);
            if (productionOrder == null) {
                log.warn("工单不存在，ID: {}", id);
                continue;
            }

            // 查询产品信息
            ProductList productList = productListMapper.selectById(productionOrder.getProductListId());
            if (productList == null) {
                log.warn("产品信息不存在，工单ID: {}", id);
                continue;
            }

            // 查询工单检验结果
            ProductionOrderResultVO orderResult = productionOrderResultMapper.selectByWorkOrder(productionOrder.getWorkOrderNumber());

            // 判断工单类型
            boolean isDPA = DicConstant.ProductionOrder.TEST_TYPE_DPA.equals(productList.getTestType());

            if (isDPA) {
                // DPA标识卡
                ProductionOrderIDCardDPAVO dpaCard = createDPAIDCard(productionOrder, productList, orderResult);
                cardDataList.add(dpaCard);
            } else {
                // 非DPA标识卡
                ProductionOrderIDCardVO normalCard = createNormalIDCard(productionOrder, productList, orderResult);
                cardDataList.add(normalCard);

                // 如果有不合格数量，生成失效标识卡
                if (orderResult != null && orderResult.getUnqualifiedQuantity() != null && orderResult.getUnqualifiedQuantity() > 0) {
                    ProductionOrderExpiredCardVO expiredCard = createExpiredIDCard(productionOrder, productList, orderResult,failureModeDict);
                    cardDataList.add(expiredCard);
                }
            }
        }

        return cardDataList;
    }

    /**
     * 创建非DPA标识卡
     */
    private ProductionOrderIDCardVO createNormalIDCard(ProductionOrderVO productionOrder, ProductList productList, ProductionOrderResultVO orderResult) {
        ProductionOrderIDCardVO card = new ProductionOrderIDCardVO();

        // 基础信息
        card.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        card.setEntrustedUnit(productionOrder.getEntrustedUnit());
        card.setProductModel(productList.getProductModel());
        card.setProductionBatch(productList.getProductionBatch());

        // 查询最后一个工序的合格数
        Long qualifiedQuantity = productionTaskService.getLastProcessQualifiedQuantity(productionOrder.getWorkOrderNumber()).getData().getData();
        card.setQualifiedQuantity(qualifiedQuantity != null ? qualifiedQuantity.intValue() : 0);

        // 失效数量
        if (orderResult != null) {
            card.setFailureQuantity(orderResult.getUnqualifiedQuantity());
        }

        return card;
    }

    /**
     * 创建失效标识卡
     */
    private ProductionOrderExpiredCardVO createExpiredIDCard(ProductionOrderVO productionOrder,
                                                             ProductList productList, ProductionOrderResultVO orderResult,Map<String, String> failureModeDict) {
        ProductionOrderExpiredCardVO card = new ProductionOrderExpiredCardVO();

        // 基础信息
        card.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        card.setEntrustedUnit(productionOrder.getEntrustedUnit());
        card.setProductModel(productList.getProductModel());
        card.setProductionBatch(productList.getProductionBatch());
        card.setFailureQuantity(orderResult.getUnqualifiedQuantity());

        // 获取失效模式备注
        String remark = getFailureModeRemark(productionOrder.getWorkOrderNumber(),failureModeDict);
        card.setRemark(remark);

        return card;
    }

    /**
     * 创建DPA标识卡
     */
    private ProductionOrderIDCardDPAVO createDPAIDCard(ProductionOrderVO productionOrder, ProductList productList, ProductionOrderResultVO orderResult) {
        ProductionOrderIDCardDPAVO card = new ProductionOrderIDCardDPAVO();

        // 基础信息
        card.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        card.setEntrustedUnit(productionOrder.getEntrustedUnit());
        card.setEntrustedType(BusinessConstant.DPA); // 固定为DPA
        card.setProductModel(productList.getProductModel());
        card.setProductionBatch(productList.getProductionBatch());

        // 结论
        if (orderResult != null) {
            card.setConclusion(orderResult.getInspectionConclusion());
        }

        return card;
    }

    /**
     * 获取失效模式备注
     *
     * @param workOrderNumber 工单编号
     * @return 失效模式备注字符串
     */
    private String getFailureModeRemark(String workOrderNumber,Map<String, String> failureModeDict) {
        try {
            // 查询工单的所有不合格工序信息
            List<UnqualifiedProcessVO> unqualifiedProcesses = productionTaskService.getUnqualifiedTaskInfo(workOrderNumber).getData().getData();

            if (CollectionUtils.isEmpty(unqualifiedProcesses)) {
                return "";
            }


            // 收集所有失效模式
            Set<String> failureModes = new HashSet<>();
            for (UnqualifiedProcessVO process : unqualifiedProcesses) {
                if (StringUtils.isNotEmpty(process.getFailureMode())) {
                    // 从字典Map中获取对应的值
                    String dictValue = failureModeDict.get(process.getFailureMode().trim());
                    if (StringUtils.isNotEmpty(dictValue)) {
                        failureModes.add(dictValue);
                    }
                }
            }

            return String.join(",", failureModes);

        } catch (Exception e) {
            log.error("获取失效模式备注失败，工单编号: {}", workOrderNumber, e);
            return "";
        }
    }

    /**
     * 获取失效模式字典数据（一次性查询所有数据）
     *
     * @return 失效模式字典Map，key为字典编码，value为字典名称
     */
    private Map<String, String> getFailureModeDictionary() {
        try {
            TorchResponse response = dicDetailService.findDicDetail("production_task_failureMode");
            if (response != null && response.getData() != null && response.getData().getData() != null) {
                List<Map<String, String>> dictList = (List<Map<String, String>>) response.getData().getData();
                return dictList.stream()
                        .collect(Collectors.toMap(
                                item -> item.get("value"),
                                item -> item.get("label"),
                                (existing, replacement) -> existing // 如果有重复key，保留第一个
                        ));
            }
        } catch (Exception e) {
            log.error("查询失效模式字典失败", e);
        }
        return new HashMap<>();
    }



	public static void main(String[] args) {
		//☑
		//□
		/**
		 * 1 、 不能进行测试的原因
		 * □   无相应器件详细资料
		 * □   现有测试设备硬件资源不够（硬件资源）
		 * ☑   无相应器件测试应用软件（硬件资源）
		 * □   无相应测试适配器（特殊封装无测试夹具）
		 * □   测试设备有故障
		 * □   规范无此分类
		 * 2 、 不能进行功率老化原因
		 * □  无相应器件详细资料
		 * □  无相应老化设备
		 * □  有设备但无相应老化板
		 * □  老化设备有故障
		 */
		ReportDocxTool rt = new ReportDocxTool();
		JSONObject jsonData = new JSONObject();
		jsonData.put("p0", "北京航宇创通技术股份有限公司");
		jsonData.put("p1", "存储器");
		jsonData.put("p2", "S29GL01GP13TFIV10");
		jsonData.put("p3", "2010                                     ");
		jsonData.put("p4", createArrayP5());
		jsonData.put("p7", createArrayP7());
		String template = BusinessConstant.TEMPLATEPATH+"不可筛报告3.docx";
		rt.writeDOCX(template, jsonData,  BusinessConstant.TEMPLATEPATH+"table_" + System.currentTimeMillis() + ".docx");
	}

	private static Object createArrayP5() {
		JSONArray array = new JSONArray();
		JSONObject object1 = new JSONObject();
		object1.put("p5", "□");
		object1.put("p6", "现有测试设备硬件资源不够（硬件资源）");
		array.add(object1);
		
		JSONObject object2 = new JSONObject();
		object2.put("p5", "☑");
		object2.put("p6", "无相应器件详细资料");
		array.add(object2);

		return array;
	}
	private static Object createArrayP7() {

		JSONArray array = new JSONArray();
		JSONObject object1 = new JSONObject();
		object1.put("p8", "□");
		object1.put("p9", "现有测试设备硬件资源不够（硬件资源）");
		array.add(object1);

		JSONObject object2 = new JSONObject();
		object2.put("p8", "☑");
		object2.put("p9", "无相应器件详细资料");
		array.add(object2);
		return array;
	}

}