package com.huatek.frame.modules.business.utils.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.alibaba.excel.util.IoUtils;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
/**
 * <AUTHOR>
 * @date 2023/10/26 16:48
 * @Description 单个图片
 */
public class UrlImageConverter implements Converter<URL> {
    public UrlImageConverter() {
    }

    public Class supportJavaTypeKey() {
        return URL.class;
    }

    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.IMAGE;
    }

    public URL convertToJavaData(CellData cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) {
        throw new UnsupportedOperationException("Cannot convert images to url.");
    }

    // 核心方法 读取图片url的io流
    public CellData convertToExcelData(URL value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws IOException {
        InputStream inputStream = null;

        CellData var6;
        try {
            inputStream = value.openStream();
            byte[] bytes = IoUtils.toByteArray(inputStream);
            var6 = new CellData(bytes);
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }

        }

        return var6;
    }
}
