package com.huatek.frame.insite.message.handler;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.WebSocketMessage;
import org.springframework.web.socket.WebSocketSession;


@Component
public class ConnectWebsocket<PERSON>andler implements WebSocketHandler {
    @Override
    // 连接建立时触发
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        System.out.println("连接建立成功："+session.getId());
    }

    @Override
    // 接收到消息时触发
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        System.out.println("收到消息: " + message.getPayload());
    }

    @Override
    // 出现异常时触发
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        System.out.println("发生错误: " + exception.getMessage());
    }

    @Override
    // 连接关闭时触发
    public void afterConnectionClosed(WebSocketSession session, CloseStatus closeStatus) throws Exception {
        System.out.println("连接关闭: " + session.getId());
    }

    @Override
    // 是否支持部分消息
    public boolean supportsPartialMessages() {
        return false;//默认返回false
    }
}
