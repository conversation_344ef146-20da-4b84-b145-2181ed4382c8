package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 开票信息VO实体类
* <AUTHOR>
* @date 2025-07-14
**/
@Data
@ApiModel("开票信息DTO实体类")
public class InvoiceInformationVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 客户编号
     **/
    @ApiModelProperty("客户编号")
    private String customerId0;
    
    /**
	 * 名称
     **/
    @ApiModelProperty("名称")
    @Excel(name = "*名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String name0;
    
    /**
	 * 税号
     **/
    @ApiModelProperty("税号")
    @Excel(name = "*税号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String taxNumber;
    
    /**
	 * 开票地址
     **/
    @ApiModelProperty("开票地址")
    @Excel(name = "开票地址",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String billingAddress;
    
    /**
	 * 开票电话
     **/
    @ApiModelProperty("开票电话")
    @Excel(name = "开票电话",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String billingTelephone;
    
    /**
	 * 开户银行
     **/
    @ApiModelProperty("开户银行")
    @Excel(name = "开户银行",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String openingBank;
    
    /**
	 * 开户账号
     **/
    @ApiModelProperty("开户账号")
    @Excel(name = "开户账号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String bankAccountNumber3;
    
    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}