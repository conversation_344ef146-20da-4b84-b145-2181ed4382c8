package com.huatek.frame.insite.message.service.impl;

import com.huatek.frame.common.utils.DateUtils;
import com.huatek.frame.insite.message.domain.WbNoticeMessage;
import com.huatek.frame.insite.message.mapper.WbNoticeMessageMapper;
import com.huatek.frame.insite.message.service.IWbNoticeMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通知信息框Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-11
 */
@Service
public class WbNoticeMessageServiceImpl implements IWbNoticeMessageService
{
    @Autowired
    private WbNoticeMessageMapper wbNoticeMessageMapper;

    /**
     * 查询通知信息框
     * 
     * @param id 通知信息框主键
     * @return 通知信息框
     */
    @Override
    public WbNoticeMessage selectWbNoticeMessageById(Long id)
    {
        return wbNoticeMessageMapper.selectWbNoticeMessageById(id);
    }

    /**
     * 查询通知信息框列表
     * 
     * @param wbNoticeMessage 通知信息框
     * @return 通知信息框
     */
    @Override
    public List<WbNoticeMessage> selectWbNoticeMessageList(WbNoticeMessage wbNoticeMessage)
    {
        return wbNoticeMessageMapper.selectWbNoticeMessageList(wbNoticeMessage);
    }

    /**
     * 新增通知信息框
     * 
     * @param wbNoticeMessage 通知信息框
     * @return 结果
     */
    @Override
    public int insertWbNoticeMessage(WbNoticeMessage wbNoticeMessage)
    {
        wbNoticeMessage.setCreateTime(DateUtils.getNowDate());
        return wbNoticeMessageMapper.insertWbNoticeMessage(wbNoticeMessage);
    }

    /**
     * 修改通知信息框
     * 
     * @param wbNoticeMessage 通知信息框
     * @return 结果
     */
    @Override
    public int updateWbNoticeMessage(WbNoticeMessage wbNoticeMessage)
    {
        wbNoticeMessage.setUpdateTime(DateUtils.getNowDate());
        return wbNoticeMessageMapper.updateWbNoticeMessage(wbNoticeMessage);
    }

    /**
     * 批量删除通知信息框
     * 
     * @param ids 需要删除的通知信息框主键
     * @return 结果
     */
    @Override
    public int deleteWbNoticeMessageByIds(Long[] ids)
    {
        return wbNoticeMessageMapper.deleteWbNoticeMessageByIds(ids);
    }

    /**
     * 删除通知信息框信息
     * 
     * @param id 通知信息框主键
     * @return 结果
     */
    @Override
    public int deleteWbNoticeMessageById(Long id)
    {
        return wbNoticeMessageMapper.deleteWbNoticeMessageById(id);
    }

    /**
     * 更新消息的阅读状态
     * @param id 消息的id
     * @return
     */
    @Override
    public int updateWbNoticeMessageReadStatus(Long id) {
        return wbNoticeMessageMapper.updateWbNoticeMessageReadStatus(id);
    }


}
