package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
* @description 试验项目VO实体类
* <AUTHOR>
* @date 2025-07-17
**/
@Data
@ApiModel("试验项目DTO实体类")
public class CustomerExperimentProjectSelectVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;


    /**
     * 显示序号
     **/
    @ApiModelProperty("显示序号")
    private Integer displayNumber;
    /**
     * 工序id
     */
    @ApiModelProperty("工序id")
    private String processId;


    /**
     * 客户试验项目名称
     **/
    @ApiModelProperty("客户工序名称")
    private String customerProcessName;

    /**
     * 执行顺序
     **/
    @ApiModelProperty("执行顺序")
    private Integer executionSequence;

}