package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;

import com.huatek.frame.modules.business.domain.vo.CustomerInfoVO;
import com.huatek.frame.modules.business.domain.vo.CustomerInformationManagementVO;
import com.huatek.frame.modules.business.domain.vo.InvoiceInformationVO;
import com.huatek.frame.modules.business.domain.vo.OtherContactsVO;

import com.huatek.frame.modules.business.domain.vo.UpstreamAndDownstreamVO;
import com.huatek.frame.modules.business.service.*;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.CustomerInformationManagementService;
import com.huatek.frame.modules.business.service.InvoiceInformationService;
import com.huatek.frame.modules.business.service.OtherContactsService;
import com.huatek.frame.modules.business.service.UpstreamAndDownstreamService;
import com.huatek.frame.modules.business.service.dto.OtherContactsDTO;
import com.huatek.frame.modules.business.service.dto.UpstreamAndDownstreamDTO;
import com.huatek.frame.modules.constant.UserInfoConstants;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysUserVO;
import com.huatek.frame.modules.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.CustomerInformationManagement;
import com.huatek.frame.modules.business.mapper.CustomerInformationManagementMapper;
import com.huatek.frame.modules.business.service.dto.CustomerInformationManagementDTO;
import java.sql.Date;
import org.springframework.util.CollectionUtils;

import com.huatek.frame.modules.business.service.dto.InvoiceInformationDTO;




/**
 * 客户信息管理 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-16
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "customerInformationManagement")
//@RefreshScope
@Slf4j
public class CustomerInformationManagementServiceImpl implements CustomerInformationManagementService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private CustomerInformationManagementMapper customerInformationManagementMapper;


    @Autowired
    protected Validator validator;

    @DubboReference
    private SysUserService sysUserService;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

    @Autowired
    private OtherContactsService otherContactsService;

    @Autowired
    private UpstreamAndDownstreamService upstreamAndDownstreamService;

    @Autowired
    private InvoiceInformationService invoiceInformationService;

    @Autowired
    private CodeManagementService codeManagementService;


	public CustomerInformationManagementServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<CustomerInformationManagementVO>> findCustomerInformationManagementPage(CustomerInformationManagementDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<CustomerInformationManagementVO> customerInformationManagements = customerInformationManagementMapper.selectCustomerInformationManagementPage(dto);
		TorchResponse<List<CustomerInformationManagementVO>> response = new TorchResponse<List<CustomerInformationManagementVO>>();
		response.getData().setData(customerInformationManagements);
		response.setStatus(200);
		response.getData().setCount(customerInformationManagements.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(CustomerInformationManagementDTO customerInformationManagementDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(customerInformationManagementDto.getCodexTorchDeleted())) {
            customerInformationManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = customerInformationManagementDto.getId();
		CustomerInformationManagement entity = new CustomerInformationManagement();
        BeanUtils.copyProperties(customerInformationManagementDto, entity);
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        entity.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserName());
		if (HuatekTools.isEmpty(id)) {
            entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
            entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
            TorchResponse response =  codeManagementService.getOrderNumber("YHDM");
            entity.setCustomerId0(response.getData().getData().toString());
			customerInformationManagementMapper.insert(entity);
		} else {
			customerInformationManagementMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        CustomerInformationManagementVO vo = new CustomerInformationManagementVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<CustomerInformationManagementVO> findCustomerInformationManagement(String id) {
		CustomerInformationManagementVO vo = new CustomerInformationManagementVO();
		if (!HuatekTools.isEmpty(id)) {
			CustomerInformationManagement entity = customerInformationManagementMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<CustomerInformationManagementVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		customerInformationManagementMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "customer_information_management", convertorFields = "type,customerPriority,sealedWithCnasSeal")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<CustomerInformationManagementVO> selectCustomerInformationManagementList(CustomerInformationManagementDTO dto) {
        return customerInformationManagementMapper.selectCustomerInformationManagementList(dto);
    }

    /**
     * 导入客户信息管理数据
     *
     * @param customerInformationManagementList 客户信息管理数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "customer_information_management", convertorFields = "type,customerPriority,sealedWithCnasSeal")
    public TorchResponse importCustomerInformationManagement(List<CustomerInformationManagementVO> customerInformationManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(customerInformationManagementList) || customerInformationManagementList.size() == 0) {
            throw new ServiceException("导入客户信息管理数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (CustomerInformationManagementVO vo : customerInformationManagementList) {
            vo.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            vo.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
            vo.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
            vo.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserName());
            try {
                CustomerInformationManagement customerInformationManagement = new CustomerInformationManagement();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, customerInformationManagement);
                customerInformationManagement.setCodexTorchCreateDatetime(new Timestamp(System.currentTimeMillis()));
                customerInformationManagement.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                customerInformationManagement.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
                customerInformationManagement.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
                TorchResponse response =  codeManagementService.getOrderNumber("YHDM");
                customerInformationManagement.setCustomerId0(response.getData().getData().toString());
                QueryWrapper<CustomerInformationManagement> wrapper = new QueryWrapper();
                CustomerInformationManagement oldCustomerInformationManagement = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = CustomerInformationManagementVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<CustomerInformationManagement> oldCustomerInformationManagementList = customerInformationManagementMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldCustomerInformationManagementList) && oldCustomerInformationManagementList.size() > 1) {
                        customerInformationManagementMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldCustomerInformationManagementList) && oldCustomerInformationManagementList.size() == 1) {
                        oldCustomerInformationManagement = oldCustomerInformationManagementList.get(0);
                    }
                }
                if (StringUtils.isNull(oldCustomerInformationManagement)) {
                    BeanValidators.validateWithException(validator, vo);
                    TorchResponse resp =  codeManagementService.getOrderNumber("YHDM");
                    customerInformationManagement.setCustomerId0(resp.getData().getData().toString());
                    //验证客户经理是否存在
                    if (StrUtil.isNotEmpty(vo.getCustomerManager())){
                        List<String> groupidList = new ArrayList<>();
                        groupidList.add(UserInfoConstants.MARKET_ID);
                        List<SysUserVO> users = sysUserService.getUsersByDepart(groupidList);
                        String customerManagerId = "";
                        for (SysUserVO sysUserVO : users){
                            if (StrUtil.equals(sysUserVO.getUserName(), vo.getCustomerManager())){
                                customerManagerId = sysUserVO.getId();
                                break;
                            }
                        }
                        if (StrUtil.isEmpty(customerManagerId)){
                            failureNum++;
                            failureMsg.append("<br/>" + failureNum + String.format("、市场部不存在名称为 %s 的客户经理", vo.getCustomerManager()));
                            continue;
                        }else {
                            customerInformationManagement.setCustomerManager(customerManagerId);
                        }
                    }

                    customerInformationManagementMapper.insert(customerInformationManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、客户编号 " + vo.getCustomerId0() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldCustomerInformationManagement, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    customerInformationManagementMapper.updateById(oldCustomerInformationManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、客户编号 " + vo.getCustomerId0() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、客户编号 " + vo.getCustomerId0() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、客户编号 " + vo.getCustomerId0() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(CustomerInformationManagementVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getEntrustedUnit())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>委托单位不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSettlementUnit())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>结算单位不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSealedWithCnasSeal())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>加盖CNAS章不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectCustomerInformationManagementListByIds(List<String> ids) {
        List<CustomerInformationManagementVO> customerInformationManagementList = customerInformationManagementMapper.selectCustomerInformationManagementListByIds(ids);

		TorchResponse<List<CustomerInformationManagementVO>> response = new TorchResponse<List<CustomerInformationManagementVO>>();
		response.getData().setData(customerInformationManagementList);
		response.setStatus(200);
		response.getData().setCount((long)customerInformationManagementList.size());
		return response;
    }

    /**
     * 客户信息管理主子表单组合提交
     *
	 * @param customerInformationManagementDto 客户信息管理DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(CustomerInformationManagementDTO customerInformationManagementDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(customerInformationManagementDto.getCodexTorchDeleted())) {
            customerInformationManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
        customerInformationManagementDto.setId("");
        customerInformationManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

        //TODO: TorchDetailItemIds TO BE DEPRECATED
        customerInformationManagementDto.setCodexTorchDetailItemIds("");
        CustomerInformationManagementVO masterVo = new CustomerInformationManagementVO();
        try{
            TorchResponse<CustomerInformationManagementVO> masterSubmitResp = this.saveOrUpdate(customerInformationManagementDto);
            masterVo = masterSubmitResp.getData().getData();

            //更新开票明细子表
            List<InvoiceInformationDTO> invoiceInformationDTOs = new ArrayList<>();
            if (customerInformationManagementDto.getInvoiceList() != null && customerInformationManagementDto.getInvoiceList().size() > 0) {
                invoiceInformationDTOs = customerInformationManagementDto.getInvoiceList();
            }
            for(InvoiceInformationDTO invoiceInformationDto : invoiceInformationDTOs){

                //非必要字段处理
                invoiceInformationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
                //主子表关联ID
                invoiceInformationDto.setCodexTorchMasterFormId(masterVo.getId());
                // 业务字段管理
                invoiceInformationDto.setCustomerId0(masterVo.getCustomerId0());
                //提交
                TorchResponse<InvoiceInformationVO> detailSubmitResp = invoiceInformationService.saveOrUpdate(invoiceInformationDto);
                InvoiceInformationVO detailVo = detailSubmitResp.getData().getData();
            }

            //更新其他联系人子表
            List<OtherContactsDTO> otherContactsDTOs = customerInformationManagementDto.getOtherContactsList();
            if (otherContactsDTOs != null){
                for(OtherContactsDTO otherContactsDto : otherContactsDTOs){

                    //非必要字段处理
                    otherContactsDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

                    //主子表关联ID
                    otherContactsDto.setCodexTorchMasterFormId(masterVo.getId());
                    // 业务字段管理
                    otherContactsDto.setCustomerId0(masterVo.getCustomerId0());
                    //提交
                    TorchResponse<OtherContactsVO> detailSubmitResp = otherContactsService.saveOrUpdate(otherContactsDto);
                    OtherContactsVO detailVo = detailSubmitResp.getData().getData();
                }
            }


            //更新上下游子表
            List<UpstreamAndDownstreamDTO> upstreamAndDownstreamDTOs = customerInformationManagementDto.getUpstreamAndDownstreamList();
            if (upstreamAndDownstreamDTOs != null){
                for(UpstreamAndDownstreamDTO upstreamAndDownstreamDto : upstreamAndDownstreamDTOs){

                    //非必要字段处理
                    upstreamAndDownstreamDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

                    //主子表关联ID
                    upstreamAndDownstreamDto.setCodexTorchMasterFormId(masterVo.getId());
                    // 业务字段管理
                    upstreamAndDownstreamDto.setCustomerId0(masterVo.getCustomerId0());
                    //提交
                    TorchResponse<UpstreamAndDownstreamVO> detailSubmitResp = upstreamAndDownstreamService.saveOrUpdate(upstreamAndDownstreamDto);
                    UpstreamAndDownstreamVO detailVo = detailSubmitResp.getData().getData();
                }
            }
        }catch (DuplicateKeyException e){
            throw new ServiceException("存在重复的委托单位");
        }



		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

    @Override
    public TorchResponse<List<CustomerInfoVO>> findAllCustomers() {
        List<CustomerInfoVO> customerInformationManagementList = customerInformationManagementMapper.findAllCustomers();

        TorchResponse<List<CustomerInfoVO>> response = new TorchResponse<List<CustomerInfoVO>>();
        response.getData().setData(customerInformationManagementList);
        response.setStatus(200);
        response.getData().setCount((long)customerInformationManagementList.size());
        return response;
    }


}
