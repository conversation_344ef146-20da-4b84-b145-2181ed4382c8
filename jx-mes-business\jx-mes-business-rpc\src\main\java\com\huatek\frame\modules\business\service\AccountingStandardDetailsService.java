package com.huatek.frame.modules.business.service;


import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDetailsDTO;

import java.util.List;


/**
* @description 核算标准明细Service
* <AUTHOR>
* @date 2025-08-22
**/
public interface AccountingStandardDetailsService {
    
    /**
	 * 分页查找查找 核算标准明细
	 * 
	 * @param dto 核算标准明细dto实体对象
	 * @return 
	 */
	TorchResponse<List<AccountingStandardDetailsVO>> findAccountingStandardDetailsPage(AccountingStandardDetailsDTO dto);

    /**
	 * 添加 \修改 核算标准明细
	 * 
	 * @param accountingStandardDetailsDto 核算标准明细dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(AccountingStandardDetailsDTO accountingStandardDetailsDto);
	
	/**
	 * 通过id查找核算标准明细
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<AccountingStandardDetailsVO> findAccountingStandardDetails(String id);
	
	/**
	 * 删除 核算标准明细
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 核算标准明细
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<AccountingStandardDetailsVO>> getOptionsList(String id);




    /**
     * 根据条件查询核算标准明细列表
     *
     * @param dto 核算标准明细信息
     * @return 核算标准明细集合信息
     */
    List<AccountingStandardDetailsVO> selectAccountingStandardDetailsList(AccountingStandardDetailsDTO dto);

    /**
     * 导入核算标准明细数据
     *
     * @param accountingStandardDetailsList 核算标准明细数据列表
     * @param unionColumns                  作为确认数据唯一性的字段集合
     * @param isUpdateSupport               是否更新支持，如果已存在，则进行更新数据
     * @param operName                      操作用户
     * @param accountingStandardId
     * @return 结果
     */
    TorchResponse importAccountingStandardDetails(List<AccountingStandardDetailsVO> accountingStandardDetailsList, List<String> unionColumns, Boolean isUpdateSupport, String operName, String accountingStandardId);

	/**
	 * 根据IDS获取核算标准明细数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectAccountingStandardDetailsListByIds(List<String> ids);



}