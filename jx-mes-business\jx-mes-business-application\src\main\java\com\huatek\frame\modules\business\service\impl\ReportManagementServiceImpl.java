package com.huatek.frame.modules.business.service.impl;

import camundajar.impl.scala.Product;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.ProductCategory;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.ReportManagement;
import com.huatek.frame.modules.business.domain.vo.ProductionOrderVO;
import com.huatek.frame.modules.business.domain.vo.ReportManagementVO;
import com.huatek.frame.modules.business.mapper.AwaitingProductionOrderMapper;
import com.huatek.frame.modules.business.mapper.ProductCategoryMapper;
import com.huatek.frame.modules.business.mapper.ReportManagementMapper;
import com.huatek.frame.modules.business.service.ReportManagementService;
import com.huatek.frame.modules.business.service.dto.ReportManagementDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.SysUser;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.RoleVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import com.huatek.frame.modules.system.service.DicDetailService;
import com.huatek.frame.modules.system.service.RoleService;
import com.huatek.frame.modules.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;
import com.huatek.frame.modules.bpm.constant.ProcessConstant;
import com.huatek.frame.modules.bpm.dto.FormApprovalDTO;
import com.huatek.frame.modules.bpm.dto.ProcessFormDTO;
import com.huatek.frame.modules.bpm.service.ProcessInstanceProxyService;
import com.huatek.frame.modules.bpm.service.ProcessPrivilegeService;
import com.huatek.frame.modules.bpm.constant.ApprovalStatus;
import org.springframework.util.ObjectUtils;


/**
 * 报告管理 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "reportManagement")
//@RefreshScope
@Slf4j
public class ReportManagementServiceImpl implements ReportManagementService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @DubboReference
    private RoleService roleService;

	@Autowired
	private ReportManagementMapper reportManagementMapper;

    private String shengChanProcessBusinessKey = "生产部报告管理";
    private String keKaoXingProcessBusinessKey = "可靠性报告管理";

    @Autowired
	private ProcessInstanceProxyService processInstanceProxyService;

    @Autowired
    private ProcessPrivilegeService processPrivilegeService;
	@Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @DubboReference
    private DicDetailService dicDetailService;
    @DubboReference
    private SysUserService sysUserService;
    @Autowired
    private ProductCategoryMapper productCategoryMapper;
    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();



	public ReportManagementServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<ReportManagementVO>> findReportManagementPage(ReportManagementDTO dto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
		//Codex - 流程人员流程查看数据权限控制
        if (currentUser.equals(ProcessConstant.SUPER_USER)){
            if(StringUtils.isEmpty(dto.getCodexTorchApplicant())){
                //管理员可查看所有数据
                dto.setCodexTorchApplicant("");
            }

            if(StringUtils.isEmpty(dto.getCodexTorchApprover())){
                dto.setCodexTorchApprover("");
            }
        }else if (dto.getWorkflowQueryRole().equals("applicant")){
            if(currentUser.equals(ProcessConstant.SUPER_USER)){
                dto.setCodexTorchApplicant("");
            }else{
                dto.setCodexTorchApplicant(currentUser);
            }
		}else if(dto.getWorkflowQueryRole().equals("approver")){
             String currentUserId = SecurityContextHolder.getCurrentUserId();
             //===========新增代码============
             List<RoleVO> roleUserVoList = roleService.findUserRoles(currentUserId);
             String[] currentUserRolesArray = new String[roleUserVoList.size()];
             int i = 0;
             for(RoleVO roleUserVo :roleUserVoList){
                 currentUserRolesArray[i++] =roleUserVo.getRole();
             }
             String currentUserRoles = Arrays.stream(Optional.of(currentUserRolesArray).orElse(new String[]{""})).collect(Collectors.joining(", "));
            //=============================
             //多审批人处理
            dto.setCodexTorchApprover(null);
            dto.setCodexTorchApprovers(currentUserId);
             dto.setCodexTorchApproverRole(currentUserRolesArray[0]);
             dto.setCodexTorchApproverRoles(currentUserRolesArray[0]);
            if(StringUtils.isEmpty(dto.getCodexTorchApprovalStatus()) || currentUser.equals(ProcessConstant.SUPER_USER)) {
                dto.setCodexTorchApprovalStatus("待审批|已审批|已驳回");
            }else {
                dto.setCodexTorchApprovalStatus(dto.getCodexTorchApprovalStatus());
            }
		}
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<ReportManagementVO> reportManagements = reportManagementMapper.selectReportManagementPage(dto);
		//CodeX - 多级审批
        updateApproveStatusForCurrentUser(reportManagements,SecurityContextHolder.getCurrentUserId());
		TorchResponse<List<ReportManagementVO>> response = new TorchResponse<List<ReportManagementVO>>();
		response.getData().setData(reportManagements);
		response.setStatus(200);
		response.getData().setCount(reportManagements.getTotal());
		return response;
	}

	/**
	 * 多级审批，不同人看到的审批状态不同
	 *
	 * @param updateStatusVOVOList
	 * @param currentUser
	 */
	private void updateApproveStatusForCurrentUser(List<ReportManagementVO> updateStatusVOVOList,String currentUser){
		for(ReportManagementVO updateStatusVO : updateStatusVOVOList){
			String currentApprover = updateStatusVO.getCodexTorchApprover();
			String approvers = updateStatusVO.getCodexTorchApprovers();
            //===========新增代码============
            String currentApproverRole = updateStatusVO.getCodexTorchApproverRole();
            //============================
			if(StringUtils.isEmpty(approvers)){
				continue;
			}

			//当前用户不在审批人之列，不能处理记录
            if(!approvers.contains(currentUser)){
                continue;
            }

			//当前用户是当前审批人,审批状态是待审批，不需要更新状态
			if(currentApprover.contains(currentUser)
					&& updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
				continue;
			}

            //===========新增代码============
            //当前用户有审批角色,审批状态是待审批，不需要更新状态
            String currentUserId = SecurityContextHolder.getCurrentUserId();
            List<RoleVO> roleUserVoList = roleService.findUserRoles(currentUserId);
            if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())&& StringUtils.isNotEmpty(currentApproverRole)) {
                boolean currentUserHasApprovedRole = false;
                for (RoleVO roleUserVO : roleUserVoList) {
                    String currentRole = roleUserVO.getRole();
                    if (currentApproverRole.equals(currentRole)) {
                        currentUserHasApprovedRole = true;
                        break;
                    }
                }

                if (currentUserHasApprovedRole) {
                    continue;
                }
            }
            //================

			//如果当前用户处在当前审批者之前，更新审批状态为已审批
			if(!currentApprover.equals(currentUser) && (approvers.indexOf(currentApprover) > approvers.indexOf(currentUser))){
				if(updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
					updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
				}
			}

            //如果当前用户在已审批用户中但不是最后一个审批用户，更新审批状态为已审批
            if(!currentApprover.equals(currentUser) && !approvers.endsWith(currentUser)) {
                if (approvers.contains(currentUser)) {
                    if (updateStatusVO.getCodexTorchApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())) {
                        updateStatusVO.setCodexTorchApprovalStatus(ApprovalStatus.APPROVED.getName());
                    }
                }
            }
		}

	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(ReportManagementDTO reportManagementDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(reportManagementDto.getCodexTorchDeleted())) {
            reportManagementDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = reportManagementDto.getId();
		ReportManagement entity = new ReportManagement();
        BeanUtils.copyProperties(reportManagementDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
		if (HuatekTools.isEmpty(id)) {
			reportManagementMapper.insert(entity);
		} else {
			reportManagementMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        ReportManagementVO vo = new ReportManagementVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<ReportManagementVO> findReportManagement(String id) {
		ReportManagementVO vo = new ReportManagementVO();
		if (!HuatekTools.isEmpty(id)) {
			ReportManagement entity = reportManagementMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<ReportManagementVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<ReportManagement> reportManagementList = reportManagementMapper.selectBatchIds(Arrays.asList(ids));
        for (ReportManagement reportManagement : reportManagementList) {
            reportManagement.setCodexTorchDeleted(Constant.DEFAULT_YES);
            reportManagementMapper.updateById(reportManagement);
        }
		//reportManagementMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submit(String[] ids, String token) {
        List<ReportManagement> reportManagementList = reportManagementMapper.selectBatchIds(Arrays.asList(ids));
        for (ReportManagement reportManagement : reportManagementList) {
            reportManagement.setReportStatus(DicConstant.ReportManagement.REPORT_STATUS_WAITAPPROVE);
            reportManagementMapper.updateById(reportManagement);
            //如果其他报告中的工单编号和当前工单的工单编号有任意一个单号重叠，且其他报告有待审批或审批通过的状态则不能提交
            //查询除当前之外的所有已审批通过的报告
            List<String> status = new ArrayList<String>();
            status.add(DicConstant.ReportManagement.REPORT_STATUS_WAITAPPROVE);
            status.add(DicConstant.ReportManagement.REPORT_STATUS_APPROVED);
            QueryWrapper wrapper = new QueryWrapper();
            wrapper.ne("id", reportManagement.getId());
            wrapper.in("report_status", status);
            List<ReportManagement> approvedReportList = reportManagementMapper.selectList(wrapper);
            Set<String> paramWorkOrderSet = new HashSet<>(Arrays.asList(reportManagement.getWorkOrderNumber().split(",")));
            List<ReportManagement> filteredList = approvedReportList.stream()
                    .filter(report -> StringUtils.isNotBlank(report.getWorkOrderNumber()))
                    .filter(report -> {
                        String[] workOrders = report.getWorkOrderNumber().split(",");
                        return Arrays.stream(workOrders)
                                .map(String::trim)
                                .anyMatch(paramWorkOrderSet::contains);
                    }).collect(Collectors.toList());
            if(!CollectionUtils.isEmpty(filteredList)){
                throw new ServiceException("该工单的工单编号已有其他报告待审批或审批通过");
            }
            //更新工单的报告状态
            List<ProductionOrder> orders = awaitingProductionOrderMapper.selectByReportWorkOrderNumber(reportManagement.getWorkOrderNumber());
            for (ProductionOrder order : orders) {
                order.setReportStatus(DicConstant.ReportManagement.REPORT_STATUS_WAITAPPROVE);
                awaitingProductionOrderMapper.updateById(order);
            }
            ReportManagementDTO dto = new ReportManagementDTO();
            BeanUtils.copyProperties(reportManagement,dto);
            this.apply(dto,token);
        }
        TorchResponse  response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("workOrderNumber",reportManagementMapper::selectOptionsByWorkOrderNumber);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}

	@Override
	@Transactional
	public TorchResponse approve(FormApprovalDTO formApprovalDTO, String token) {
		log.info("表单审批,formApprovalDTO:{}",formApprovalDTO);
        if (CollectionUtils.isEmpty(formApprovalDTO.getFormIdList())) {
            singleApproval(formApprovalDTO, token);
        } else {
            for (String formId: formApprovalDTO.getFormIdList()) {
                formApprovalDTO.setFormId(formId);
                singleApproval(formApprovalDTO, token);
            }
        }
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	private void singleApproval(FormApprovalDTO formApprovalDTO, String token) {
        ProcessFormDTO processFormDTO = new ProcessFormDTO();
        BeanUtils.copyProperties(formApprovalDTO,processFormDTO);
        ReportManagement oldreportMent = reportManagementMapper.selectById(processFormDTO.getFormId());
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("work_order_number",oldreportMent.getWorkOrderNumber());
        ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(wrapper);
        if(productionOrder.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)){
            processFormDTO.setBusinessKey(keKaoXingProcessBusinessKey);
        }
        if(productionOrder.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
            processFormDTO.setBusinessKey(shengChanProcessBusinessKey);
        }
        SysProcessRecordVO processRecordResp = processInstanceProxyService.approve(processFormDTO,token);
        ReportManagement updateProcessStatusEntity =reportManagementMapper.selectById(processRecordResp.getFormId());
        updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());
        updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());
        //======新增代码========
        updateProcessStatusEntity.setCodexTorchApproverRole(processRecordResp.getApproverRole());
        updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
        String status = "";
        if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.PENDING_REAPPLY.getName())){
            status=DicConstant.ReportManagement.REPORT_STATUS_REJCCT;
        }
        if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.APPROVED.getName())){
            status=DicConstant.ReportManagement.REPORT_STATUS_APPROVED;
        }
        if(processRecordResp.getApprovalStatus().equals(ApprovalStatus.PENDING_APPROVAL.getName())){
            status=DicConstant.ReportManagement.REPORT_STATUS_WAITAPPROVE;
        }
        updateProcessStatusEntity.setReportStatus(status);
        reportManagementMapper.updateById(updateProcessStatusEntity);
        //更新工单的报告状态
        List<ProductionOrder> orders = awaitingProductionOrderMapper.selectByReportWorkOrderNumber(updateProcessStatusEntity.getWorkOrderNumber());
        for (ProductionOrder order : orders) {
            if(!order.getReportStatus().equals(DicConstant.ReportManagement.REPORT_STATUS_APPROVED)){
                order.setReportStatus(status);
                awaitingProductionOrderMapper.updateById(order);
            }
        }
        log.info("表单审批完成,processRecordResp:{}",processRecordResp);
    }

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse apply(ReportManagementDTO reportManagementDto, String token) {
		log.info("表单提交申请,reportManagementDto:{}",reportManagementDto);

	    JSONObject json = securityUser.currentUser(token);
        String currentUser = json.getString("userName");
		reportManagementDto.setCodexTorchApplicant(currentUser);
		reportManagementDto.setCodexTorchApprovalStatus("待审批");

		TorchResponse<ReportManagementVO> updateVoResp = this.saveOrUpdate(reportManagementDto);
		ReportManagementVO updateVo = updateVoResp.getData().getData();

		//CodeX 表单工作流绑定
        ProcessFormDTO processFormDTO = new ProcessFormDTO();
        processFormDTO.setProcessDefinitionKey("SimpleApprovalProcess");
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("work_order_number",reportManagementDto.getWorkOrderNumber());
        ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(wrapper);
        if(productionOrder.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_RELIABILITY)){
            processFormDTO.setBusinessKey(keKaoXingProcessBusinessKey);
        }
        if(productionOrder.getWorkOrderNumber().startsWith(DicConstant.ProductionOrder.WORK_ORDER_TYPE_SCREENING)){
            processFormDTO.setBusinessKey(shengChanProcessBusinessKey);
        }
        processFormDTO.setFormId(updateVo.getId());
		SysProcessRecordVO processRecordResp = processInstanceProxyService.startProcessByKey(processFormDTO,token);
		ReportManagement updateProcessStatusEntity = new ReportManagement();
		updateProcessStatusEntity.setId(updateVo.getId());
		updateProcessStatusEntity.setCodexTorchApplicant(processRecordResp.getApplicant());
		updateProcessStatusEntity.setCodexTorchApprover(processRecordResp.getApprover());

		//更新审批人列表
		updateProcessStatusEntity.setCodexTorchApprovers(processRecordResp.getApprovers());
        //=====新增代码=========
        updateProcessStatusEntity.setCodexTorchApproverRole(processRecordResp.getApproverRole());
		updateProcessStatusEntity.setCodexTorchApprovalStatus(processRecordResp.getApprovalStatus());
		reportManagementMapper.updateById(updateProcessStatusEntity);

		return updateVoResp;
	}



    @Override
	public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
	    Map<String, String> data = new HashMap();
        try {
	        switch (linkageDataTableName) {
                case "awaiting_production_order":
                    data = selectDataLinkageByWorkOrderNumber(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
	    TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
	}
    @Override
    public Map<String,String> selectDataLinkageByWorkOrderNumber(String work_order_number) {
        return reportManagementMapper.selectDataLinkageByWorkOrderNumber(work_order_number);
    }

    @Override
    @ExcelExportConversion(tableName = "report_management", convertorFields = "reportStatus")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ReportManagementVO> selectReportManagementList(ReportManagementDTO dto) {
        //Codex - 流程人员流程查看数据权限控制
        if (ProcessConstant.SUPER_USER.equals(dto.getCodexTorchApplicant())){
            //管理员可查看所有数据
            dto.setCodexTorchApplicant("");
            dto.setCodexTorchApprover("");
        }else if(StringUtils.isNotEmpty(dto.getCodexTorchApprover())){
            //多审批人处理
            dto.setCodexTorchApprovers(dto.getCodexTorchApprover());
            dto.setCodexTorchApprover(null);
        }
        List<ReportManagementVO> list = reportManagementMapper.selectReportManagementList(dto);
        TorchResponse<List<Map<String,String>>> response = dicDetailService.findDicDetail("report_management0_reportStatus");
        TorchResponse<List<Map<String,String>>> testTypeResponse = dicDetailService.findDicDetail("test_data_dictionary_testType");
        list.stream().forEach(item->{
            if(StringUtils.isNotBlank(item.getReportStatus()))
            item.setReportStatus(response.getData().getData().stream().filter(map->map.get("value").equals(item.getReportStatus())).findFirst().get().get("label"));
            //查询审批人列表和带审批人列表
            if(StringUtils.isNotBlank(item.getCodexTorchApprover())){
                List<SysUser> users = sysUserService.selectUserListByIds(Arrays.asList(item.getCodexTorchApprover().split(",")));
                List<String> names = users.stream().map(SysUser::getUserName).collect(Collectors.toList());
                item.setCodexTorchApprover(String.join(",",names));
            }
            if(StringUtils.isNotBlank(item.getCodexTorchApprovers())){
                List<SysUser> users =sysUserService.selectUserListByIds(Arrays.asList(item.getCodexTorchApprovers().split(",")));
                List<String> names = users.stream().map(SysUser::getUserName).collect(Collectors.toList());
                item.setCodexTorchApprovers(String.join(",",names));
            }
            if(StringUtils.isNotBlank(item.getTestType()))
            item.setTestType(testTypeResponse.getData().getData().stream().filter(map -> map.get("value").equals(item.getTestType())).findFirst().get().get("label"));
            ProductCategory category = productCategoryMapper.selectById(item.getProductCategory());
            if(!ObjectUtils.isEmpty(category)){
                item.setProductCategory(category.getCategoryName());
            }
        });
        return list;
    }

    /**
     * 导入报告管理数据
     *
     * @param reportManagementList 报告管理数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "report_management", convertorFields = "reportStatus")
    public TorchResponse importReportManagement(List<ReportManagementVO> reportManagementList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(reportManagementList) || reportManagementList.size() == 0) {
            throw new ServiceException("导入报告管理数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ReportManagementVO vo : reportManagementList) {
            try {
                ReportManagement reportManagement = new ReportManagement();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, reportManagement);
                QueryWrapper<ReportManagement> wrapper = new QueryWrapper();
                ReportManagement oldReportManagement = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = ReportManagementVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ReportManagement> oldReportManagementList = reportManagementMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldReportManagementList) && oldReportManagementList.size() > 1) {
                        reportManagementMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldReportManagementList) && oldReportManagementList.size() == 1) {
                        oldReportManagement = oldReportManagementList.get(0);
                    }
                }
                if (StringUtils.isNull(oldReportManagement)) {
                    BeanValidators.validateWithException(validator, vo);
                    reportManagementMapper.insert(reportManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、报告编号 " + vo.getReportNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldReportManagement, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    reportManagementMapper.updateById(oldReportManagement);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、报告编号 " + vo.getReportNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、报告编号 " + vo.getReportNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、报告编号 " + vo.getReportNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ReportManagementVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getWorkOrderNumber())) {
            List<String> workOrderNumberList = Arrays.asList(vo.getWorkOrderNumber().split(","));
            List<ProductionOrder> list = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>().in("work_order_number", workOrderNumberList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("工单编号=" + vo.getWorkOrderNumber() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectReportManagementListByIds(List<String> ids) {
        List<ReportManagementVO> reportManagementList = reportManagementMapper.selectReportManagementListByIds(ids);

		TorchResponse<List<ReportManagementVO>> response = new TorchResponse<List<ReportManagementVO>>();
		response.getData().setData(reportManagementList);
		response.setStatus(200);
		response.getData().setCount((long)reportManagementList.size());
		return response;
    }




}
