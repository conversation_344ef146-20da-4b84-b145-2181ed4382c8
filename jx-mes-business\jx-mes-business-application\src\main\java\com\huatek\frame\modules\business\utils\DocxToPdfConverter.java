package com.huatek.frame.modules.business.utils;
import com.documents4j.api.DocumentType;
import com.documents4j.api.IConverter;
import com.documents4j.job.LocalConverter;
import com.huatek.frame.common.exception.ServiceException;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import java.io.*;

import java.io.*;

import static com.alibaba.com.caucho.hessian.io.HessianInputFactory.log;

public class DocxToPdfConverter {

    public static byte[] createAndConvertToPdfBytesStreaming(XWPFDocument document) {
        try {
            // 将DOCX转换为输入流
            try (ByteArrayOutputStream docxOutputStream = new ByteArrayOutputStream()) {
                document.write(docxOutputStream);

                try (ByteArrayInputStream docxInputStream =
                             new ByteArrayInputStream(docxOutputStream.toByteArray())) {

                    // 直接转换为PDF字节数组
                    return toPdfConverter(docxInputStream);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("Failed to create PDF bytes", e);
        }
    }

    public static byte[] toPdfConverter(InputStream docxInputStream) {
        try (ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream()) {
            IConverter converter = LocalConverter.builder().build();
            converter.convert(docxInputStream)
                    .as(DocumentType.DOCX)
                    .to(pdfOutputStream)
                    .as(DocumentType.PDF)
                    .execute();

            return pdfOutputStream.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("PDF conversion failed", e);
        }
    }
    /**
     * 将XWPFDocument转换为byte[]
     */
    public static byte[] convertXWPFDocumentToBytes(XWPFDocument document) {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            document.write(outputStream);
            return outputStream.toByteArray();
        } catch (IOException e) {
            throw new ServiceException("文档转换失败");
        }
    }
}
