package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.FileReview;
import com.huatek.frame.modules.business.domain.vo.FileReviewVO;
import com.huatek.frame.modules.business.service.dto.FileReviewDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 文件评审mapper
* <AUTHOR>
* @date 2025-08-20
**/
public interface FileReviewMapper extends BaseMapper<FileReview> {

     /**
	 * 文件评审分页
	 * @param dto
	 * @return
	 */
	Page<FileReviewVO> selectFileReviewPage(FileReviewDTO dto);

    /**
	 * 外键关联表: awaiting_production_order - work_order_number
     **/
    @ApiModelProperty("外键 awaiting_production_order - work_order_number")
	Page<SelectOptionsVO> selectOptionsByWorkOrder(String productionWorkOrder);

    /**
     * 根据条件查询文件评审列表
     *
     * @param dto 文件评审信息
     * @return 文件评审集合信息
     */
    List<FileReviewVO> selectFileReviewList(FileReviewDTO dto);

	/**
	 * 根据IDS查询文件评审列表
	 * @param ids
	 * @return
	 */
    List<FileReviewVO> selectFileReviewListByIds(@Param("ids") List<String> ids);

	/**
	 * 查询是否有同分类同规范已评审通过的记录
	 * @param productCategory
	 * @param standardSpecificationId
	 * @return
	 */
    List<FileReview> selectByCategoryAndstandardSpecification(@Param("productCategory")String productCategory, @Param("standardSpecificationId")String standardSpecificationId);
}