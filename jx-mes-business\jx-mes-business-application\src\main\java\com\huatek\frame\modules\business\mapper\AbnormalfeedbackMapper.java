package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.Abnormalfeedback;
import  com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO;
import com.huatek.frame.modules.business.service.dto.AbnormalRequestDTO;
import com.huatek.frame.modules.business.service.dto.AbnormalfeedbackDTO;

import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 异常反馈mapper
* <AUTHOR>
* @date 2025-07-22
**/
public interface AbnormalfeedbackMapper extends BaseMapper<Abnormalfeedback> {

     /**
	 * 异常反馈分页
	 * @param dto
	 * @return
	 */
	Page<AbnormalfeedbackVO> selectAbnormalfeedbackPage(AbnormalfeedbackDTO dto);

    /**
	 * 外键关联表: customer_information_management - customer_id0
     **/
    @ApiModelProperty("外键 customer_information_management - customer_id0")
	Page<SelectOptionsVO> selectOptionsBySettlementUnit(String settlementUnit);
    /**
	 * 外键关联表: sys_user - name
     **/
    @ApiModelProperty("外键 sys_user - name")
	Page<SelectOptionsVO> selectOptionsByToNotify(String toNotify);

    /**
     * 根据条件查询异常反馈列表
     *
     * @param dto 异常反馈信息
     * @return 异常反馈集合信息
     */
    List<AbnormalfeedbackVO> selectAbnormalfeedbackList(AbnormalfeedbackDTO dto);

	/**
	 * 根据IDS查询异常反馈列表
	 * @param ids
	 * @return
	 */
    List<AbnormalfeedbackVO> selectAbnormalfeedbackListByIds(@Param("ids") List<String> ids);

	/**
	 * 根据工单编码和工序id查询异常反馈
	 * @param requestParam
	 * @return
	 */
    List<AbnormalfeedbackVO> getAbnormalsOfWorkOrderNumberORProcessId(AbnormalRequestDTO requestParam);

	/**
	 * 根据异常来源id查询异常信息
	 * @param sourceId
	 * @return
	 */
    List<AbnormalfeedbackVO> getAbnormalDetailsBySourceId(@Param("sourceId") String sourceId);
}