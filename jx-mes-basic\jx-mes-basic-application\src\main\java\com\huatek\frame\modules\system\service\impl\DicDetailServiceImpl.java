package com.huatek.frame.modules.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.http.HttpStatus;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.utils.DictCacheUtils;
import com.huatek.frame.modules.system.domain.Dic;
import com.huatek.frame.modules.system.mapper.DicMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.system.domain.DicDetail;
import com.huatek.frame.modules.system.domain.vo.DicDetailVO;
import com.huatek.frame.modules.system.mapper.DicDetailMapper;
import com.huatek.frame.modules.system.service.DicDetailService;
import com.huatek.frame.modules.system.service.dto.DicDetailDTO;

/**
 * 系统_字典 明细ServiceImpl
 *
 * <AUTHOR>
 * @date 2018-7-11 14:03:46
 */
@DubboService
//@CacheConfig(cacheNames = "dic")
public class DicDetailServiceImpl implements DicDetailService {

	@Autowired
	private DicDetailMapper dicDetailMapper;
	@Autowired
	private DicMapper dicMapper;

	@Override
//	@Cacheable(keyGenerator = "keyGenerator")
	public TorchResponse<List<DicDetailVO>> findDicDetailPage(DicDetailDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		dto.setDeleted(Constant.DEFAULT_NO);
		Page<DicDetailVO> vos = dicDetailMapper.selectDicDetailPage(dto);
		TorchResponse<List<DicDetailVO>> response = new TorchResponse<List<DicDetailVO>>();
		List<DicDetailVO> vo = vos.getResult();
		response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setCount(vos.getTotal());
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
//	@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(DicDetail entity) {
		if (!HuatekTools.isEmpty(entity.getId())) {
			List<DicDetail> lists = dicDetailMapper
					.selectList(new QueryWrapper<DicDetail>().eq("deleted", Constant.DEFAULT_NO)
							.ne("id", entity.getId()).eq("dname", entity.getDname()).eq("pid", entity.getPid()));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("名称重复");
			}
			lists = dicDetailMapper.selectList(new QueryWrapper<DicDetail>().eq("deleted", Constant.DEFAULT_NO)
					.ne("id", entity.getId()).eq("dcode", entity.getDcode()).eq("pid", entity.getPid()));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("编码重复");
			}
			dicDetailMapper.updateById(entity);
		} else {
			List<DicDetail> lists = dicDetailMapper.selectList(new QueryWrapper<DicDetail>()
					.eq("deleted", Constant.DEFAULT_NO).eq("dname", entity.getDname()).eq("pid", entity.getPid()));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("名称重复");
			}
			lists = dicDetailMapper.selectList(new QueryWrapper<DicDetail>().eq("deleted", Constant.DEFAULT_NO)
					.eq("dcode", entity.getDcode()).eq("pid", entity.getPid()));
			if (!ObjectUtils.isEmpty(lists)) {
				throw new ServiceException("编码重复");
			}
			entity.setDeleted(Constant.DEFAULT_NO);
			dicDetailMapper.insert(entity);
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
//	@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		for (String id : ids) {
			DicDetail detail = new DicDetail();
			detail.setDeleted(Constant.DEFAULT_YES);
			dicDetailMapper.update(detail, new QueryWrapper<DicDetail>().eq("id", id));
		}
		TorchResponse response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	public TorchResponse findDicDetail(String dicCode) {
		List<Map<String,String>> dicDetailVOS = dicDetailMapper.selectDicDetailByDicCode(dicCode);
		TorchResponse response = new TorchResponse<>();
		response.getData().setData(dicDetailVOS);
		response.setStatus(HttpStatus.HTTP_OK);
		return response;
	}

	@Override
	public List<Map<String, String>>  findDicDetailByDicCode(String dicCode) {
		return dicDetailMapper.selectDicDetailByDicCode(dicCode);
	}

	@Override
	public Map<String, String> selectDicDetailByDicCodeAndDetailName(String dicCode, String dName) {
		return dicDetailMapper.selectDicDetailByDicCodeAndDetailName(dicCode, dName);
	}

	@Override
	public Map<String, String> selectDicDetailByDicCodeAndDetailCode(String dicCode, String dCode) {
		return dicDetailMapper.selectDicDetailByDicCodeAndDetailCode(dicCode, dCode);
	}

	/**
	 * 字典数据更新到redis
	 */
	@Override
	public void updateDictCacheData() {
		List<Dic> dicList = dicMapper.selectList(new QueryWrapper<Dic>().eq("deleted", Constant.DEFAULT_NO));
		for (Dic dic : dicList) {
			List<Map<String, String>> maps = dicDetailMapper.selectDicDetailByDicCode(dic.getCode());
			List<JSONObject> dictData = maps.stream().map(map -> {
						JSONObject jsonObject = new JSONObject();
						jsonObject.putAll(map);
						return jsonObject;
					})
					.collect(Collectors.toList());
			DictCacheUtils.setDictCache(dic.getCode(), dictData);
		}
	}

}
