package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 根据工单编号返回生产任务相关信息
 */
@Data
public class ProductionTaskInfoVO {
    /**
     * 客户工序名称
     **/
    @ApiModelProperty("客户工序名称")
    private String customerProcessName;

    /**
     * 显示序号
     **/
    @ApiModelProperty("显示序号")
    private Integer displayNumber;

    /**
     * 试验依据
     **/
    @ApiModelProperty("试验依据")
    private String testBasis;

    /**
     * 工序名称
     */
    @ApiModelProperty("工序名称")
    private String outsourcingProcess;
}
