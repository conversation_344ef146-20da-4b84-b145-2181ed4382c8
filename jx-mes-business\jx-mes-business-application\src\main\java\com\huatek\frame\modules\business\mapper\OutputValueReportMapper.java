package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;
import com.huatek.frame.modules.business.domain.OutputValueReport;
import  com.huatek.frame.modules.business.domain.vo.OutputValueReportVO;
import com.huatek.frame.modules.business.service.dto.OutputValueReportDTO;
import com.huatek.frame.modules.business.domain.vo.OutputValueDashboardVO;
import com.huatek.frame.modules.business.service.dto.ProductionValueCalculationDTO;
import org.apache.ibatis.annotations.Param;

/**
* 产值报表mapper
* <AUTHOR>
* @date 2025-08-28
**/
public interface OutputValueReportMapper extends BaseMapper<OutputValueReport> {

     /**
	 * 产值报表分页
	 * @param dto
	 * @return
	 */
	Page<OutputValueReportVO> selectOutputValueReportPage(OutputValueReportDTO dto);


    /**
     * 根据条件查询产值报表列表
     *
     * @param dto 产值报表信息
     * @return 产值报表集合信息
     */
    List<OutputValueReportVO> selectOutputValueReportList(OutputValueReportDTO dto);

	/**
	 * 根据IDS查询产值报表列表
	 * @param ids
	 * @return
	 */
    List<OutputValueReportVO> selectOutputValueReportListByIds(@Param("ids") List<String> ids);

	/**
	 * 获取首页看板统计数据
	 * @return 统计数据
	 */
	OutputValueDashboardVO selectDashboardStats();

	List<ProductionValueCalculationDTO> selectProductionValueCalculations(ProductionValueCalculationDTO queryDto);
}