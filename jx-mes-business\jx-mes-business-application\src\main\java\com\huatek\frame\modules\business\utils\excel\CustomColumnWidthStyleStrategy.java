package com.huatek.frame.modules.business.utils.excel;

import java.util.List;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;

import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;

public class CustomColumnWidthStyleStrategy extends AbstractColumnWidthStyleStrategy {

	@Override
	protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<CellData> cellDataList, Cell cell, Head head,
			Integer relativeRowIndex, Boolean isHead) {
		 if (isHead) {
		        // 设置表头的列宽
		        writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 20 * 256);
		    } else {
		        // 设置内容的列宽
		        writeSheetHolder.getSheet().setColumnWidth(cell.getColumnIndex(), 20 * 256);
		    }
		 // 无论是否有数据，都为单元格设置边框
	        if (cell != null) {
	            CellStyle cellStyle = cell.getCellStyle();
	            if (cellStyle == null) {
	                Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
	                cellStyle = workbook.createCellStyle();
	                cell.setCellStyle(cellStyle);
	            }
	            // 这里设置边框样式
	            cellStyle.setBorderLeft(BorderStyle.THIN);
	            cellStyle.setBorderRight(BorderStyle.THIN);
	            cellStyle.setBorderTop(BorderStyle.THIN);
	            cellStyle.setBorderBottom(BorderStyle.THIN);
	        }
		
	}
}
