package com.huatek.frame.modules.business.service.impl;

import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.vo.ProductionTaskVO;
import com.huatek.frame.modules.business.service.dto.ProductionTaskDTO;
import com.huatek.frame.modules.common.response.TorchResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;

/**
 * 生产任务待办页面过滤测试
 * 测试相同工单编号只显示执行顺序最小的未完成任务的功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class ProductionTaskTodoFilterTest {

    @Resource
    private ProductionTaskServiceImpl productionTaskService;

    private ProductionTaskDTO todoDto;
    private ProductionTaskDTO allDto;

    @BeforeEach
    void setUp() {
        // 创建待办查询条件
        todoDto = new ProductionTaskDTO();
        todoDto.setToDoOrAll(BusinessConstant.PRODUCTIONTASK_TODO); // "0"
        todoDto.setPage(1);
        todoDto.setLimit(100);

        // 创建全部查询条件
        allDto = new ProductionTaskDTO();
        allDto.setToDoOrAll(BusinessConstant.PRODUCTIONTASK_ALL); // "1"
        allDto.setPage(1);
        allDto.setLimit(100);
    }

    @Test
    void testTodoPageShowsMinExecutionSequenceOnly() {
        // 测试待办页面只显示执行顺序最小的未完成任务
        
        System.out.println("=== 测试待办页面过滤功能 ===");
        
        // 查询待办任务
        TorchResponse<List<ProductionTaskVO>> todoResponse = productionTaskService.findProductionTaskPage(todoDto);
        List<ProductionTaskVO> todoTasks = todoResponse.getData().getData();
        
        System.out.println("待办任务数量: " + todoTasks.size());
        
        // 查询全部任务
        TorchResponse<List<ProductionTaskVO>> allResponse = productionTaskService.findProductionTaskPage(allDto);
        List<ProductionTaskVO> allTasks = allResponse.getData().getData();
        
        System.out.println("全部任务数量: " + allTasks.size());
        
        // 验证待办页面的过滤逻辑
        validateTodoFiltering(todoTasks, allTasks);
    }

    @Test
    void testSpecificWorkOrderFiltering() {
        // 测试特定工单的过滤逻辑
        
        // 设置特定工单编号进行测试
        String testWorkOrderNumber = "WO202501001"; // 替换为实际存在的工单编号
        
        todoDto.setWorkOrderNumber(testWorkOrderNumber);
        allDto.setWorkOrderNumber(testWorkOrderNumber);
        
        System.out.println("=== 测试工单 " + testWorkOrderNumber + " 的过滤功能 ===");
        
        // 查询待办任务
        TorchResponse<List<ProductionTaskVO>> todoResponse = productionTaskService.findProductionTaskPage(todoDto);
        List<ProductionTaskVO> todoTasks = todoResponse.getData().getData();
        
        // 查询全部任务
        TorchResponse<List<ProductionTaskVO>> allResponse = productionTaskService.findProductionTaskPage(allDto);
        List<ProductionTaskVO> allTasks = allResponse.getData().getData();
        
        System.out.println("工单 " + testWorkOrderNumber + " 待办任务数量: " + todoTasks.size());
        System.out.println("工单 " + testWorkOrderNumber + " 全部任务数量: " + allTasks.size());
        
        // 打印任务详情
        printTaskDetails(todoTasks, "待办任务");
        printTaskDetails(allTasks, "全部任务");
    }

    /**
     * 验证待办页面的过滤逻辑
     */
    private void validateTodoFiltering(List<ProductionTaskVO> todoTasks, List<ProductionTaskVO> allTasks) {
        // 按工单编号分组统计
        System.out.println("\n=== 验证过滤逻辑 ===");
        
        // 统计每个工单在待办和全部页面的任务数量
        todoTasks.stream()
                .collect(java.util.stream.Collectors.groupingBy(
                        ProductionTaskVO::getWorkOrderNumber,
                        java.util.stream.Collectors.counting()))
                .forEach((workOrder, count) -> {
                    System.out.println("工单 " + workOrder + " 在待办页面有 " + count + " 个任务");
                    
                    // 查找该工单在全部页面的任务
                    List<ProductionTaskVO> allTasksForWorkOrder = allTasks.stream()
                            .filter(task -> workOrder.equals(task.getWorkOrderNumber()))
                            .collect(java.util.stream.Collectors.toList());
                    
                    System.out.println("工单 " + workOrder + " 在全部页面有 " + allTasksForWorkOrder.size() + " 个任务");
                    
                    if (allTasksForWorkOrder.size() > 1) {
                        // 验证待办页面只显示执行顺序最小的任务
                        Integer minExecutionSequence = allTasksForWorkOrder.stream()
                                .filter(task -> isUnfinishedStatus(task.getStatus()))
                                .mapToInt(ProductionTaskVO::getExecutionSequence)
                                .min()
                                .orElse(-1);
                        
                        if (minExecutionSequence != -1) {
                            System.out.println("工单 " + workOrder + " 未完成任务的最小执行顺序: " + minExecutionSequence);
                            
                            // 验证待办页面的任务是否为最小执行顺序
                            todoTasks.stream()
                                    .filter(task -> workOrder.equals(task.getWorkOrderNumber()))
                                    .forEach(task -> {
                                        if (!task.getExecutionSequence().equals(minExecutionSequence)) {
                                            System.err.println("错误：工单 " + workOrder + " 的待办任务执行顺序 " 
                                                    + task.getExecutionSequence() + " 不是最小值 " + minExecutionSequence);
                                        } else {
                                            System.out.println("✓ 工单 " + workOrder + " 的待办任务执行顺序正确: " + task.getExecutionSequence());
                                        }
                                    });
                        }
                    }
                    System.out.println();
                });
    }

    /**
     * 判断是否为未完成状态
     */
    private boolean isUnfinishedStatus(String status) {
        return "0".equals(status) || // 未开始
               "1".equals(status) || // 进行中
               "2".equals(status) || // 待审批
               "3".equals(status) || // 驳回
               "4".equals(status);   // 暂停
    }

    /**
     * 打印任务详情
     */
    private void printTaskDetails(List<ProductionTaskVO> tasks, String taskType) {
        System.out.println("\n=== " + taskType + " 详情 ===");
        tasks.forEach(task -> {
            System.out.println("任务编号: " + task.getTaskNumber() + 
                             ", 工单编号: " + task.getWorkOrderNumber() + 
                             ", 执行顺序: " + task.getExecutionSequence() + 
                             ", 状态: " + task.getStatus() + 
                             ", 工序名称: " + task.getProcessName2());
        });
    }

    @Test
    void testExecutionSequenceFiltering() {
        // 专门测试执行顺序过滤
        System.out.println("=== 测试执行顺序过滤 ===");
        
        // 查询待办任务
        TorchResponse<List<ProductionTaskVO>> response = productionTaskService.findProductionTaskPage(todoDto);
        List<ProductionTaskVO> tasks = response.getData().getData();
        
        // 按工单分组，验证每个工单只有一个任务且是最小执行顺序
        tasks.stream()
                .collect(java.util.stream.Collectors.groupingBy(ProductionTaskVO::getWorkOrderNumber))
                .forEach((workOrder, workOrderTasks) -> {
                    System.out.println("工单 " + workOrder + " 的待办任务:");
                    workOrderTasks.forEach(task -> {
                        System.out.println("  - 执行顺序: " + task.getExecutionSequence() + 
                                         ", 状态: " + getStatusName(task.getStatus()) + 
                                         ", 工序: " + task.getProcessName2());
                    });
                    
                    if (workOrderTasks.size() > 1) {
                        System.err.println("警告：工单 " + workOrder + " 在待办页面有多个任务，可能过滤逻辑有问题");
                    }
                });
    }

    /**
     * 获取状态名称
     */
    private String getStatusName(String statusCode) {
        switch (statusCode) {
            case "0": return "未开始";
            case "1": return "进行中";
            case "2": return "待审批";
            case "3": return "驳回";
            case "4": return "暂停";
            case "5": return "取消";
            case "6": return "完成";
            case "7": return "已外协";
            default: return "未知状态(" + statusCode + ")";
        }
    }
}
