package com.huatek.frame.modules.business.service.impl;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.MessageManagementService;
import com.huatek.frame.modules.business.service.ProdValCalcDetailsService;
import com.huatek.frame.modules.business.service.ProductionOrderResultService;
import com.huatek.frame.modules.business.service.ProductionValueCalculationService;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.service.DicDetailService;
import com.huatek.frame.modules.system.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.camunda.feel.syntaxtree.For;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;


/**
 * 产值计算 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productionValueCalculation")
//@RefreshScope
@Slf4j
public class ProductionValueCalculationServiceImpl implements ProductionValueCalculationService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private ProductionValueCalculationMapper productionValueCalculationMapper;

    @Autowired
    private AwaitingProductionOrderMapper awaitingProductionOrderMapper;

    @Autowired
    private ProductListMapper productListMapper;

    @Autowired
    private CustomerProcessSchemeMapper customerProcessSchemeMapper;

    @Autowired
    private CustomerExperimentProjectMapper customerExperimentProjectMapper;

    @Autowired
    private CustomerInformationManagementMapper customerInformationManagementMapper;

    @Autowired
    private AccountingStandardMapper accountingStandardMapper;

    @Autowired
    private AccountingStandardDetailsMapper accountingStandardDetailsMapper;

    @Autowired
    private CcCategorizationCodeMapper ccCategorizationCodeMapper;

    @Autowired
    private MessageManagementService messageManagementService;

    @Autowired
    private ProdValCalcDetailsMapper prodValCalcDetailsMapper;

    @Autowired
    private EvaluationOrderMapper evaluationOrderMapper;

    @DubboReference
    private SysUserService sysUserService;

    @Autowired
    private ProdValCalcDetailsService prodValCalcDetailsService;

    @DubboReference
    private DicDetailService dicDetailService;

    @Autowired
    protected Validator validator;

    @Autowired
    private SalesOutboundMapper salesOutboundMapper;

    @Autowired
    private ProductInventoryMapper productInventoryMapper;

    @Autowired
    private ProductionOrderResultService productionOrderResultService;

    @Autowired
    private ProductionTaskMapper productionTaskMapper;

    @Autowired
    private ProductionOrderProcessTestMapper productionOrderProcessTestMapper;

    @Autowired
    private ProductCategoryMapper productCategoryMapper;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    public ProductionValueCalculationServiceImpl() {

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<ProductionValueCalculationVO>> findProductionValueCalculationPage(ProductionValueCalculationDTO dto) {
        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<ProductionValueCalculationVO> productionValueCalculations = productionValueCalculationMapper.selectProductionValueCalculationPage(dto);
        TorchResponse<List<ProductionValueCalculationVO>> response = new TorchResponse<List<ProductionValueCalculationVO>>();
        response.getData().setData(productionValueCalculations);
        response.setStatus(200);
        response.getData().setCount(productionValueCalculations.getTotal());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(ProductionValueCalculationDTO productionValueCalculationDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productionValueCalculationDto.getCodexTorchDeleted())) {
            productionValueCalculationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productionValueCalculationDto.getId();
        ProductionValueCalculation entity = new ProductionValueCalculation();
        BeanUtils.copyProperties(productionValueCalculationDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        if (HuatekTools.isEmpty(id)) {
            productionValueCalculationMapper.insert(entity);
        } else {
            productionValueCalculationMapper.updateById(entity);
        }

        TorchResponse response = new TorchResponse();
        ProductionValueCalculationVO vo = new ProductionValueCalculationVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<ProductionValueCalculationVO> findProductionValueCalculation(String id) {
        ProductionValueCalculationVO vo = new ProductionValueCalculationVO();
        if (!HuatekTools.isEmpty(id)) {
            ProductionValueCalculation entity = productionValueCalculationMapper.selectById(id);
            if (HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);
        }
        TorchResponse<ProductionValueCalculationVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        List<ProductionValueCalculation> productionValueCalculationList = productionValueCalculationMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionValueCalculation productionValueCalculation : productionValueCalculationList) {
            productionValueCalculation.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productionValueCalculationMapper.updateById(productionValueCalculation);
        }
        //productionValueCalculationMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id) {
        if (selectOptionsFuncMap.size() == 0) {
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }


    @Override
    @ExcelExportConversion(tableName = "production_value_calculation", convertorFields = "status,type,testType")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionValueCalculationVO> selectProductionValueCalculationList(ProductionValueCalculationDTO dto) {
        return productionValueCalculationMapper.selectProductionValueCalculationList(dto);
    }

    /**
     * 对账结果数据批量导入
     *
     * @param productionValueCalculationList 对账结果数据列表
     * @param unionColumns                   作为确认数据唯一性的字段集合
     * @param isUpdateSupport                是否更新支持，如果已存在，则进行更新数据
     * @param operName                       操作用户
     * @return 结果
     */
    @Override
    public TorchResponse importProductionValueCalculation(List<ProductionValueCalculationDuiZhangVO> productionValueCalculationList,
                                                          List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productionValueCalculationList) || productionValueCalculationList.size() == 0) {
            throw new ServiceException("对账结果数据批量导入不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (ProductionValueCalculationDuiZhangVO vo : productionValueCalculationList) {
            try {
                // 1. 验证所有必填字段不能为空
                if (validateRequiredFields(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }

                // 2. 根据工单编号查询现有数据
                ProductionValueCalculation existingRecord = findExistingRecordByWorkOrder(vo.getWorkOrderNumber().trim());
                if (existingRecord == null) {
                    failureNum++;
                    failureMsg.append("<br/>").append(failureNum).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 不存在，无法更新");
                    continue;
                }


                // 4. 执行更新操作
                updateDuizhangs(vo);


                successNum++;
                successMsg.append("<br/>").append(successNum).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 更新成功");

            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工单编号 " + vo.getWorkOrderNumber() + " 导入失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private void updateDuizhangs(ProductionValueCalculationDuiZhangVO vo) {
        UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("work_order_number", vo.getWorkOrderNumber().trim());
        updateWrapper.set("status", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);
        updateWrapper.set("settlement_price", vo.getSettlementPrice());
        updateWrapper.set("bill_statement_number", vo.getBillStatementNumber().trim());
        updateWrapper.set("settlement_date", new Date());
        updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
        updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
        productionValueCalculationMapper.update(null, updateWrapper);
    }

    /**
     * 验证必填字段
     *
     * @param vo         对账VO对象
     * @param failureNum 失败数量
     * @param failureMsg 失败消息
     * @return true表示验证失败，false表示验证通过
     */
    private boolean validateRequiredFields(ProductionValueCalculationDuiZhangVO vo, int failureNum, StringBuilder failureMsg) {
        StringBuilder errorMsg = new StringBuilder();

        if (StringUtils.isEmpty(vo.getWorkOrderNumber())) {
            errorMsg.append("工单编号不能为空；");
        }
        if (vo.getSettlementPrice() == null) {
            errorMsg.append("对账价格不能为空；");
        }
        if (StringUtils.isEmpty(vo.getBillStatementNumber())) {
            errorMsg.append("对账单号不能为空；");
        }

        if (errorMsg.length() > 0) {
            failureMsg.append("<br/>").append(failureNum + 1).append("、工单编号 ").append(vo.getWorkOrderNumber()).append(" 验证失败：").append(errorMsg.toString());
            return true;
        }
        return false;
    }

    /**
     * 根据工单编号查询现有记录
     *
     * @param workOrderNumber 工单编号
     * @return 现有记录，如果不存在返回null
     */
    private ProductionValueCalculation findExistingRecordByWorkOrder(String workOrderNumber) {
        if (StringUtils.isEmpty(workOrderNumber)) {
            return null;
        }

        QueryWrapper<ProductionValueCalculation> wrapper = new QueryWrapper<>();
        wrapper.eq("work_order_number", workOrderNumber);
        wrapper.eq("type", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU);
        wrapper.eq("codex_torch_deleted", "0");

        List<ProductionValueCalculation> existingRecords = productionValueCalculationMapper.selectList(wrapper);
        if (!CollectionUtils.isEmpty(existingRecords)) {
            return existingRecords.get(0);
        }
        return null;
    }


    private Boolean linkedDataValidityVerification(ProductionValueCalculationVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (failureRecord > 0) {
            failureNum++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductionValueCalculationListByIds(List<String> ids) {
        List<ProductionValueCalculationVO> productionValueCalculationList = productionValueCalculationMapper.selectProductionValueCalculationListByIds(ids);

        TorchResponse<List<ProductionValueCalculationVO>> response = new TorchResponse<List<ProductionValueCalculationVO>>();
        response.getData().setData(productionValueCalculationList);
        response.setStatus(200);
        response.getData().setCount((long) productionValueCalculationList.size());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse duiZhang(ProductionValueCalculationDuiZhangDTO productionValueCalculationDuiZhangDTO) {

        List<String> ids = productionValueCalculationDuiZhangDTO.getIds();
        if (HuatekTools.isEmpty(ids)) {

            throw new ServiceException("数据不存在");

        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(productionValueCalculation)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                if (!productionValueCalculation.getType().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU)) {
                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 类型为君信标准，不可对账; ");
                    continue;
                }

                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("status", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES);
                updateWrapper.set("settlement_price", productionValueCalculationDuiZhangDTO.getSettlementPrice());
                updateWrapper.set("bill_statement_number", productionValueCalculationDuiZhangDTO.getBillStatementNumber());
                updateWrapper.set("settlement_date", new Date());
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = productionValueCalculation != null ? productionValueCalculation.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 对账失败:").append(e.getMessage()).append("; ");
                log.error("对账失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            // 单个操作
            if (successCount == 1) {
                //response.setMessage("对账成功");
            } else {
                response.setMessage("对账失败");
                response.setStatus(Constant.REQUEST_SUCCESS);
            }
        } else {
            // 批量操作
            if (failedWorkOrders.isEmpty()) {
                //response.setMessage("批量对账成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量对账完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse updateContract(ProductionValueCalculationContractDTO productionValueCalculationContractDTO) {
        // 获取ID列表，支持批量操作和单个操作
        List<String> ids = productionValueCalculationContractDTO.getIds();
        if (HuatekTools.isEmpty(ids)) {

            throw new ServiceException("数据不存在");

        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
//            try {
            ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
            if (HuatekTools.isEmpty(productionValueCalculation)) {
//                    failedWorkOrders.add("ID:" + id);
//                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
//                    continue;
                throw new ServiceException("数据不存在");
            }

            if (!productionValueCalculation.getType().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU)) {
//                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
//                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 类型为君信标准，不可录入合同; ");
                throw new ServiceException("工单编号:" + productionValueCalculation.getWorkOrderNumber() + " 类型为君信标准，不可录入合同");
                //continue;
            }

            if (!productionValueCalculation.getStatus().equals(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES)) {
//                    failedWorkOrders.add(productionValueCalculation.getWorkOrderNumber());
//                    failureDetails.append("工单编号:").append(productionValueCalculation.getWorkOrderNumber()).append(" 状态为未对账，不可录入合同; ");
//                    continue;
                throw new ServiceException("工单编号:" + productionValueCalculation.getWorkOrderNumber() + " 状态为未对账，不可录入合同");
            }

            UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);
            updateWrapper.set("contract_number", productionValueCalculationContractDTO.getContractNumber());
            updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
            updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
            productionValueCalculationMapper.update(null, updateWrapper);

            successCount++;
//            } catch (Exception e) {
//                ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(id);
//                String workOrderNumber = productionValueCalculation != null ? productionValueCalculation.getWorkOrderNumber() : "ID:" + id;
//                failedWorkOrders.add(workOrderNumber);
//                failureDetails.append("工单编号:").append(workOrderNumber).append(" 合同录入失败:").append(e.getMessage()).append("; ");
//                log.error("合同录入失败，ID: {}, 错误: {}", id, e.getMessage(), e);
//            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
//        if (ids.size() == 1) {
//            // 单个操作
//            if (successCount == 1) {
//                //response.setMessage("合同录入成功");
//            } else {
//                response.setMessage("合同录入失败");
//                response.setStatus(Constant.REQUEST_SUCCESS);
//            }
//        } else {
//            // 批量操作
//            if (failedWorkOrders.isEmpty()) {
//                //response.setMessage("批量合同录入成功，共处理 " + successCount + " 条记录");
//            } else {
//                response.setMessage("批量合同录入完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
//            }
//        }

        log.info(failureDetails.toString());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse generateCalculation() throws Exception {
        // 启动异步线程处理核算生成任务
        Runnable task = new Runnable() {
            @Override
            public void run() {
                // 查询需要处理的生产工单
                List<ProductionOrder> productionOrderList = awaitingProductionOrderMapper.selectList(new QueryWrapper<ProductionOrder>()
                        .eq("is_calculation", DicConstant.CommonDic.DEFAULT_ZERO)
                        .eq("production_stage", DicConstant.ProductionOrder.PRODUCTION_STAGE_OUTSTORE)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
                );

                if (CollectionUtils.isEmpty(productionOrderList)) {
                    log.info("没有需要生成核算的工单");
                    return;
                }

                int successCount = 0;
                int failureCount = 0;
                StringBuilder failureDetails = new StringBuilder();

                for (ProductionOrder productionOrder : productionOrderList) {
                    try {
                        // 获取工单相关信息
                        ProductList productList = productListMapper.selectById(productionOrder.getProduct());
                        if (productList == null) {
                            failureCount++;
                            failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 产品信息不存在; ");
                            continue;
                        }

                        // 获取客户工序方案信息
                        QueryWrapper<CustomerProcessScheme> schemeWrapper = new QueryWrapper<>();
                        schemeWrapper.eq("work_order", productionOrder.getId());
                        CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(schemeWrapper);
                        if (scheme == null) {
                            failureCount++;
                            failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 客户工序方案不存在; ");
                            continue;
                        }

                        // 获取试验类型
                        String testType = productList.getTestType();

                        // 获取结算单位相关信息
                        EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
                        if (evaluationOrder == null) {
                            failureCount++;
                            failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 测评订单不存在; ");
                            continue;
                        }

                        CustomerInformationManagement customerInformationManagement = customerInformationManagementMapper.selectById(evaluationOrder.getCustomerId());
                        if (customerInformationManagement == null) {
                            failureCount++;
                            failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 客户信息不存在; ");
                            continue;
                        }

                        // 获取工序信息
                        CustomerExperimentProjectDTO customerExperimentProjectDTO = new CustomerExperimentProjectDTO();
                        customerExperimentProjectDTO.setCodexTorchMasterFormId(scheme.getId());
                        List<CustomerExperimentProjectVO> customerExperimentProjects = customerExperimentProjectMapper.selectCustomerExperimentProjectList(customerExperimentProjectDTO);

                        if (CollectionUtils.isEmpty(customerExperimentProjects)) {
                            failureCount++;
                            failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 客户试验项目不存在; ");
                            continue;
                        }

                        // 生成两条核算记录：君信标准和客户标准
                        generateCalculationRecords(productionOrder, productList, scheme, testType, customerInformationManagement,
                                customerExperimentProjects, evaluationOrder);

                        // 更新工单的is_calculation状态
                        UpdateWrapper<ProductionOrder> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq("id", productionOrder.getId());
                        updateWrapper.set("is_calculation", DicConstant.CommonDic.DEFAULT_ONE);
                        updateWrapper.set("calculation_time", new Timestamp(System.currentTimeMillis()));
                        updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                        updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                        awaitingProductionOrderMapper.update(null, updateWrapper);

                        successCount++;
                    } catch (Exception e) {
                        failureCount++;
                        failureDetails.append("工单编号:").append(productionOrder.getWorkOrderNumber()).append(" 生成核算失败:").append(e.getMessage()).append("; ");
                        log.error("生成核算失败，工单ID: {}, 错误: {}", productionOrder.getId(), e.getMessage(), e);
                    }
                }

                // 记录处理结果日志
                if (failureCount == 0) {
                    log.info("核算信息生成成功，共处理 " + successCount + " 个工单");
                } else {
                    log.info("核算信息生成完成，成功 " + successCount + " 个工单，失败 " + failureCount + " 个工单。失败详情: " + failureDetails.toString());
                }
            }
        };

        // 启动线程执行任务
        Thread thread = new Thread(task);
        thread.start();

        // 立即返回响应，不等待异步任务完成
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("核算生成任务已启动，正在后台处理");
        return response;
    }

    /**
     * 根据内部分类获取对应的客户器件分类
     *
     * @param internalClassification 内部分类（产品分类）
     * @param testType               试验类型
     * @return 客户器件分类
     */
    private String getCustomerDeviceClassificationByInternalClassification(String internalClassification, String testType, String settlementUnit,
                                                                           ProductionValueCalculation kehuRecord) {
        try {
            // 首先根据试验类型查询核算标准，获取收费标准编号
            QueryWrapper<AccountingStandard> standardWrapper = new QueryWrapper<>();
            standardWrapper.eq("test_type", testType);
            standardWrapper.eq("charge_standard_type", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU);
            standardWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
            standardWrapper.eq("settlement_unit", settlementUnit);
            standardWrapper.orderByDesc("codex_torch_create_datetime");
            List<AccountingStandard> standards = accountingStandardMapper.selectList(standardWrapper);

            List<AccountingStandard> accountingStandards = accountingStandardMapper.selectList(standardWrapper);
            if (CollectionUtils.isEmpty(accountingStandards)) {
                log.warn("未找到客户标准的核算标准，试验类型: {}", testType);
                return "";
            }

            String chargeStandardNumber = accountingStandards.get(0).getChargeStandardNumber();
            kehuRecord.setChargingStandardName(accountingStandards.get(0).getChargingStandardName());
            // 根据收费标准编号和内部分类查询客户器件分类
            QueryWrapper<CcCategorizationCode> ccWrapper = new QueryWrapper<>();
            ccWrapper.eq("charge_standard_number", chargeStandardNumber);
            ccWrapper.eq("internal_classification", internalClassification);
            ccWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);

            List<CcCategorizationCode> ccCodes = ccCategorizationCodeMapper.selectList(ccWrapper);
            if (!CollectionUtils.isEmpty(ccCodes)) {
                return ccCodes.get(0).getCustomerDeviceClassification();
            }

            log.warn("未找到内部分类对应的客户器件分类，内部分类: {}, 收费标准编号: {}", internalClassification, chargeStandardNumber);
            return "";

        } catch (Exception e) {
            log.error("获取客户器件分类失败，内部分类: {}, 试验类型: {}", internalClassification, testType, e);
            return "";
        }
    }


    /**
     * 生成核算记录（每工单2条主表：君信标准/客户标准，每主表下每工序一条明细）
     */
    private void generateCalculationRecords(ProductionOrder productionOrder, ProductList productList,
                                            CustomerProcessScheme scheme, String testType, CustomerInformationManagement customerInformationManagement,
                                            List<CustomerExperimentProjectVO> experimentProjects, EvaluationOrder evaluationOrder) {
        // 1. 获取当前客户标准的核算方式
        QueryWrapper<AccountingStandard> kehuStandardWrapper = new QueryWrapper<>();
        kehuStandardWrapper.eq("test_type", testType);
        kehuStandardWrapper.eq("charge_standard_type", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU);
        kehuStandardWrapper.eq("settlement_unit", customerInformationManagement.getId());
        kehuStandardWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
        kehuStandardWrapper.orderByDesc("codex_torch_create_datetime");
        List<AccountingStandard> kehuStandards = accountingStandardMapper.selectList(kehuStandardWrapper);
        AccountingStandard kehuStandard = kehuStandards.isEmpty() ? null : kehuStandards.get(0);

        // 2. 创建君信标准记录（始终按分类核算）
        ProductionValueCalculation junxinRecord = createBaseCalculationRecord(productionOrder, productList, evaluationOrder, customerInformationManagement, scheme, testType);
        junxinRecord.setType(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN);
        productionValueCalculationMapper.insert(junxinRecord);
        String junxinId = junxinRecord.getId();
        BigDecimal junxinTotalPrice = BigDecimal.ZERO;

        // 3. 创建客户标准记录
        ProductionValueCalculation kehuRecord = createBaseCalculationRecord(productionOrder, productList, evaluationOrder, customerInformationManagement, scheme, testType);
        kehuRecord.setType(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU);
        if (kehuStandard != null) {
            kehuRecord.setDiscount(kehuStandard.getDiscount());
        }
        //通过君信分类查询客户分类
        String customerCategory = getCustomerDeviceClassificationByInternalClassification(productList.getProductCategory(), testType, customerInformationManagement.getId(), kehuRecord);
        kehuRecord.setCustomerPriceClassification(customerCategory);

        productionValueCalculationMapper.insert(kehuRecord);
        String kehuId = kehuRecord.getId();

        // 4. 生成明细记录
        for (CustomerExperimentProjectVO experimentProject : experimentProjects) {
            String processId = experimentProject.getProcessId();
            Integer quantity = productionOrder.getQuantity();

            // 4.1 生成君信标准明细（按分类核算）
            ProdValCalcDetails junxinDetail = new ProdValCalcDetails();
            junxinDetail.setProductionValueCalculationId(junxinId);
            junxinDetail.setExperimentProject(processId);
            junxinDetail.setCustomerExperimentProjectId(experimentProject.getId());
            junxinDetail.setQuantity(quantity);

            ProductionValueCalcuPriceDTO productionValueCalcuPriceDTO=getUnitPrice(junxinRecord, testType, experimentProject, productList.getProductCategory(),
                    true, customerInformationManagement.getId(), quantity);
            BigDecimal junxinUnitPrice=productionValueCalcuPriceDTO.getPrice();
            junxinDetail.setUnitPrice(junxinUnitPrice);
            //批
            BigDecimal junxinDetailTotal=junxinUnitPrice;
            if(productionValueCalcuPriceDTO.getType().equals(DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU)){
                //个
                junxinDetailTotal = junxinUnitPrice != null ? junxinUnitPrice.multiply(BigDecimal.valueOf(quantity)) : BigDecimal.ZERO;
            }
            junxinDetail.setTotal(junxinDetailTotal);
            prodValCalcDetailsMapper.insert(junxinDetail);

            // 累加君信标准总价
            junxinTotalPrice = junxinTotalPrice.add(junxinDetailTotal);

            // 4.2 处理客户标准
            if (kehuStandard == null || DicConstant.SalesOrder.ACCOUNTING_STANDARD_ACCOUNTINGMETHOD_ZHEKOU.equals(kehuStandard.getAccountingMethod())) {
//                // 按产值核算折扣：不生成明细，直接使用君信标准价格乘以折扣
//                BigDecimal kehuTotalPrice = junxinTotalPrice;
//                if (kehuStandard.getDiscount() != null) {
//                    //折扣 除以100
//                    BigDecimal zhekou = kehuStandard.getDiscount().divide(new BigDecimal(100));
//                    kehuTotalPrice = kehuTotalPrice.multiply(zhekou);
//                }
//                kehuRecord.setInternalAccountingPrice(junxinTotalPrice);
//                kehuRecord.setCustomerAccountingPrice(kehuTotalPrice);
//                productionValueCalculationMapper.updateById(kehuRecord);
                continue;
            }


            // 按分类核算：生成客户标准明细
            ProdValCalcDetails kehuDetail = new ProdValCalcDetails();
            kehuDetail.setProductionValueCalculationId(kehuId);
            kehuDetail.setExperimentProject(processId);
            kehuDetail.setCustomerExperimentProjectId(experimentProject.getId());
            //String customerCategory = getCustomerDeviceClassificationByInternalClassification(productList.getProductCategory(), testType, customerInformationManagement.getSettlementUnit());
            kehuDetail.setQuantity(quantity);

            ProductionValueCalcuPriceDTO kehuProductionValueCalcuPriceDTO=getUnitPrice(kehuRecord, testType, experimentProject, customerCategory,
                    false, customerInformationManagement.getId(), quantity);
            BigDecimal kehuUnitPrice=kehuProductionValueCalcuPriceDTO.getPrice();
            kehuDetail.setUnitPrice(kehuUnitPrice);
            //批
            BigDecimal kehuDetailTotal=kehuUnitPrice;
            if(productionValueCalcuPriceDTO.getType().equals(DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU)){
                //个
                kehuDetailTotal = kehuUnitPrice != null ? kehuUnitPrice.multiply(BigDecimal.valueOf(quantity)) : BigDecimal.ZERO;
            }
            // 如果是按分类核算，在明细层面应用折扣
            if (kehuStandard != null && kehuStandard.getDiscount() != null && DicConstant.SalesOrder.ACCOUNTING_STANDARD_ACCOUNTINGMETHOD_FENLEI.equals(kehuStandard.getAccountingMethod())) {

                BigDecimal zhekou = kehuStandard.getDiscount().divide(new BigDecimal(100));
                kehuDetailTotal = kehuDetailTotal.multiply(zhekou);
            }
            kehuDetail.setTotal(kehuDetailTotal);
            prodValCalcDetailsMapper.insert(kehuDetail);
        }

        // 5. 更新君信标准记录的总价
        junxinRecord.setInternalAccountingPrice(junxinTotalPrice);
        productionValueCalculationMapper.updateById(junxinRecord);

        // 6. 如果是按产值核算折扣，更新客户标准的总价
        if (kehuStandard != null && DicConstant.SalesOrder.ACCOUNTING_STANDARD_ACCOUNTINGMETHOD_ZHEKOU.equals(kehuStandard.getAccountingMethod())) {
            BigDecimal kehuTotalPrice = junxinTotalPrice;
            if (kehuStandard.getDiscount() != null) {
                BigDecimal zhekou = kehuStandard.getDiscount().divide(new BigDecimal(100));
                kehuTotalPrice = kehuTotalPrice.multiply(zhekou);
            }
            kehuRecord.setInternalAccountingPrice(junxinTotalPrice);
            kehuRecord.setCustomerAccountingPrice(kehuTotalPrice);
            productionValueCalculationMapper.updateById(kehuRecord);
        }
    }

    /**
     *               //个数
     *     //                if ((DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_DANWEI_CI.equals(detail.getUnit()) &&
     *     //                        detail.getQuantity().intValueExact() == experimentProject.getTestingTimes())
     *     //                        || (DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_DANWEI_H.equals(detail.getUnit()) &&
     *     //                        detail.getQuantity() == experimentProject.getDurationOfTesting())) {
     *     //
     *     //                    if (quantity >= detail.getMinimumQuantityRequirement()) {
     *     //                        return detail.getBasePrice();
     *     //                    } else {
     *     //                        return detail.getBasePrice().multiply(detail.getCfbrq());
     *     //                    }
     *     //                }
     */
    /**
     * 获取单价（核算标准明细）
     */
    private ProductionValueCalcuPriceDTO getUnitPrice(ProductionValueCalculation productionValueCalculation, String testType, CustomerExperimentProjectVO experimentProject,
                                    String category, boolean isInternal, String settlementUnit, Integer quantity) {

        ProductionValueCalcuPriceDTO productionValueCalcuPriceDTO=new ProductionValueCalcuPriceDTO();
        productionValueCalcuPriceDTO.setPrice(new BigDecimal(0));
        productionValueCalcuPriceDTO.setType(DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_PI);

        String processId = experimentProject.getProcessId();
        String chargeStandardType = isInternal ? DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN : DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU;
        QueryWrapper<AccountingStandard> standardWrapper = new QueryWrapper<>();
        standardWrapper.eq("test_type", testType);
        standardWrapper.eq("charge_standard_type", chargeStandardType);
        standardWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
        if (!isInternal) {
            standardWrapper.eq("settlement_unit", settlementUnit);
        }
        standardWrapper.orderByDesc("codex_torch_create_datetime");
        List<AccountingStandard> standards = accountingStandardMapper.selectList(standardWrapper);
        if (CollectionUtils.isEmpty(standards)) {
            return  productionValueCalcuPriceDTO;
        }

        //收费标准
        productionValueCalculation.setChargingStandardName(standards.get(0).getChargingStandardName());

        //String chargeStandardNumber = standards.get(0).getChargeStandardNumber();
        QueryWrapper<AccountingStandardDetails> detailsWrapper = new QueryWrapper<>();
        detailsWrapper.eq("codex_torch_master_form_id", standards.get(0).getId());
        detailsWrapper.eq("experiment_project", processId);
        detailsWrapper.eq("product_category", category);
        detailsWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
        AccountingStandardDetails detail = accountingStandardDetailsMapper.selectOne(detailsWrapper);
        productionValueCalcuPriceDTO.setType(detail.getChargingMethod());
        if (null != detail) {
            if (DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU.equals(detail.getChargingMethod())) {

                if (quantity >= detail.getMinimumQuantityRequirement()) {
                    productionValueCalcuPriceDTO.setPrice(detail.getBasePrice());
                    return  productionValueCalcuPriceDTO;
                } else {
                    productionValueCalcuPriceDTO.setPrice(detail.getBasePrice().multiply(detail.getCfbrq()));
                    return  productionValueCalcuPriceDTO;
                }
            } else if (DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_PI.equals(detail.getChargingMethod())) {
                productionValueCalcuPriceDTO.setPrice(detail.getBasePrice());
                return  productionValueCalcuPriceDTO;
            }
        }
        sendMessageToMarketSettlementRoleByTask(productionValueCalculation, experimentProject);


        return  productionValueCalcuPriceDTO;
    }

    /**
     * 创建基础核算记录
     */
    private ProductionValueCalculation createBaseCalculationRecord(ProductionOrder productionOrder, ProductList productList, EvaluationOrder evaluationOrder,
                                                                   CustomerInformationManagement customerInformationManagement, CustomerProcessScheme scheme, String testType) {
        ProductionValueCalculation record = new ProductionValueCalculation();

        // 基础信息
        record.setWorkOrderNumber(productionOrder.getWorkOrderNumber());
        record.setTicketType(productionOrder.getTestsMethodology());
        record.setOrderNumber(productionOrder.getOrderNumber());
        record.setManufacturer(productList.getManufacturer());

        record.setProductName(productList.getProductName());
        record.setProductModel(productList.getProductModel());
        record.setBatchNumber(productList.getProductionBatch());
        record.setProductCategory(productList.getProductCategory());
        record.setInternalPriceClassification(productList.getProductCategory());
        record.setProductInformationName(productList.getProductName());
        record.setEntrustedUnit(evaluationOrder.getEntrustedUnit());

        record.setProductQuantity(productionOrder.getQuantity());
        record.setTestType(testType);
        record.setStatus(DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_NO); // 未对账

        record.setSettlementUnit(customerInformationManagement.getId());
        record.setDateOfEntrustment(evaluationOrder.getDateOfEntrustment());
        record.setOrderInspectionNumber(evaluationOrder.getOrderInspectionNumber());
        record.setWorkOrderInspectionNumber1(productList.getWorkOrderInspectionNumber());
        record.setCompletionTime(productionOrder.getCompletionTime());


        SalesOutbound salesOutbound = salesOutboundMapper.selectOne(
                new QueryWrapper<SalesOutbound>()
                        .eq("work_order_number", productionOrder.getWorkOrderNumber())
        );
        if (salesOutbound != null) {
            record.setShippingDate(java.sql.Date.valueOf(salesOutbound.getShippingDate()));
            record.setRecipientCustomer(salesOutbound.getRecipientCustomer());
        }

        ProductionValueCalculationTestVo productionValueCalculationTestVo = getProductionValueCalculationTestVo(productionOrder);

        record.setNumberOfQualifiedProductions(productionValueCalculationTestVo.getNumberOfQualifiedProductions());
        record.setNonQualityProcess(productionValueCalculationTestVo.getNonQualityProcess());
        record.setNumNonQualProd(productionValueCalculationTestVo.getNumNonQualProd());

        return record;
    }

    private ProductionValueCalculationTestVo getProductionValueCalculationTestVo(ProductionOrder productionOrder) {

        //old
        // 直接从数据库查询该工单的最后一个工序的合格数量
//        Long qualifiedQuantity = productionTaskMapper.getLastProcessQualifiedQuantity(productionOrder.getWorkOrderNumber());
//        record.setNumberOfQualifiedProductions(qualifiedQuantity.intValue());

        // 直接从数据库查询该工单的所有不合格工序信息  setNonQualityProcess
//        List<UnqualifiedProcessVO> unqualifiedProcesses = productionTaskMapper.getWorkOrderQualityInfo(productionOrder.getWorkOrderNumber());
//        if (!CollectionUtils.isEmpty(unqualifiedProcesses)) {
//            record.setNonQualityProcess(unqualifiedProcesses.stream().map(UnqualifiedProcessVO::getProcessName).collect(Collectors.joining(",")));
//        }


        ProductionValueCalculationTestVo productionValueCalculationTestVo = new ProductionValueCalculationTestVo();
        //new
        List<ProductionOrderProcessTest> productionOrderProcessTestList = productionOrderProcessTestMapper.selectList(
                new QueryWrapper<ProductionOrderProcessTest>()
                        .eq("work_order", productionOrder.getWorkOrderNumber())
                        .eq("codex_torch_deleted", DicConstant.CommonDic.DIC_NO)
        );
        List<ProductionTaskVO> productionTaskVOList = new ArrayList<>();
        if (!productionOrderProcessTestList.isEmpty()) {
            productionOrderProcessTestList.stream().forEach(productionOrderProcessTest -> {
                productionTaskVOList.add(JSON.parseObject(productionOrderProcessTest.getProcessData(), ProductionTaskVO.class));
            });
        }
        //productionTaskVOList 按照执行顺序降序
        productionTaskVOList.sort(Comparator.comparingInt(ProductionTaskVO::getExecutionSequence).reversed());
        //最后一个工序的合格数量
        productionValueCalculationTestVo.setNumberOfQualifiedProductions(productionTaskVOList.get(0).getQualifiedQuantity());


        //拿到productionTaskVOList中的不合格工序
        productionValueCalculationTestVo.setNonQualityProcess(productionTaskVOList.stream().filter(productionTaskVO ->
                        null != productionTaskVO.getUnqualifiedQuantity() && productionTaskVO.getUnqualifiedQuantity() > 0)
                .map(ProductionTaskVO::getProcessName2)
                .distinct()
                .collect(Collectors.joining(",")));


        //production_order_result拿到不合格数量
        ProductionOrderResultVO orderResult = productionOrderResultService.selectResultByProductionOrder(productionOrder.getWorkOrderNumber());
        if (null != orderResult) {
            productionValueCalculationTestVo.setNumNonQualProd(orderResult.getUnqualifiedQuantity());
        }
        return productionValueCalculationTestVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse recalculateCustomerPrice(List<String> ids) {
        if (HuatekTools.isEmpty(ids)) {
            throw new ServiceException("数据不存在");
        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                // 获取核算记录
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(record)) {
                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                // 验证类型必须为客户标准
                if (!DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU.equals(record.getType())) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 类型不是客户标准，无法重新核算客户价格; ");
                    continue;
                }

                // 获取工单相关信息
                ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(
                        new QueryWrapper<ProductionOrder>()
                                .eq("work_order_number", record.getWorkOrderNumber())
                                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
                );

                if (productionOrder == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 工单信息不存在; ");
                    continue;
                }

                // 获取产品信息（需要获取测评订单ID）
                ProductList productList = productListMapper.selectById(productionOrder.getProduct());
                if (productList == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 产品信息不存在; ");
                    continue;
                }

                // 获取客户工序方案
                CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(
                        new QueryWrapper<CustomerProcessScheme>()
                                .eq("work_order", productionOrder.getId())
                );

                if (scheme == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 客户工序方案不存在; ");
                    continue;
                }

                // 获取结算单位
                EvaluationOrder evaluationOrder = evaluationOrderMapper.selectById(productList.getEvaluationOrderId());
                if (evaluationOrder == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 测评订单不存在; ");
                    continue;
                }

                CustomerInformationManagement customerInfo = customerInformationManagementMapper.selectById(evaluationOrder.getCustomerId());
                if (customerInfo == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 客户信息不存在; ");
                    continue;
                }

                // 获取工序信息
                CustomerExperimentProjectDTO projectDTO = new CustomerExperimentProjectDTO();
                projectDTO.setCodexTorchMasterFormId(scheme.getId());
                List<CustomerExperimentProjectVO> experimentProjects = customerExperimentProjectMapper.selectCustomerExperimentProjectList(projectDTO);

                if (CollectionUtils.isEmpty(experimentProjects)) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 客户试验项目不存在; ");
                    continue;
                }

                // 获取当前客户标准的核算方式
                QueryWrapper<AccountingStandard> standardWrapper = new QueryWrapper<>();
                standardWrapper.eq("test_type", record.getTestType());
                standardWrapper.eq("charge_standard_type", DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU);
                standardWrapper.eq("settlement_unit", customerInfo.getId());
                standardWrapper.eq("codex_torch_deleted", Constant.DEFAULT_NO);
                standardWrapper.orderByDesc("codex_torch_create_datetime");
                List<AccountingStandard> standards = accountingStandardMapper.selectList(standardWrapper);
                AccountingStandard currentStandard = standards.isEmpty() ? null : standards.get(0);

                // 首先计算君信内部按分类的价格
                BigDecimal internalTotalPrice = BigDecimal.ZERO;
                for (CustomerExperimentProjectVO project : experimentProjects) {
                    Integer quantity = record.getProductQuantity();

                    ProductionValueCalcuPriceDTO productionValueCalcuPriceDTO=getUnitPrice(record, record.getTestType(), project, record.getProductCategory(), true, null, quantity);
                    // 计算君信内部单个工序的价格
                    BigDecimal internalUnitPrice =productionValueCalcuPriceDTO.getPrice();

                    if(productionValueCalcuPriceDTO.getType().equals(DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU)){
                        internalTotalPrice = internalTotalPrice.add(internalUnitPrice.multiply(BigDecimal.valueOf(quantity)));
                    }else{
                        internalTotalPrice = internalTotalPrice.add(internalUnitPrice);
                    }
                }

                BigDecimal totalPrice = BigDecimal.ZERO;
                if (currentStandard != null) {
                    if (DicConstant.SalesOrder.ACCOUNTING_STANDARD_ACCOUNTINGMETHOD_ZHEKOU.equals(currentStandard.getAccountingMethod())) {
                        // 按产值核算折扣：使用新计算的内部核算价格乘以折扣
                        totalPrice = internalTotalPrice;
                        if (currentStandard.getDiscount() != null) {

                            BigDecimal zhekou = currentStandard.getDiscount().divide(new BigDecimal(100));
                            totalPrice = totalPrice.multiply(zhekou);
                        }
                    } else {
                        // 按分类核算：计算每个工序的价格并应用折扣
                        String customerCategory = getCustomerDeviceClassificationByInternalClassification(record.getProductCategory(), record.getTestType(),
                                customerInfo.getId(), record);

                        for (CustomerExperimentProjectVO project : experimentProjects) {
                            Integer quantity = record.getProductQuantity();

                            ProductionValueCalcuPriceDTO productionValueCalcuPriceDTO=getUnitPrice(record, record.getTestType(), project, customerCategory, false, customerInfo.getId(), quantity);
                            BigDecimal unitPrice = productionValueCalcuPriceDTO.getPrice();

                            if (unitPrice != null) {
                                BigDecimal processTotal = unitPrice;
                                if(productionValueCalcuPriceDTO.getType().equals(DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU)){
                                    processTotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
                                }
                                if (currentStandard.getDiscount() != null) {
                                    BigDecimal zhekou = currentStandard.getDiscount().divide(new BigDecimal(100));
                                    processTotal = processTotal.multiply(currentStandard.getDiscount());
                                }
                                totalPrice = totalPrice.add(processTotal);
                                //更新明细表
                                UpdateWrapper<ProdValCalcDetails> updateWrapper = new UpdateWrapper<>();
                                updateWrapper.eq("production_value_calculation_id", id);
                                updateWrapper.eq("experiment_project", project.getProcessId());
                                updateWrapper.set("unit_price", unitPrice);
                                updateWrapper.set("total", processTotal);
                                prodValCalcDetailsMapper.update(null, updateWrapper);
                            }
                        }
                    }
                } else {
                    // 如果没有找到客户标准，使用新计算的内部核算价格
                    totalPrice = internalTotalPrice;
                }

                // 更新客户核算价格、内部核算价格和折扣信息
                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("internal_accounting_price", internalTotalPrice); // 更新内部核算价格
                updateWrapper.set("customer_accounting_price", totalPrice);
                if (currentStandard != null) {
                    updateWrapper.set("discount", currentStandard.getDiscount());
                }
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = record != null ? record.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 客户价格重新核算失败:").append(e.getMessage()).append("; ");
                log.error("客户价格重新核算失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            if (successCount == 1) {
                //response.setMessage("客户价格重新核算成功");
            } else {
                response.setMessage("客户价格重新核算失败");
            }
        } else {
            if (failedWorkOrders.isEmpty()) {
                //response.setMessage("批量客户价格重新核算成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量客户价格重新核算完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse recalculateInternalPrice(List<String> ids) {
        if (HuatekTools.isEmpty(ids)) {
            throw new ServiceException("数据不存在");
        }

        int successCount = 0;
        List<String> failedWorkOrders = new ArrayList<>();
        StringBuilder failureDetails = new StringBuilder();

        for (String id : ids) {
            try {
                // 获取核算记录
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                if (HuatekTools.isEmpty(record)) {
//                    failedWorkOrders.add("ID:" + id);
                    failureDetails.append("ID:").append(id).append(" 数据不存在; ");
                    continue;
                }

                // 验证类型必须为君信标准
                if (!DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN.equals(record.getType())) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 类型不是君信标准，无法重新核算内部价格; ");
                    continue;
                }
                //已对账不能进行重新核算
                if (DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_YES.equals(record.getStatus())) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 已对账，无法重新核算内部价格; ");
                    continue;
                }
                // 获取工单相关信息
                ProductionOrder productionOrder = awaitingProductionOrderMapper.selectOne(
                        new QueryWrapper<ProductionOrder>()
                                .eq("work_order_number", record.getWorkOrderNumber())
                                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
                );

                if (productionOrder == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 工单信息不存在; ");
                    continue;
                }

                // 获取客户工序方案
                CustomerProcessScheme scheme = customerProcessSchemeMapper.selectOne(
                        new QueryWrapper<CustomerProcessScheme>()
                                .eq("work_order", productionOrder.getId())
                );

                if (scheme == null) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 客户工序方案不存在; ");
                    continue;
                }

                // 获取工序信息
                CustomerExperimentProjectDTO projectDTO = new CustomerExperimentProjectDTO();
                projectDTO.setCodexTorchMasterFormId(scheme.getId());
                List<CustomerExperimentProjectVO> experimentProjects = customerExperimentProjectMapper.selectCustomerExperimentProjectList(projectDTO);

                if (CollectionUtils.isEmpty(experimentProjects)) {
                    failedWorkOrders.add(record.getWorkOrderNumber());
                    failureDetails.append("工单编号:").append(record.getWorkOrderNumber()).append(" 客户试验项目不存在; ");
                    continue;
                }

                // 计算内部价格
                BigDecimal totalPrice = BigDecimal.ZERO;
                for (CustomerExperimentProjectVO project : experimentProjects) {
                    Integer quantity = record.getProductQuantity();

                    // 计算单个工序的价格
                    ProductionValueCalcuPriceDTO productionValueCalcuPriceDTO=getUnitPrice(record, record.getTestType(), project, record.getProductCategory(), true, null, quantity);
                    BigDecimal unitPrice = productionValueCalcuPriceDTO.getPrice();
                    if (unitPrice != null) {
                        BigDecimal tempPrice =unitPrice;
                        if(productionValueCalcuPriceDTO.getType().equals(DicConstant.SalesOrder.ACCOUNTING_STANDARD_DETAILS_CHARGINGMETHOD_GESHU)){
                            tempPrice=unitPrice.multiply(BigDecimal.valueOf(quantity));
                        }

                        //更新明细表
                        UpdateWrapper<ProdValCalcDetails> updateWrapper = new UpdateWrapper<>();
                        updateWrapper.eq("production_value_calculation_id", id);
                        updateWrapper.eq("experiment_project", project.getProcessId());
                        updateWrapper.set("unit_price", unitPrice);
                        updateWrapper.set("total", tempPrice);
                        prodValCalcDetailsMapper.update(null, updateWrapper);

                        totalPrice = totalPrice.add(tempPrice);
                    }
                }

                // 更新内部核算价格
                UpdateWrapper<ProductionValueCalculation> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("id", id);
                updateWrapper.set("internal_accounting_price", totalPrice);
                updateWrapper.set("codex_torch_updater", SecurityContextHolder.getCurrentUserId());
                updateWrapper.set("codex_torch_update_datetime", new Timestamp(System.currentTimeMillis()));
                productionValueCalculationMapper.update(null, updateWrapper);

                successCount++;
            } catch (Exception e) {
                ProductionValueCalculation record = productionValueCalculationMapper.selectById(id);
                String workOrderNumber = record != null ? record.getWorkOrderNumber() : "ID:" + id;
                failedWorkOrders.add(workOrderNumber);
                failureDetails.append("工单编号:").append(workOrderNumber).append(" 内部价格重新核算失败:").append(e.getMessage()).append("; ");
                log.error("内部价格重新核算失败，ID: {}, 错误: {}", id, e.getMessage(), e);
            }
        }

        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据操作结果返回不同的消息
        if (ids.size() == 1) {
            if (successCount == 1) {
                //response.setMessage("内部价格重新核算成功");
            } else {
                response.setMessage("内部价格重新核算失败");
            }
        } else {
            if (failedWorkOrders.isEmpty()) {
                //response.setMessage("批量内部价格重新核算成功，共处理 " + successCount + " 条记录");
            } else {
                response.setMessage("批量内部价格重新核算完成，成功 " + successCount + " 条，失败 " + failedWorkOrders.size() + " 条。失败工单编号: " + String.join(", ", failedWorkOrders));
            }
        }

        log.info(failureDetails.toString());

        // 如果有失败的工单，发送消息给市场结算角色
//        if (!failedWorkOrders.isEmpty()) {
//            sendMessageToMarketSettlementRole(failedWorkOrders);
//        }

        return response;
    }
//
//
//    /**
//     * 发送消息给市场结算角色
//     *
//     * @param workOrderNumbers 未计算出结果的工单编号列表
//     */
//    private void sendMessageToMarketSettlementRole(List<String> workOrderNumbers) {
//        try {
//            //查询市场结算角色的用户
//            List<String> userIds = sysUserService.selectUserIdsByRole(BusinessConstant.ROLE_SHICHANGJIESUAN);
//            if (CollectionUtils.isEmpty(userIds)) {
//                return;
//            }
//            // 发送消息
//            MessageManagementDTO messageDto = new MessageManagementDTO();
//            messageDto.setMessageContent("以下工单未能计算出内部价格，请人工处理：" + String.join(", ", workOrderNumbers));
//            messageDto.setSendTime(new Timestamp(System.currentTimeMillis()));
//            for (String userId : userIds) {
//                messageDto.setUserId(userId);
//                messageManagementService.saveOrUpdate(messageDto);
//            }
//            log.info("已发送内部价格核算失败提醒给市场结算角色，工单编号：{}", String.join(", ", workOrderNumbers));
//        } catch (Exception e) {
//            log.error("发送消息给市场结算角色失败", e);
//        }
//    }


    /**
     * 发送消息给市场结算角色
     */
    private void sendMessageToMarketSettlementRoleByTask(ProductionValueCalculation productionValueCalculation, CustomerExperimentProjectVO experimentProject) {
        try {
            //查询市场结算角色的用户
            List<String> userIds = sysUserService.selectUserIdsByRole(BusinessConstant.ROLE_SHICHANGJIESUAN);
            if (CollectionUtils.isEmpty(userIds)) {
                return;
            }
            List<MessageManagement> messageList = new ArrayList<>();
            // 获取发送者ID
            String senderId = SecurityContextHolder.getCurrentUserId();
            Timestamp sendTime = new Timestamp(System.currentTimeMillis());
            String msg = String.format("产值核算:%s工单的%s工序核算结果为0，请注意查看！" + productionValueCalculation.getWorkOrderNumber(), experimentProject.getCustomerProcessName());
            for (String userId : userIds) {
                MessageManagement message = new MessageManagement();
                message.setMessageContent(msg);
                message.setSendTime(sendTime);
                message.setUserId(userId);
                message.setSenderId(senderId);
                messageList.add(message);
            }
            // 批量保存消息
            if (!messageList.isEmpty()) {
                messageManagementService.batchSaveMessage(messageList, 200);
            }
            log.info(msg);
        } catch (Exception e) {
            log.error("发送消息给市场结算角色失败", e);
        }
    }

    @Override
    public TorchResponse selectProdValCalcDetailsListByProductionValueCalculationId(String productionValueCalculationId) {

        if (HuatekTools.isEmpty(productionValueCalculationId)) {
            throw new ServiceException("数据不存在");
        }
        //根据id查询
        ProductionValueCalculation productionValueCalculation = productionValueCalculationMapper.selectById(productionValueCalculationId);
        if (HuatekTools.isEmpty(productionValueCalculation)) {
            throw new ServiceException("数据不存在");
        }

        List<ProdValCalcDetailsVO> prodValCalcDetailsVOS = new ArrayList<>();
        //判断君信标准就查标准工序，如果客户标准就查客户工序名称
        if (DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN.equals(productionValueCalculation.getType())) {
            prodValCalcDetailsVOS = prodValCalcDetailsService.selectPVCDListByPVCIdJunXin(productionValueCalculationId);
        } else {
            prodValCalcDetailsVOS = prodValCalcDetailsService.selectPVCDListByPVCIdCustomer(productionValueCalculationId);
        }


        for (ProdValCalcDetailsVO prodValCalcDetailsVO : prodValCalcDetailsVOS) {

            //通过分类id查询分类
            ProductCategory productCategory = productCategoryMapper.selectById(productionValueCalculation.getProductCategory());
            if (productCategory != null) {
                prodValCalcDetailsVO.setProductCategory(productCategory.getCategoryName());
            } else {
                prodValCalcDetailsVO.setProductCategory("");
            }


            prodValCalcDetailsVO.setBatchNumber(productionValueCalculation.getBatchNumber());
            prodValCalcDetailsVO.setProductName(productionValueCalculation.getProductName());
            prodValCalcDetailsVO.setProductModel(productionValueCalculation.getProductModel());
        }

        TorchResponse<List<ProdValCalcDetailsVO>> response = new TorchResponse<List<ProdValCalcDetailsVO>>();
        response.getData().setData(prodValCalcDetailsVOS);
        response.setStatus(200);
        response.getData().setCount((long) prodValCalcDetailsVOS.size());
        return response;
    }

    @Override
    public String findContractNumber(String workOrderNumber) {
        if (HuatekTools.isEmpty(workOrderNumber)) {
            return null;
        }
        List<ProductionValueCalculation> calculations = productionValueCalculationMapper.selectList(new QueryWrapper<ProductionValueCalculation>()
                .eq("work_order_number", workOrderNumber)
                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        if (CollectionUtils.isEmpty(calculations)) {
            return null;
        }

        return calculations.get(0).getContractNumber();
    }

    @Override
    public TorchResponse<List<ProductionValueCalculationDetailExportVO>> selectDetailExportData(List<String> ids) {
        if (HuatekTools.isEmpty(ids)) {
            throw new ServiceException("请选择要导出的记录");
        }

        // 验证所选记录是否为同一类型标准
        //委托单位要关联customer_information_management
        List<ProductionValueCalculationVO> list = productionValueCalculationMapper.selectProductionValueCalculationListByIds(ids);
        if (list.isEmpty()) {
            throw new ServiceException("未找到相关记录");
        }

        String firstType = list.get(0).getType();
        boolean isAllSameType = list.stream().allMatch(item -> firstType.equals(item.getType()));
        if (!isAllSameType) {
            throw new ServiceException("所选记录必须是同一种标准");
        }

        List<ProductionValueCalculationDetailExportVO> exportList = new ArrayList<>();
        boolean isCustomerStandard = DicConstant.SalesOrder.PRODUCTION_VALUE_CALCULATION_STATUS_KEHU.equals(firstType);

        List<Map<String, String>> testTypeList = new ArrayList<>();
        TorchResponse<List<Map<String, String>>> testTypes = dicDetailService.findDicDetail("test_data_dictionary_testType");
        if (testTypes != null && testTypes.getData() != null && testTypes.getData().getData() != null) {
            testTypeList = testTypes.getData().getData();
        }
        for (ProductionValueCalculationVO record : list) {
            List<ProdValCalcDetailsVO> details = prodValCalcDetailsService.selectPVCDListByPVCIdJunXin(record.getId());

            // 处理每条明细记录
            for (ProdValCalcDetailsVO detail : details) {
                ProductionValueCalculationDetailExportVO exportVO = new ProductionValueCalculationDetailExportVO();
                BeanUtils.copyProperties(record, exportVO);

                //查询字典来匹配
                if (testTypeList != null) {
                    for (Map<String, String> testTypeMap : testTypeList) {
                        if (testTypeMap.get("value").equals(record.getTestType())) {
                            exportVO.setTestType(testTypeMap.get("label"));
                            break;
                        }
                    }
                }


                // 根据标准类型设置价格和分类
                if (isCustomerStandard) {
                    exportVO.setCustomerAccountingPrice(record.getCustomerAccountingPrice());
                    exportVO.setCustomerPriceClassification(record.getCustomerPriceClassification());
                } else {
                    exportVO.setCustomerAccountingPrice(record.getInternalAccountingPrice());
                    exportVO.setCustomerPriceClassification(record.getInternalPriceClassification());
                }

                // 设置动态列数据
                Map<String, BigDecimal> dynamicColumns = new HashMap<>();
                dynamicColumns.put(detail.getExperimentProject(), detail.getTotal());
                exportVO.setDynamicColumns(dynamicColumns);

                exportList.add(exportVO);
            }
        }

        // 处理动态表头去重和合并列
        exportList = mergeDynamicColumns(exportList);

        TorchResponse<List<ProductionValueCalculationDetailExportVO>> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(exportList);
        return response;
    }

    /**
     * 合并动态列数据
     *
     * @param list 原始数据列表
     * @return 合并后的数据列表
     */
    private List<ProductionValueCalculationDetailExportVO> mergeDynamicColumns(List<ProductionValueCalculationDetailExportVO> list) {
        // 获取所有唯一的experimentProject
        Set<String> uniqueProjects = list.stream()
                .flatMap(vo -> vo.getDynamicColumns().keySet().stream())
                .collect(Collectors.toCollection(LinkedHashSet::new));

        // 按工单编号分组
        Map<String, List<ProductionValueCalculationDetailExportVO>> groups = list.stream()
                .collect(Collectors.groupingBy(ProductionValueCalculationDetailExportVO::getWorkOrderNumber));

        // 合并每个工单的动态列
        List<ProductionValueCalculationDetailExportVO> result = new ArrayList<>();
        for (List<ProductionValueCalculationDetailExportVO> group : groups.values()) {
            ProductionValueCalculationDetailExportVO mergedVO = group.get(0);
            Map<String, BigDecimal> mergedColumns = new HashMap<>();

            for (String project : uniqueProjects) {
                mergedColumns.put(project, group.stream()
                        .map(vo -> vo.getDynamicColumns().getOrDefault(project, BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
            }

            mergedVO.setDynamicColumns(mergedColumns);
            result.add(mergedVO);
        }

        return result;
    }


}
