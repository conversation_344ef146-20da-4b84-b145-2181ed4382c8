package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import com.alibaba.fastjson.JSONObject;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.domain.AccountingStandard;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardDetailsVO;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardVO;
import com.huatek.frame.modules.business.mapper.AccountingStandardMapper;
import com.huatek.frame.modules.business.service.AccountingStandardDetailsService;
import com.huatek.frame.modules.business.service.AccountingStandardService;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDTO;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDetailsDTO;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysProcessRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import java.sql.Date;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;


/**
 * 核算标准 ServiceImpl
 * <AUTHOR>
 * @date 2025-08-22
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "accountingStandard")
//@RefreshScope
@Slf4j
public class AccountingStandardServiceImpl implements AccountingStandardService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private AccountingStandardMapper accountingStandardMapper;


    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private AccountingStandardDetailsService accountingStandardDetailsService;
    @Autowired
    private CodeManagementService codeManagementService;

	public AccountingStandardServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<AccountingStandardVO>> findAccountingStandardPage(AccountingStandardDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<AccountingStandardVO> accountingStandards = accountingStandardMapper.selectAccountingStandardPage(dto);
		TorchResponse<List<AccountingStandardVO>> response = new TorchResponse<List<AccountingStandardVO>>();
		response.getData().setData(accountingStandards);
		response.setStatus(200);
		response.getData().setCount(accountingStandards.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(AccountingStandardDTO accountingStandardDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(accountingStandardDto.getCodexTorchDeleted())) {
            accountingStandardDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = accountingStandardDto.getId();
		AccountingStandard entity = new AccountingStandard();
        BeanUtils.copyProperties(accountingStandardDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        AccountingStandard oldentity = accountingStandardMapper.selectById(id);
		if (ObjectUtils.isEmpty(oldentity)) {
			accountingStandardMapper.insert(entity);
		} else {
			accountingStandardMapper.updateById(entity);
		}

		TorchResponse response = new TorchResponse();
        AccountingStandardVO vo = new AccountingStandardVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<AccountingStandardVO> findAccountingStandard(String id) {
		AccountingStandardVO vo = new AccountingStandardVO();
		if (!HuatekTools.isEmpty(id)) {
			AccountingStandard entity = accountingStandardMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<AccountingStandardVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
	    List<AccountingStandard> accountingStandardList = accountingStandardMapper.selectBatchIds(Arrays.asList(ids));
        for (AccountingStandard accountingStandard : accountingStandardList) {
            accountingStandard.setCodexTorchDeleted(Constant.DEFAULT_YES);
            accountingStandardMapper.updateById(accountingStandard);
        }
		//accountingStandardMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
            //初始化外键函数
            selectOptionsFuncMap.put("settlementUnit",accountingStandardMapper::selectOptionsBySettlementUnit);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "accounting_standard", convertorFields = "chargeStandardType,testType,accountingMethod")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<AccountingStandardVO> selectAccountingStandardList(AccountingStandardDTO dto) {
        return accountingStandardMapper.selectAccountingStandardList(dto);
    }

    /**
     * 导入核算标准数据
     *
     * @param accountingStandardList 核算标准数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "accounting_standard", convertorFields = "chargeStandardType,testType,accountingMethod")
    public TorchResponse importAccountingStandard(List<AccountingStandardVO> accountingStandardList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(accountingStandardList) || accountingStandardList.size() == 0) {
            throw new ServiceException("导入核算标准数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (AccountingStandardVO vo : accountingStandardList) {
            try {
                AccountingStandard accountingStandard = new AccountingStandard();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, accountingStandard);
                QueryWrapper<AccountingStandard> wrapper = new QueryWrapper();
                AccountingStandard oldAccountingStandard = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = AccountingStandardVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<AccountingStandard> oldAccountingStandardList = accountingStandardMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldAccountingStandardList) && oldAccountingStandardList.size() > 1) {
                        accountingStandardMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldAccountingStandardList) && oldAccountingStandardList.size() == 1) {
                        oldAccountingStandard = oldAccountingStandardList.get(0);
                    }
                }
                if (StringUtils.isNull(oldAccountingStandard)) {
                    BeanValidators.validateWithException(validator, vo);
                    accountingStandardMapper.insert(accountingStandard);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、收费标准编号 " + vo.getChargeStandardNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldAccountingStandard, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    accountingStandardMapper.updateById(oldAccountingStandard);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、收费标准编号 " + vo.getChargeStandardNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、收费标准编号 " + vo.getChargeStandardNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、收费标准编号 " + vo.getChargeStandardNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(AccountingStandardVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (HuatekTools.isEmpty(vo.getChargeStandardNumber())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>收费标准编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getChargingStandardName())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>收费标准名称不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getChargeStandardType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>收费标准类型不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getTestType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>试验类型不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getAccountingMethod())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>核算方式不能为空!");
        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectAccountingStandardListByIds(List<String> ids) {
        List<AccountingStandardVO> accountingStandardList = accountingStandardMapper.selectAccountingStandardListByIds(ids);

		TorchResponse<List<AccountingStandardVO>> response = new TorchResponse<List<AccountingStandardVO>>();
		response.getData().setData(accountingStandardList);
		response.setStatus(200);
		response.getData().setCount((long)accountingStandardList.size());
		return response;
    }

    /**
     * 核算标准主子表单组合提交
     *
	 * @param accountingStandardDto 核算标准DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(AccountingStandardDTO accountingStandardDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(accountingStandardDto.getCodexTorchDeleted())) {
            accountingStandardDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
        accountingStandardDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        TorchResponse<String> codeResponse =  codeManagementService.getOrderNumber("SFBZ");
        accountingStandardDto.setChargeStandardNumber(codeResponse.getData().getData());
        //TODO: TorchDetailItemIds TO BE DEPRECATED
        accountingStandardDto.setCodexTorchDetailItemIds("");
        //判断唯一性
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.eq("charge_standard_type", accountingStandardDto.getChargeStandardType());
        wrapper.eq("test_type", accountingStandardDto.getTestType());
        if(accountingStandardDto.getChargeStandardType().equals(DicConstant.AccountingStandard.ACCOUNTING_CUSTOMER)){
            wrapper.eq("settlement_unit", accountingStandardDto.getSettlementUnit());
        }
        List<AccountingStandard> exists = accountingStandardMapper.selectList(wrapper);
        if(!ObjectUtils.isEmpty(exists) || exists.size()>0){
            throw new ServiceException("该收费标准类型、试验类型、结算单位的数据已存在");
        }
        TorchResponse<AccountingStandardVO> masterSubmitResp = this.saveOrUpdate(accountingStandardDto);
        AccountingStandardVO masterVo = masterSubmitResp.getData().getData();

        List<AccountingStandardDetailsDTO> accountingStandardDetailsDTOs = new ArrayList<>();
        if (accountingStandardDto.getDetailFormItems() != null && accountingStandardDto.getDetailFormItems().length > 0) {
            accountingStandardDetailsDTOs = Arrays.asList(accountingStandardDto.getDetailFormItems());
        } else if (StringUtils.isNotEmpty(accountingStandardDto.getCodexTorchDetailItemIds())) {
        }
//        else {
//            throw new ServiceException("表单提交异常，表单明细项为空");
//        }

//        for(AccountingStandardDetailsDTO accountingStandardDetailsDto : accountingStandardDetailsDTOs){
//            accountingStandardDetailsDto.setId("");
//
//            //非必要字段处理
//            accountingStandardDetailsDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
//
//            //主子表关联ID
//            accountingStandardDetailsDto.setCodexTorchMasterFormId(masterVo.getId());
//            // 业务字段管理
//            accountingStandardDetailsDto.setChargeStandardNumber(masterVo.getChargeStandardNumber());
//
//            //提交
//            TorchResponse<AccountingStandardDetailsVO> detailSubmitResp = accountingStandardDetailsService.saveOrUpdate(accountingStandardDetailsDto);
//            AccountingStandardDetailsVO detailVo = detailSubmitResp.getData().getData();
//        }

		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }



}
