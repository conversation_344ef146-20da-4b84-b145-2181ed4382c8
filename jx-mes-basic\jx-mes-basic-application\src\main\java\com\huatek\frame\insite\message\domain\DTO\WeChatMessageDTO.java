package com.huatek.frame.insite.message.domain.DTO;

import lombok.Data;

@Data
public class WeChatMessageDTO {
    /**
     * 指定接收消息的成员，成员ID列表（多个接收者用‘|’分隔，最多支持1000个）。
     * 特殊情况：指定为"@all"，则向该企业应用的全部成员发送
     */
    private String touser;

    /**
     * 指定接收消息的部门，部门ID列表，多个接收者用‘|’分隔，最多支持100个。
     * 当touser为"@all"时忽略本参数
     */
    private String toparty;

    /**
     * 指定接收消息的标签，标签ID列表，多个接收者用‘|’分隔，最多支持100个。
     * 当touser为"@all"时忽略本参数
     */
    private String totag;

    /**
     * 消息类型,text为返回的文本
     */
    private String msgtype;

    /**
     * 企业应用的id，整型。企业内部开发，可在应用的设置页面查看；第三方服务商，可通过接口 获取企业授权信息 获取该参数值
     */
    private Integer agentid;

    /**
     * 	消息内容，最长不超过2048个字节，超过将截断（支持id转译）
     */
    private WeChatTextDTO text;

    /**
     * 	卡片消息的展现形式非常灵活，支持使用br标签或者空格来进行换行处理，也支持使用div标签来使用不同的字体颜色，目前内置了3种文字颜色：灰色(gray)、高亮(highlight)、默认黑色(normal)，将其作为div标签的class属性即可，具体用法请参考上面的示例。
     */
    private WeChatTextDTO textcard;

    /**
     * 表示是否是保密消息，0表示可对外分享，1表示不能分享且内容显示水印，默认为0
     */
    private Integer safe;

    /**
     * 表示是否开启id转译，0表示否，1表示是，默认0。
     */
    private Integer enable_id_trans;

    /**
     * 表示是否开启重复消息检查，0表示否，1表示是，默认0
     */
    private Integer enable_duplicate_check;

    /**
     * 表示是否重复消息检查的时间间隔，默认1800s，最大不超过4小时
     */
    private Integer duplicate_check_interval;

    //touser、toparty、totag不能同时为空，后面不再强调。
}
