package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.business.domain.StandardSpecification;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.util.List;

/**
* @description 文件评审VO实体类
* <AUTHOR>
* @date 2025-08-20
**/
@Data
@ApiModel("文件评审DTO实体类")
public class FileReviewVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 生产工单
     **/
    @ApiModelProperty("生产工单")
    @Excel(name = "生产工单",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrder;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;
    private String productCategoryName;

    /**
	 * 标准规范号
     **/
    @ApiModelProperty("标准规范号")
    @Excel(name = "标准规范号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String standardSpecificationNumber;
    /**
     * 标准规范
     **/
    @ApiModelProperty("标准规范")
    private List<StandardSpecification> standardSpecifications;

    /**
	 * 关联异常反馈编号
     **/
    @ApiModelProperty("关联异常反馈编号")
    @Excel(name = "关联异常反馈编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String assocExceptionFeedbackNum;

    /**
	 * 变更文件
     **/
    @ApiModelProperty("变更文件")
    @Excel(name = "变更文件",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String changeFile;

    /**
	 * 评审结果
     **/
    @ApiModelProperty("评审结果")
    @Excel(name = "评审结果",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reviewResult;

    /**
	 * 评审备注
     **/
    @ApiModelProperty("评审备注")
    @Excel(name = "评审备注",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reviewRemark;

    /**
	 * 评审人
     **/
    @ApiModelProperty("评审人")
    @Excel(name = "评审人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reviewer;

    /**
	 * 评审时间
     **/
    @ApiModelProperty("评审时间")
    @Excel(name = "评审时间",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reviewTime;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}