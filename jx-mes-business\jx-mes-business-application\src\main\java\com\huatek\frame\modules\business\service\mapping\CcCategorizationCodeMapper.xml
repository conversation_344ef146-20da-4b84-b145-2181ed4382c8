<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.CcCategorizationCodeMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.customer_device_classification as customerDeviceClassification,
		t.internal_classification as internalClassification,
		t.charge_standard_number as chargeStandardNumber,
        t.codex_torch_master_form_id as codexTorchMasterFormId,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectCcCategorizationCodePage" parameterType="com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO">
		select
		<include refid="Base_Column_List" />
			from cc_categorization_code t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="customerDeviceClassification != null and customerDeviceClassification != ''">
                    and t.customer_device_classification  like concat('%', #{customerDeviceClassification} ,'%')
                </if>
                <if test="internalClassification != null and internalClassification != ''">
                    and t.internal_classification  like concat('%', #{internalClassification} ,'%')
                </if>
                <if test="chargeStandardNumber != null and chargeStandardNumber != ''">
                    and t.charge_standard_number  like concat('%', #{chargeStandardNumber} ,'%')
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.codex_torch_master_form_id  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        order by t.CODEX_TORCH_CREATE_DATETIME desc
	</select>

    <select id="selectCcCategorizationCodeList" parameterType="com.huatek.frame.modules.business.service.dto.CcCategorizationCodeDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO">
		select
		<include refid="Base_Column_List" />
			from cc_categorization_code t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="customerDeviceClassification != null and customerDeviceClassification != ''">
                    and t.customer_device_classification  like concat('%', #{customerDeviceClassification} ,'%')
                </if>
                <if test="internalClassification != null and internalClassification != ''">
                    and t.internal_classification  like concat('%', #{internalClassification} ,'%')
                </if>
                <if test="chargeStandardNumber != null and chargeStandardNumber != ''">
                    and t.charge_standard_number  like concat('%', #{chargeStandardNumber} ,'%')
                </if>
                <if test="codexTorchMasterFormId != null and codexTorchMasterFormId != ''">
                    and t.codex_torch_master_form_id  like concat('%', #{codexTorchMasterFormId} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectCcCategorizationCodeListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.CcCategorizationCodeVO">
		select
		<include refid="Base_Column_List" />
			from cc_categorization_code t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>