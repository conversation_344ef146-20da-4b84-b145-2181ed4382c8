package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 其他联系人VO实体类
* <AUTHOR>
* @date 2025-07-14
**/
@Data
@ApiModel("其他联系人DTO实体类")
public class OtherContactsVO implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 客户编号
     **/
    @ApiModelProperty("客户编号")
    private String customerId0;
    
    /**
	 * 姓名
     **/
    @ApiModelProperty("姓名")
    @Excel(name = "*姓名",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String name;
    
    /**
	 * 电话
     **/
    @ApiModelProperty("电话")
    @Excel(name = "*电话",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String userMobile;
    
    /**
	 * 邮箱
     **/
    @ApiModelProperty("邮箱")
    @Excel(name = "邮箱",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String userEmail;
    
    /**
	 * 部门
     **/
    @ApiModelProperty("部门")
    @Excel(name = "部门",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String department0;
    
    /**
	 * 主表单ID
     **/
    @ApiModelProperty("主表单ID")
    private String codexTorchMasterFormId;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}