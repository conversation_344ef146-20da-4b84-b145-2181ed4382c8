<?xml version="1.0" encoding="UTF-8"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.huatek.torch</groupId>
		<artifactId>jx-mes-basic</artifactId>
		<version>1.1.0-aigc</version>
	</parent>
	<artifactId>jx-mes-basic-rpc</artifactId>
	<name>jx-mes-basic-rpc</name>
	<packaging>jar</packaging>

	<properties>
		<java.version>1.8</java.version>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.cloud</groupId>
			<artifactId>spring-cloud-starter-dubbo</artifactId>
		</dependency>
		<dependency>
			<groupId>com.huatek.torch</groupId>
			<artifactId>huatek-torch-common-core</artifactId>
			<version>2.1.10</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas-extra</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huatek.torch</groupId>
			<artifactId>huatek-torch-common-oss</artifactId>
			<version>2.1.3</version>
		</dependency>
		<dependency>
			<groupId>com.huatek.torch</groupId>
			<artifactId>huatek-torch-common-excel</artifactId>
			<version>2.1.3</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml-schemas-extra</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.pagehelper</groupId>
			<artifactId>pagehelper-spring-boot-starter</artifactId>
			<version>1.2.3</version>
			<exclusions>
				<exclusion>
					<groupId>org.mybatis</groupId>
					<artifactId>mybatis</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.mybatis</groupId>
					<artifactId>mybatis-spring</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.huatek.torch</groupId>
			<artifactId>huatek-torch-common-log</artifactId>
			<version>2.1.5</version>
		</dependency>

		<!--openapi文档服务knife4j组件 -->
		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-micro-spring-boot-starter</artifactId>
			<version>2.0.9</version>
		</dependency>

		<dependency>
			<groupId>com.github.xiaoymin</groupId>
			<artifactId>knife4j-spring-ui</artifactId>
			<version>2.0.9</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.2.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>

	</dependencies>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.alibaba.cloud</groupId>
				<artifactId>spring-cloud-alibaba-dependencies</artifactId>
				<version>2.2.6.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>
</project>