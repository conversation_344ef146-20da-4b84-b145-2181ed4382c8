package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

/**
 * 假期时间管理Controller
 */
@Api(tags = "假期时间管理")
@RestController
@RequestMapping("/api/holiday")
public class BasHolidayController {

    @Autowired
    HttpClientUtil httpClientUtil;
    /**
     * 获取所有信息
     */
    @Log("获取所有信息")
    @ApiOperation(value = "获取所有信息")
    @GetMapping(value = "/getallasync/{currentmonth}", produces = { "application/json;charset=utf-8" })
    public Object getallasync(@PathVariable(value = "currentmonth")String currentmonth, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/holiday/getallasync/" + currentmonth);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取分页信息
     */
    @Log("获取分页信息")
    @ApiOperation(value = "获取分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object page(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/holiday/page");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增
     */
    @Log("新增")
    @ApiOperation(value = "新增")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object add(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/holiday/add");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 更新
     */
    @Log("更新")
    @ApiOperation(value = "更新")
    @PostMapping(value = "/edit", produces = { "application/json;charset=utf-8" })
    public Object edit(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/holiday/edit");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 删除
     */
    @Log("删除")
    @ApiOperation(value = "删除")
    @DeleteMapping(value = "/delete/{id}", produces = { "application/json;charset=utf-8" })
    public Object edit(@PathVariable(value = "id") String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("DELETE");
        inputParamDto.setServiceUrl("aps/api/holiday/delete/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 根据id获取
     */
    @Log("根据id获取")
    @ApiOperation(value = "根据id获取")
    @PostMapping(value = "/getbyid/{id}", produces = { "application/json;charset=utf-8" })
    public Object getbyid(@PathVariable(value = "id")String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/holiday/getbyid/" + id);
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 导入
     */
    @Log("导入")
    @ApiOperation(value = "导入")
    @PostMapping(value = "/importholidays")
    public Object importholidays(@RequestParam("formFile") MultipartFile file,  HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/holiday/importholidays");

        return httpClientUtil.callMesWithFile(request, inputParamDto, file);
    }

    /**
     * 删除全部假期
     */
    @Log("删除全部假期")
    @ApiOperation(value = "删除全部假期")
    @DeleteMapping(value = "/deleteall", produces = { "application/json;charset=utf-8" })
    public Object edit(HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("DELETE");
        inputParamDto.setServiceUrl("aps/api/holiday/deleteall");
        return httpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取时间段内的非排产时间
     */
    @Log("获取时间段内的非排产时间")
    @ApiOperation(value = "获取时间段内的非排产时间")
    @PostMapping(value = "/getunplan", produces = { "application/json;charset=utf-8" })
    public Object getunplan(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/holiday/getunplan");
        return httpClientUtil.callMes(request, inputParamDto);
    }
}
