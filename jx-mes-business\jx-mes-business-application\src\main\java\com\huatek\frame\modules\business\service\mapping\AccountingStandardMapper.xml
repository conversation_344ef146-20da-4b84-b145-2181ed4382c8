<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.AccountingStandardMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.charge_standard_number as chargeStandardNumber,
		t.charging_standard_name as chargingStandardName,
		t.charge_standard_type as chargeStandardType,
		t.test_type as testType,
		t.settlement_unit as settlementUnit,
		t.accounting_method as accountingMethod,
		t.discount as discount,
		t.codex_torch_detail_item_ids as codexTorchDetailItemIds,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectAccountingStandardPage" parameterType="com.huatek.frame.modules.business.service.dto.AccountingStandardDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AccountingStandardVO">
		select
		<include refid="Base_Column_List" />,su.user_name as codexTorchCreatorName,cim.settlement_unit as settlementUnitName
			from accounting_standard t
            left join  sys_user su on t.codex_torch_creator_id = su.id
            left join customer_information_management cim on t.settlement_unit=cim.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="chargeStandardNumber != null and chargeStandardNumber != ''">
                    and t.charge_standard_number  like concat('%', #{chargeStandardNumber} ,'%')
                </if>
                <if test="chargingStandardName != null and chargingStandardName != ''">
                    and t.charging_standard_name  like concat('%', #{chargingStandardName} ,'%')
                </if>
                <if test="chargeStandardType != null and chargeStandardType != ''">
                    and t.charge_standard_type  = #{chargeStandardType}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                <if test="settlementUnit != null and settlementUnit != ''">
                    and t.settlement_unit  like concat('%', #{settlementUnit} ,'%')
                </if>
                <if test="accountingMethod != null and accountingMethod != ''">
                    and t.accounting_method  = #{accountingMethod}
                </if>
                <if test="discount != null and discount != ''">
                    and t.discount  = #{discount}
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.codex_torch_detail_item_ids  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        order by t.CODEX_TORCH_CREATE_DATETIME desc
	</select>

    <select id="selectAccountingStandardList" parameterType="com.huatek.frame.modules.business.service.dto.AccountingStandardDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AccountingStandardVO">
		select
		<include refid="Base_Column_List" />,su.user_name as codexTorchCreatorName,cim.settlement_unit as settlementUnitName
        from accounting_standard t
        left join  sys_user su on t.codex_torch_creator_id = su.id
        left join customer_information_management cim on t.settlement_unit=cim.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="chargeStandardNumber != null and chargeStandardNumber != ''">
                    and t.charge_standard_number  like concat('%', #{chargeStandardNumber} ,'%')
                </if>
                <if test="chargingStandardName != null and chargingStandardName != ''">
                    and t.charging_standard_name  like concat('%', #{chargingStandardName} ,'%')
                </if>
                <if test="chargeStandardType != null and chargeStandardType != ''">
                    and t.charge_standard_type  = #{chargeStandardType}
                </if>
                <if test="testType != null and testType != ''">
                    and t.test_type  = #{testType}
                </if>
                <if test="settlementUnit != null and settlementUnit != ''">
                    and t.settlement_unit  like concat('%', #{settlementUnit} ,'%')
                </if>
                <if test="accountingMethod != null and accountingMethod != ''">
                    and t.accounting_method  = #{accountingMethod}
                </if>
                <if test="discount != null and discount != ''">
                    and t.discount  = #{discount}
                </if>
                <if test="codexTorchDetailItemIds != null and codexTorchDetailItemIds != ''">
                    and t.codex_torch_detail_item_ids  like concat('%', #{codexTorchDetailItemIds} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectAccountingStandardListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.AccountingStandardVO">
		select
		<include refid="Base_Column_List" />,su.user_name as codexTorchCreatorName,cim.settlement_unit as settlementUnitName
        from accounting_standard t
        left join  sys_user su on t.codex_torch_creator_id = su.id
        left join customer_information_management cim on t.settlement_unit=cim.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>

	<select id="selectOptionsBySettlementUnit"
            resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select t.settlement_unit as label,t.id as value
        from customer_information_management t where t.settlement_unit !='' and t.CODEX_TORCH_DELETED ='0'
    </select>
</mapper>