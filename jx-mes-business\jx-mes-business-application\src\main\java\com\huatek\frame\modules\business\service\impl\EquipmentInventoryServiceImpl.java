package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.annotation.datascope.DataScope;
import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.DeviceInfoVO;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventorySampleVO;
import com.huatek.frame.modules.business.domain.vo.EquipmentInventoryVO;
import com.huatek.frame.modules.business.domain.vo.TraceInformationVO;
import com.huatek.frame.modules.business.mapper.*;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.DeviceTraceabilityService;
import com.huatek.frame.modules.business.service.EquipmentInventoryService;
import com.huatek.frame.modules.business.service.TraceInformationService;
import com.huatek.frame.modules.business.service.dto.DeviceTraceabilityDTO;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryDTO;
import com.huatek.frame.modules.business.service.dto.EquipmentInventoryUpdateStatusDTO;
import com.huatek.frame.modules.business.service.dto.TraceInformationDTO;
import com.huatek.frame.modules.business.utils.DateTimeUtil;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 设备台账 ServiceImpl
 * <AUTHOR>
 * @date 2025-07-18
 */
//@Service
@DubboService
//@CacheConfig(cacheNames = "equipmentInventory")
//@RefreshScope
@Slf4j
public class EquipmentInventoryServiceImpl implements EquipmentInventoryService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

	@Autowired
	private EquipmentInventoryMapper equipmentInventoryMapper;

	@Autowired
    private StandardProcessPlanMapper standardProcessPlanMapper;
	@Autowired
    private DeviceTypeMapper deviceTypeMapper;

    @Autowired
    private DeviceTraceabilityMapper deviceTraceabilityMapper;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private DeviceTraceabilityService deviceTraceabilityService;

    @Autowired
    private TraceInformationMapper traceInformationMapper;

    @Autowired
    protected Validator validator;

	private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();


    @Autowired
    private TraceInformationService traceInformationService;


	public EquipmentInventoryServiceImpl(){

	}

	@Override
	//@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
	public TorchResponse<List<EquipmentInventoryVO>> findEquipmentInventoryPage(EquipmentInventoryDTO dto) {
		PageHelper.startPage(dto.getPage(), dto.getLimit());
		Page<EquipmentInventoryVO> equipmentInventorys = equipmentInventoryMapper.selectEquipmentInventoryPage(dto);
		TorchResponse<List<EquipmentInventoryVO>> response = new TorchResponse<List<EquipmentInventoryVO>>();
		response.getData().setData(equipmentInventorys);
		response.setStatus(200);
		response.getData().setCount(equipmentInventorys.getTotal());
		return response;
	}


	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse saveOrUpdate(EquipmentInventoryDTO equipmentInventoryDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(equipmentInventoryDto.getCodexTorchDeleted())) {
            equipmentInventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //todo 如果是金蝶同步，从同步信息获取

        String id = equipmentInventoryDto.getId();
		EquipmentInventory entity = new EquipmentInventory();
        BeanUtils.copyProperties(equipmentInventoryDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

        try{
            if (HuatekTools.isEmpty(id)) {
                if (StrUtil.isEmpty(equipmentInventoryDto.getDeviceSerialNumber())){
                    TorchResponse response =  codeManagementService.getOrderNumber("JXYQ");
                    entity.setDeviceSerialNumber(response.getData().getData().toString());
                }

                equipmentInventoryMapper.insert(entity);
            } else {
                if (StrUtil.equals(equipmentInventoryDto.getStatus(), DicConstant.deviceManagement.EQUIPMENT_INVENTORY_STATUS_SUSPENDED)
                        || StrUtil.equals(equipmentInventoryDto.getStatus(), DicConstant.deviceManagement.EQUIPMENT_INVENTORY_STATUS_SCRAPPED)){
                    //修改设备溯源和溯源信息状态为已失效
                    LambdaUpdateWrapper<DeviceTraceability> traceabilityLambdaUpdateWrapper = Wrappers.lambdaUpdate(DeviceTraceability.class)
                            .eq(DeviceTraceability::getDeviceSerialNumber, equipmentInventoryDto.getDeviceSerialNumber())
                            .orderByDesc(DeviceTraceability::getCodexTorchCreateDatetime)
                            .last("LIMIT 1")
                            .set(DeviceTraceability::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_INVALID);
                    LambdaUpdateWrapper<TraceInformation> informationLambdaUpdateWrapper = Wrappers.lambdaUpdate(TraceInformation.class)
                            .eq(TraceInformation::getCodexTorchMasterFormId, equipmentInventoryDto.getId())
                            .set(TraceInformation::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_INVALID);
                    deviceTraceabilityMapper.update(null, traceabilityLambdaUpdateWrapper);
                    traceInformationMapper.update(null, informationLambdaUpdateWrapper);
                }
                equipmentInventoryMapper.updateById(entity);
            }
        }catch (DuplicateKeyException ex){
            throw new ServiceException(String.format("出厂编号为 %s 的设备已存在，不允许存在重复设备", equipmentInventoryDto.getD03()));
        }


		TorchResponse response = new TorchResponse();
        EquipmentInventoryVO vo = new EquipmentInventoryVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	@Override
	//@Cacheable(key = "#p0")
	public TorchResponse<EquipmentInventoryVO> findEquipmentInventory(String id) {
		EquipmentInventoryVO vo = new EquipmentInventoryVO();
		if (!HuatekTools.isEmpty(id)) {
			EquipmentInventory entity = equipmentInventoryMapper.selectById(id);
			if(HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
			}
			BeanUtils.copyProperties(entity, vo);
		}
		TorchResponse<EquipmentInventoryVO> response = new TorchResponse<>();
		response.setStatus(Constant.REQUEST_SUCCESS);
		response.getData().setData(vo);
		return response;
	}

	@SuppressWarnings("rawtypes")
	@Override
	//@CacheEvict(allEntries = true)
	@Transactional(rollbackFor = Exception.class)
	public TorchResponse delete(String[] ids) {
		equipmentInventoryMapper.deleteBatchIds(Arrays.asList(ids));
		TorchResponse  response = new TorchResponse();
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
	}

	public TorchResponse getOptionsList(String id){
	    if(selectOptionsFuncMap.size() == 0){
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("manufacturer",equipmentInventoryMapper::selectOptionsByManufacturer);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("deviceTypeCode",equipmentInventoryMapper::selectOptionsByDeviceType);
 		    //初始化外键函数
 		    selectOptionsFuncMap.put("belongingGroup",equipmentInventoryMapper::selectOptionsByBelongingGroup);
        }

	    //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
		PageHelper.startPage(1, 1000);
		Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

  		TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
  		response.getData().setData(selectOptionsVOs);
   		response.setStatus(Constant.REQUEST_SUCCESS);
   		response.getData().setCount(selectOptionsVOs.getTotal());
   		return response;
	}





    @Override
    @ExcelExportConversion(tableName = "equipment_inventory", convertorFields = "trace_information_d0#tracingMethod,deviceCategory,status")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<EquipmentInventoryVO> selectEquipmentInventoryList(EquipmentInventoryDTO dto) {
        return equipmentInventoryMapper.selectEquipmentInventoryList(dto);
    }

    /**
     * 导入设备台账数据
     *
     * @param equipmentInventoryList 设备台账数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "equipment_inventory", convertorFields = "d0,deviceCategory,status,equipment_inventory_department_team#belongingGroup,trace_information_d0#tracingMethod")
    public TorchResponse importEquipmentInventory(List<EquipmentInventoryVO> equipmentInventoryList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(equipmentInventoryList) || equipmentInventoryList.size() == 0) {
            throw new ServiceException("导入设备台账数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        int index = 0;
        for (EquipmentInventoryVO vo : equipmentInventoryList) {
            index++;
            String tracebackValidityPeriod = "";
            if (!DateTimeUtil.isValidDate(vo.getTracebackDate(), "yyyy-MM-dd")){
                failureNum++;
                failureMsg.append("<br/>" + failureNum + String.format("、第%d行导入数据有误，溯源日期格式不正确，请按照xxxx-xx-xx的格式输入", index));
                continue;
            }
            Date tracebackDate = DateUtil.parse(vo.getTracebackDate());
            if (StrUtil.isNotEmpty(vo.getTracebackDate()) && vo.getTracebackPeriod() != null){
                int period = vo.getTracebackPeriod();
                Date nextTracebackDate = DateUtil.offsetMonth(tracebackDate, period);
                tracebackValidityPeriod = DateUtil.format(nextTracebackDate, "yyyy-MM-dd");
            }
            try {
                EquipmentInventory equipmentInventory = new EquipmentInventory();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum ++;
                    continue;
                }
                BeanUtils.copyProperties(vo, equipmentInventory);
                QueryWrapper<EquipmentInventory> wrapper = new QueryWrapper();
                EquipmentInventory oldEquipmentInventory = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn: unionColumns) {
                        try {
                            Field field = EquipmentInventoryVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<EquipmentInventory> oldEquipmentInventoryList = equipmentInventoryMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldEquipmentInventoryList) && oldEquipmentInventoryList.size() > 1) {
                        equipmentInventoryMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldEquipmentInventoryList) && oldEquipmentInventoryList.size() == 1) {
                        oldEquipmentInventory = oldEquipmentInventoryList.get(0);
                    }
                }
                if (StringUtils.isNull(oldEquipmentInventory)) {
                    BeanValidators.validateWithException(validator, vo);
                    EquipmentInventoryDTO equipmentInventoryDTO = BeanUtil.toBean(equipmentInventory, EquipmentInventoryDTO.class);
                    //设备分类存储为编码
                    String deviceType = vo.getDeviceType();
                    if (StrUtil.isNotEmpty(deviceType)){
                        LambdaQueryWrapper<DeviceType> queryWrapper = Wrappers.lambdaQuery(DeviceType.class)
                                .eq(DeviceType::getDeviceTypeName, deviceType);
                        DeviceType devicetype = deviceTypeMapper.selectOne(queryWrapper);
                        equipmentInventoryDTO.setDeviceTypeCode(devicetype.getDeviceTypeCode());
                    }
                    EquipmentInventoryVO equipmentInventoryVO = (EquipmentInventoryVO)this.saveOrUpdate(equipmentInventoryDTO).getData().getData();
                    //同时还要插入溯源信息记录和设备溯源记录，均是完成状态
                    TraceInformationDTO traceInformationDTO = TraceInformationDTO.builder()
                            .d0(vo.getD0())
                            .deviceSerialNumber(vo.getDeviceSerialNumber())
                            .d01(vo.getCalibUnit())
                            .tracebackDate(DateUtil.format(tracebackDate, "yyyy-MM-dd"))
                            .tracebackPeriod((long) vo.getTracebackPeriod())
                            .tracebackValidityPeriod(tracebackValidityPeriod)
                            .status(DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_FINISHED)
                            .codexTorchMasterFormId(equipmentInventoryVO.getId())
                            .build();
                    traceInformationService.saveOrUpdate(traceInformationDTO);
                    DeviceTraceabilityDTO deviceTraceabilityDTO = DeviceTraceabilityDTO.builder()
                            .equipmentInventoryId(equipmentInventoryVO.getId())
                            .deviceSerialNumber(equipmentInventoryVO.getDeviceSerialNumber())
                            .specificationModel(equipmentInventoryVO.getSpecificationModel())
                            .deviceName(equipmentInventoryVO.getDeviceName())
                            .scheduledTraceabilityDate(vo.getTracebackDate())
                            .tracebackDate(DateUtil.format(tracebackDate, "yyyy-MM-dd"))
                            .traceabilityCycleinMonths((long) vo.getTracebackPeriod())
                            .traceabilityMethod(vo.getTracingMethod())
                            .status(DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_FINISHED)
                            .nextTraceabilityDate(tracebackValidityPeriod)
                            .build();
                    deviceTraceabilityService.saveOrUpdate(deviceTraceabilityDTO);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldEquipmentInventory, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    equipmentInventoryMapper.updateById(oldEquipmentInventory);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 更新成功");
                }  else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 已存在");
                }
            } catch (Exception e)  {
                failureNum++;
                String msg = "<br/>" + failureNum + "、设备编号 " + vo.getDeviceSerialNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(EquipmentInventoryVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();


        if (HuatekTools.isEmpty(vo.getD0())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>是否固定资产不能为空!");
        }

        if (HuatekTools.isEmpty(vo.getDeviceType())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>设备类型不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getD03())){
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>出厂编号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getSpecificationModel())){
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>规格型号不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getBelongingGroup())) {
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>所属分组不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getStatus())){
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>状态不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getTracingMethod())){
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>溯源方式不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getTracebackPeriod())){
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>溯源周期不能为空!");
        }
        if (HuatekTools.isEmpty(vo.getTracebackDate())){
            failureRecord++;
            failureNotNullMsg.append("<br/>" + failureRecord + "=>溯源日期不能为空!");
        }
//        if (!HuatekTools.isEmpty(vo.getBelongingGroup())) {
//            List<String> belongingGroupList = Arrays.asList(vo.getBelongingGroup().split(","));
//            List<SysGroup> list = sysGroupMapper.selectList(new QueryWrapper<SysGroup>().in("group_code", belongingGroupList));
//            if (CollectionUtils.isEmpty(list)) {
//                failureRecord++;
//                failureRecordMsg.append("所属分组=" + vo.getBelongingGroup() + "; ");
//            }
//        }
        if (failureRecord > 0) {
            failureNum ++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectEquipmentInventoryListByIds(List<String> ids) {
        List<EquipmentInventoryVO> equipmentInventoryList = equipmentInventoryMapper.selectEquipmentInventoryListByIds(ids);

		TorchResponse<List<EquipmentInventoryVO>> response = new TorchResponse<List<EquipmentInventoryVO>>();
		response.getData().setData(equipmentInventoryList);
		response.setStatus(200);
		response.getData().setCount((long)equipmentInventoryList.size());
		return response;
    }

    /**
     * 设备台账主子表单组合提交
     *
	 * @param equipmentInventoryDto 设备台账DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitMasterDetails(EquipmentInventoryDTO equipmentInventoryDto){
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(equipmentInventoryDto.getCodexTorchDeleted())) {
            equipmentInventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }

        //非必要字段处理
//        equipmentInventoryDto.setId("");
        equipmentInventoryDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

        equipmentInventoryDto.setCodexTorchDetailItemIds("");


        TorchResponse<EquipmentInventoryVO> masterSubmitResp = this.saveOrUpdate(equipmentInventoryDto);
        EquipmentInventoryVO masterVo = masterSubmitResp.getData().getData();

        List<TraceInformationDTO> traceInformationDTOs = new ArrayList<>();
        if (equipmentInventoryDto.getDetailFormItems() != null && equipmentInventoryDto.getDetailFormItems().length > 0) {
            traceInformationDTOs = Arrays.asList(equipmentInventoryDto.getDetailFormItems());
        }
//        else if (StringUtils.isNotEmpty(equipmentInventoryDto.getCodexTorchDetailItemIds())) {
//        } else {
//            throw new ServiceException("表单提交异常，表单明细项为空");
//        }

        for(TraceInformationDTO traceInformationDto : traceInformationDTOs){
            traceInformationDto.setId("");

            //非必要字段处理
            traceInformationDto.setCodexTorchDeleted(Constant.DEFAULT_NO);

//            traceInformationDto.setEquipmentInventoryId(masterVo.getId());
            //主子表关联ID
            traceInformationDto.setCodexTorchMasterFormId(masterVo.getId());
            // 业务字段管理
            traceInformationDto.setDeviceSerialNumber(masterVo.getDeviceSerialNumber());
            if (StrUtil.equals(masterVo.getStatus(), DicConstant.deviceManagement.EQUIPMENT_INVENTORY_STATUS_SUSPENDED)
                || StrUtil.equals(masterVo.getStatus(), DicConstant.deviceManagement.EQUIPMENT_INVENTORY_STATUS_SCRAPPED)){
                traceInformationDto.setStatus(DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_INVALID);
            }else {
                traceInformationDto.setStatus(DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_UN_FINISHED);
            }
            //提交
            TorchResponse<TraceInformationVO> detailSubmitResp = traceInformationService.saveOrUpdate(traceInformationDto);
            TraceInformationVO detailVo = detailSubmitResp.getData().getData();

            //保存本次新增时的溯源记录
            DeviceTraceabilityDTO oldTraceabilityDTO = DeviceTraceabilityDTO.builder()
                    .equipmentInventoryId(masterVo.getId())
                    .deviceSerialNumber(masterVo.getDeviceSerialNumber())
                    .specificationModel(masterVo.getSpecificationModel())
                    .deviceName(masterVo.getDeviceName())
                    .scheduledTraceabilityDate(traceInformationDto.getTracebackDate())
                    .tracebackDate(traceInformationDto.getTracebackDate())
                    .traceConfirmForm(traceInformationDto.getTracebackReport())
                    .traceabilityCycleinMonths(traceInformationDto.getTracebackPeriod())
                    .traceabilityMethod(traceInformationDto.getD0())
                    .tracebackScheme(traceInformationDto.getTracebackScheme())
                    .status(DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_FINISHED)
                    .build();
            deviceTraceabilityService.saveOrUpdate(oldTraceabilityDTO);
            //新增下次设备溯源数据
            DeviceTraceabilityDTO deviceTraceability = DeviceTraceabilityDTO.builder()
                    .equipmentInventoryId(masterVo.getId())
                    .deviceSerialNumber(masterVo.getDeviceSerialNumber())
                    .specificationModel(masterVo.getSpecificationModel())
                    .deviceName(masterVo.getDeviceName())
                    .traceabilityCycleinMonths(traceInformationDto.getTracebackPeriod())
                    .traceabilityMethod(traceInformationDto.getD0())
                    .scheduledTraceabilityDate(traceInformationDto.getTracebackValidityPeriod())
                    .tracebackScheme(traceInformationDto.getTracebackScheme())
                    .status(traceInformationDto.getStatus())
                    .build();
            deviceTraceabilityService.saveOrUpdate(deviceTraceability);

        }

		TorchResponse response = new TorchResponse();
        response.getData().setData(masterVo);
		response.setStatus(Constant.REQUEST_SUCCESS);
		return response;
    }

    /**
     * 查询设备最新的溯源记录
     * @param deviceSerialNumber
     * @return
     */
    @Override
    public TorchResponse getLatestTraceInfo(String deviceSerialNumber) {
        TorchResponse response = traceInformationService.getLatestTraceInfo(deviceSerialNumber);
        return response;
    }

    /**
     * 查询相同设备类型的设备信息列表
     * @param requestParam 设备类型
     * @return
     */
    @Override
    public TorchResponse findDeviceListByType(String requestParam) {
        List<DeviceInfoVO> deviceInfoVOList = equipmentInventoryMapper.findDeviceListByType(requestParam);
        TorchResponse response = new TorchResponse();
        response.getData().setData(deviceInfoVOList);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount((long)deviceInfoVOList.size());
        return response;

    }

    @Override
    public TorchResponse updateStatus(EquipmentInventoryUpdateStatusDTO requestParam) {
        if (CollUtil.isEmpty(requestParam.getIds())){
            throw new ServiceException("所选设备为空，请选择要修改的设备");
        }
        equipmentInventoryMapper.updateStatus(requestParam);
        //依次判断状态是否修改为停用/报废 修改设备溯源状态为已失效
        if (StrUtil.equals(requestParam.getStatus(), DicConstant.deviceManagement.EQUIPMENT_INVENTORY_STATUS_SCRAPPED)
                || StrUtil.equals(requestParam.getStatus(), DicConstant.deviceManagement.EQUIPMENT_INVENTORY_STATUS_SUSPENDED)){
            //批量更新设备溯源状态
            List<DeviceTraceability> deviceTraceabilities = deviceTraceabilityMapper.selectDeviceTraceabilityListByDeviceIds(requestParam.getIds());
            List<String> deviceTraceabilityIds = deviceTraceabilities.stream().map(DeviceTraceability::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deviceTraceabilityIds)){
                LambdaUpdateWrapper<DeviceTraceability> traceabilityLambdaUpdateWrapper = Wrappers.lambdaUpdate(DeviceTraceability.class)
                        .in(DeviceTraceability::getId, deviceTraceabilityIds)
                        .set(DeviceTraceability::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_INVALID);
                deviceTraceabilityMapper.update(null, traceabilityLambdaUpdateWrapper);
            }
            //批量更新溯源信息
            LambdaUpdateWrapper<TraceInformation> informationLambdaUpdateWrapper = Wrappers.lambdaUpdate(TraceInformation.class)
                    .in(TraceInformation::getCodexTorchMasterFormId, requestParam.getIds())
                    .set(TraceInformation::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_INVALID);
            traceInformationMapper.update(null, informationLambdaUpdateWrapper);
        }
        TorchResponse response = new TorchResponse();
        return  response;

    }

    @Override
    public TorchResponse calibrationRate() {
        //计算出设备状态为已完成和已过期的数量，然后求设备即时校准率
        LambdaQueryWrapper<DeviceTraceability> onTimeQueryWrapper = Wrappers.lambdaQuery(DeviceTraceability.class)
                .eq(DeviceTraceability::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_FINISHED);

        LambdaQueryWrapper<DeviceTraceability> expiredQueryWrapper = Wrappers.lambdaQuery(DeviceTraceability.class)
                .eq(DeviceTraceability::getStatus, DicConstant.deviceManagement.DEVICE_MANAGEMENT_STATUS_EXPIRED);
        Long onTimeCompletion = deviceTraceabilityMapper.selectCount(onTimeQueryWrapper);
        Long expired = deviceTraceabilityMapper.selectCount(expiredQueryWrapper);

        // 计算校准率（避免除以零）
        BigDecimal calibrationRate;
        long total = onTimeCompletion + expired;
        String calibrationRateStr;
        if (total == 0) {
            // 没有数据时默认返回-
            calibrationRateStr = "-";
        } else {
            // 计算比例并转换为百分数（乘以100）
            calibrationRate = new BigDecimal(onTimeCompletion)
                    .divide(new BigDecimal(total), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100))
                    .setScale(2, RoundingMode.HALF_UP);
            calibrationRateStr = calibrationRate + "%";
        }

        TorchResponse response = new TorchResponse();
        response.getData().setData(calibrationRateStr);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;


    }

    @Override
    public TorchResponse<List<EquipmentInventorySampleVO>> getAllEquipmentInventory() {
        List<EquipmentInventorySampleVO> equipmentInventoryList = equipmentInventoryMapper.getAllEquipmentInventory();

        TorchResponse<List<EquipmentInventorySampleVO>> response = new TorchResponse<List<EquipmentInventorySampleVO>>();
        response.getData().setData(equipmentInventoryList);
        response.setStatus(200);
        response.getData().setCount((long)equipmentInventoryList.size());
        return response;
    }

}
