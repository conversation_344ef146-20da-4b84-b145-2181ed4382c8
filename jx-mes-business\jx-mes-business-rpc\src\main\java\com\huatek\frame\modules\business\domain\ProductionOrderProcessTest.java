package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;

/**
* @description 工单工序试验数据
* <AUTHOR>
* @date 2025-07-30
**/
@Setter
@Getter
@TableName("production_order_process_test")
public class ProductionOrderProcessTest implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单
     **/
    @TableField(value = "work_order"
    )
    private String workOrder;

    
    /**
	 * 工序
     **/
    @TableField(value = "process_code"
    )
    private String processCode;

    /**
     * 工序报工内容
     **/
    @TableField(value = "process_data"
    )
    private String processData;

    /**
     * 设备数据
     **/
    @TableField(value = "device_data"
    )
    private String deviceData;
    /**
	 * 试验结果
     **/
    @TableField(value = "test_data"
    )
    private String testData;

    /**
     * 任务id
     **/
    @TableField(value = "task_id"
    )
    private String taskId;

    /**
     * 执行顺序
     **/
    @TableField(value = "execution_sequence"
    )
    private Integer executionSequence;


    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    

    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}