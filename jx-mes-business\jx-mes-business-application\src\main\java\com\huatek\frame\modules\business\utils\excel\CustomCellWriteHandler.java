package com.huatek.frame.modules.business.utils.excel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import java.awt.Color;

public class CustomCellWriteHandler extends AbstractCellStyleStrategy implements CellWriteHandler {
    private XSSFCellStyle headCellStyle;
    private XSSFCellStyle contentCellStyle;

    @Override
    protected void initCellStyle(Workbook workbook) {
        headCellStyle = (XSSFCellStyle) StyleUtil.buildHeadCellStyle(workbook, null);
//        headCellStyle.setFillForegroundColor(new XSSFColor(new Color(146, 169, 219)));
        headCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headCellStyle.setWrapText(Boolean.TRUE);
        headCellStyle.setBorderBottom(BorderStyle.THIN);
        headCellStyle.setBorderLeft(BorderStyle.THIN);
        headCellStyle.setBorderRight(BorderStyle.THIN);
        headCellStyle.setBorderTop(BorderStyle.THIN);
        Font font = workbook.createFont();
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 12);
        font.setBold(true);
        headCellStyle.setFont(font);


        contentCellStyle = (XSSFCellStyle) StyleUtil.buildHeadCellStyle(workbook, null);
//        contentCellStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(255, 255, 255)));
        contentCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        contentCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentCellStyle.setWrapText(Boolean.TRUE);
        contentCellStyle.setBorderBottom(BorderStyle.THIN);
        contentCellStyle.setBorderLeft(BorderStyle.THIN);
        contentCellStyle.setBorderRight(BorderStyle.THIN);
        contentCellStyle.setBorderTop(BorderStyle.THIN);
        Font font1 = workbook.createFont();
        font1.setFontName("微软雅黑");
        font1.setFontHeightInPoints((short) 11);

        headCellStyle.setFont(font);
    }

    @Override
    protected void setHeadCellStyle(Cell cell, Head head, Integer relativeRowIndex) {
        cell.setCellStyle(headCellStyle);
    }

    @Override
    protected void setContentCellStyle(Cell cell, Head head, Integer relativeRowIndex) {


        cell.setCellStyle(contentCellStyle);
    }

}
