<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.insite.message.mapper.WbNoticeMessageMapper">
    
    <resultMap type="com.huatek.frame.insite.message.domain.WbNoticeMessage" id="WbNoticeMessageResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="type"    column="type"    />
        <result property="senderId"    column="sender_id"    />
        <result property="senderName"    column="sender_name"    />
        <result property="receiverId"    column="receiver_id"    />
        <result property="receiverName"    column="receiver_name"    />
        <result property="isRead"    column="is_read"    />
        <result property="readTime"    column="read_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="priority"    column="priority"    />
        <result property="targetUrl"    column="target_url"    />
        <result property="bizType"    column="biz_type"    />
        <result property="bizId"    column="biz_id"    />
    </resultMap>

    <sql id="selectWbNoticeMessageVo">
        select id, title, content, type, sender_id, sender_name, receiver_id, receiver_name, is_read, read_time, create_time, update_time, del_flag, priority, target_url, biz_type, biz_id from wb_notice_message
    </sql>

    <select id="selectWbNoticeMessageList" parameterType="com.huatek.frame.insite.message.domain.WbNoticeMessage" resultMap="WbNoticeMessageResult">
        <include refid="selectWbNoticeMessageVo"/>
        <where>  
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="senderId != null  and senderId != ''"> and sender_id = #{senderId}</if>
            <if test="senderName != null  and senderName != ''"> and sender_name like concat('%', #{senderName}, '%')</if>
            <if test="receiverId != null  and receiverId != ''"> and receiver_id = #{receiverId}</if>
            <if test="receiverName != null  and receiverName != ''"> and receiver_name like concat('%', #{receiverName}, '%')</if>
            <if test="isRead != null "> and is_read = #{isRead}</if>
            <if test="readTime != null "> and read_time = #{readTime}</if>
            <if test="priority != null "> and priority = #{priority}</if>
            <if test="targetUrl != null  and targetUrl != ''"> and target_url = #{targetUrl}</if>
            <if test="bizType != null  and bizType != ''"> and biz_type = #{bizType}</if>
            <if test="bizId != null  and bizId != ''"> and biz_id = #{bizId}</if>
        </where>
    </select>
    
    <select id="selectWbNoticeMessageById" parameterType="Long" resultMap="WbNoticeMessageResult">
        <include refid="selectWbNoticeMessageVo"/>
        where id = #{id}
    </select>

    <insert id="insertWbNoticeMessage" parameterType="com.huatek.frame.insite.message.domain.WbNoticeMessage" useGeneratedKeys="true" keyProperty="id">
        insert into wb_notice_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="type != null">type,</if>
            <if test="senderId != null">sender_id,</if>
            <if test="senderName != null">sender_name,</if>
            <if test="receiverId != null and receiverId != ''">receiver_id,</if>
            <if test="receiverName != null">receiver_name,</if>
            <if test="isRead != null">is_read,</if>
            <if test="readTime != null">read_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="priority != null">priority,</if>
            <if test="targetUrl != null">target_url,</if>
            <if test="bizType != null">biz_type,</if>
            <if test="bizId != null">biz_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="type != null">#{type},</if>
            <if test="senderId != null">#{senderId},</if>
            <if test="senderName != null">#{senderName},</if>
            <if test="receiverId != null and receiverId != ''">#{receiverId},</if>
            <if test="receiverName != null">#{receiverName},</if>
            <if test="isRead != null">#{isRead},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="priority != null">#{priority},</if>
            <if test="targetUrl != null">#{targetUrl},</if>
            <if test="bizType != null">#{bizType},</if>
            <if test="bizId != null">#{bizId},</if>
         </trim>
    </insert>

    <update id="updateWbNoticeMessage" parameterType="com.huatek.frame.insite.message.domain.WbNoticeMessage">
        update wb_notice_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="type != null">type = #{type},</if>
            <if test="senderId != null">sender_id = #{senderId},</if>
            <if test="senderName != null">sender_name = #{senderName},</if>
            <if test="receiverId != null and receiverId != ''">receiver_id = #{receiverId},</if>
            <if test="receiverName != null">receiver_name = #{receiverName},</if>
            <if test="isRead != null">is_read = #{isRead},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="priority != null">priority = #{priority},</if>
            <if test="targetUrl != null">target_url = #{targetUrl},</if>
            <if test="bizType != null">biz_type = #{bizType},</if>
            <if test="bizId != null">biz_id = #{bizId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWbNoticeMessageById" parameterType="Long">
        delete from wb_notice_message where id = #{id}
    </delete>

    <delete id="deleteWbNoticeMessageByIds" parameterType="String">
        delete from wb_notice_message where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="updateWbNoticeMessageReadStatus" parameterType="Long">
        update wb_notice_message
        set is_read = '1'
        where id = #{id}
    </update>
</mapper>