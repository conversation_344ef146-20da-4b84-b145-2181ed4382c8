package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.AccountingStandard;
import com.huatek.frame.modules.business.domain.vo.AccountingStandardVO;
import com.huatek.frame.modules.business.service.dto.AccountingStandardDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 核算标准mapper
* <AUTHOR>
* @date 2025-08-22
**/
public interface AccountingStandardMapper extends BaseMapper<AccountingStandard> {

     /**
	 * 核算标准分页
	 * @param dto
	 * @return
	 */
	Page<AccountingStandardVO> selectAccountingStandardPage(AccountingStandardDTO dto);


    /**
     * 根据条件查询核算标准列表
     *
     * @param dto 核算标准信息
     * @return 核算标准集合信息
     */
    List<AccountingStandardVO> selectAccountingStandardList(AccountingStandardDTO dto);

	/**
	 * 根据IDS查询核算标准列表
	 * @param ids
	 * @return
	 */
    List<AccountingStandardVO> selectAccountingStandardListByIds(@Param("ids") List<String> ids);

	/**
	 * 查询结算单位，客户信息管理
	 * @param s
	 * @return
	 */
    Page<SelectOptionsVO> selectOptionsBySettlementUnit(String s);
}