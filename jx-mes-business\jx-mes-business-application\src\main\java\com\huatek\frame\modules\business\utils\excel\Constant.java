package com.huatek.frame.modules.business.utils.excel;

/**
 * @Description: 系统常量类
 * <AUTHOR>
 * @Since JDK 1.8
 * @Version V1.0
 * @Date:2023年2月9日 上午10:15:25
 * Copyright (c) 2023, www.huatek.com All Rights Reserved.
 */

public class Constant extends com.huatek.frame.common.utils.Constant {

	/**
	 * 公共常量定义区
	 * <AUTHOR>
	 *
	 */
	public class Common {
		public static final String CHAR_Y = "Y";
		public static final String CHAR_N = "N";
		public static final String CHAR_0 = "0";
		public static final String CHAR_1 = "1";
		public static final String CHAR_2 = "2";
		public static final String CHAR_3 = "3";
		public static final String CHAR_4 = "4";
		public static final String CHAR_5 = "5";
		public static final String CHAR_6 = "6";
		public static final String CHAR_7 = "7";
		public static final String CHAR_8 = "8";
		public static final String CHAR_9 = "9";
		public static final String STR_Y = "是";
		public static final String STR_N = "否";
		public static final int INT_0 = 0;
		public static final int INT_1 = 1;
		public static final int INT_2 = 2;
		public static final int INT_3 = 3;
		public static final int INT_4 = 4;
		public static final int INT_5 = 5;
		public static final int INT_6 = 6;
		public static final int INT_7 = 7;
		public static final int INT_8 = 8;
		public static final int INT_9 = 9;

		/**
		 * 系统当前数据库类型(用于数据同步时数据类型映射，当前有MySQL,Oracle,SqlServer三种取值)
		 */
		public static final String SYS_DATABASE_TYPE = "MYSQL";
	}

	/**
	 * 数据库常量
	 * <AUTHOR>
	 *
	 */
	public class DataBase{

		/**
		 * 数据类型
		 * <AUTHOR>
		 */
		public class DataType{
			public static final String DIC_DATATYPE_INT = "INT";
			public static final String DIC_DATATYPE_BIGINT = "BIGINT";
			public static final String DIC_DATATYPE_VARCHAR = "VARCHAR";
			public static final String DIC_DATATYPE_DECIMAL = "DECIMAL";
			public static final String DIC_DATATYPE_DATE = "DATE";
			public static final String DIC_DATATYPE_TIMESTAMP = "TIMESTAMP";
		}
	}

	public  class  CapacityType{
		public static final String YQJ = "2";
		public static final String REALY = "4";
		public static final String DXX = "3";
		public static final String TZX = "1";
	}
	/**
	 * 附件类型
	 * <AUTHOR>
	 *
	 */
	public class AttachType{
		/**
		 * 设计管理-设计任务完成度上传
		 */
		public static final String DESIGN_TASK_COMPLETE = "COMPLETE";

		/**
		 * 设计管理-设计任务重新打开
		 */
		public static final String DESIGN_TASK_REOPEN = "REOPEN";

		/**
		 * 设计管理-设计任务审核
		 */
		public static final String DESIGN_TASK_CHECK = "CHECK";
	}

	/**
	 * MES-BASIC
	 * <AUTHOR>
	 *
	 */
	public class MesBasic {

		/**
		 * 字典编码前缀-生产阶段
		 */
		public static final String DIC_PHASE_PREFIX = "MES_CATEGORY_PHASE_";

		/**
		 * 字典编码前缀-工序类型
		 */
		public static final String DIC_CATEGORY_PREFIX = "MES_CLASSIFICATION_CATEGORY_";

		/**
		 * 设计任务状态-未分派
		 */
		public static final String DIC_DESIGN_TASK_STATE_UNASSIGNED = "UNASSIGNED";

		/**
		 * 设计任务状态-已分派
		 */
		public static final String DIC_DESIGN_TASK_STATE_ASSIGNED = "ASSIGNED";

		/**
		 * 设计任务状态-进行中
		 */
		public static final String DIC_DESIGN_TASK_STATE_UNDERWAY = "UNDERWAY";

		/**
		 * 设计任务状态-待审核
		 */
		public static final String DIC_DESIGN_TASK_STATE_WAIT_REVIEW = "WAIT_REVIEW";

		/**
		 * 设计任务状态-已完成
		 */
		public static final String DIC_DESIGN_TASK_STATE_COMPLETE = "COMPLETE";

		/**
		 * 设计任务状态-被驳回
		 */
		public static final String DIC_DESIGN_TASK_STATE_REJECTED = "REJECTED";

		/**
		 * 设计任务操作结果-通过
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_RESULT_APPROVE = "APPROVE";

		/**
		 * 设计任务操作结果-驳回
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_RESULT_REJECT = "REJECT";

		/**
		 * 设计任务操作结果-未完成
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_RESULT_UNDERWAY = "UNDERWAY";

		/**
		 * 设计任务操作结果-已完成
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_RESULT_COMPLETE = "COMPLETE";

		/**
		 * 设计任务操作类型-指派
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_TYPE_ASSIGN = "ASSIGN";

		/**
		 * 设计任务操作类型-指派
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_TYPE_COPY= "COPY";

		/**
		 * 设计任务操作结果-复制
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_RESULT_COPY = "COPY";

		/**
		 * 设计任务操作类型-改派
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_TYPE_REASSIGNMENT = "REASSIGNMENT";

		/**
		 * 设计任务操作类型-执行
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_TYPE_EXECUTE = "EXECUTE";

		/**
		 * 设计任务操作类型-审核
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_TYPE_REVIEW = "REVIEW";

		/**
		 * 设计任务操作类型-重新打开
		 */
		public static final String DIC_DESIGN_TASK_OPERATE_TYPE_REOPEN = "REOPEN";

		/**
		 * 设计任务重启原因-设计缺陷
		 */
		public static final String DIC_DESIGN_TASK_REOPEN_REASON_DESIGN_DEFECT = "DESIGN_DEFECT";

		/**
		 * 设计任务重启原因-其他
		 */
		public static final String DIC_DESIGN_TASK_REOPEN_REASON_OTHER = "OTHER";

		/**
		 * 方案类型-针卡设计工序方案
		 */
		public static final String DIC_PLAN_TYPE_ZKSJGXFA = "ZKSJGXFA";

		/**
		 * 方案类型-硬件设计工序方案
		 */
		public static final String DIC_PLAN_TYPE_YJSJGXFA = "YJSJGXFA";


		/**
		 * 方案类型-预销售针卡评估工序方案
		 */
		public static final String DIC_PLAN_TYPE_ZKPGFA = "zkpgfa";

		/**
		 * 方案类型-预销售硬件评估工序方案
		 */
		public static final String DIC_PLAN_TYPE_YJPGFA = "yjpgfa";


		/**
		 * 设计评估任务操作记录类型-开始评估
		 */
		public static final String DIC_DESIGN_REVIEW_OPERATE_TYPE_START = "START";

		/**
		 * 设计评估任务操作记录类型-评估完成
		 */
		public static final String DIC_DESIGN_REVIEW_OPERATE_TYPE_COMPLETE = "COMPLETE";
		/**
		 * 设计评估任务操作记录类型-审核
		 */
		public static final String DIC_DESIGN_REVIEW_OPERATE_TYPE_APPROVE = "APPROVE";

		/**
		 * 设计评估任务操作记录类型-审核
		 */
		public static final String DIC_DESIGN_REVIEW_OPERATE_TYPE_QAAPPROVE = "QAAPPROVE";
		/**
		 * 设计评估任务操作记录类型-驳回
		 */
		public static final String DIC_DESIGN_REVIEW_OPERATE_TYPE_CHECK = "CHECK";

		/**
		 * 设计图纸工序
		 */
		public static final String PROCESS_DESIGN_PICTURE = "设计图纸";

		/**
		 * 设计评估任务操作记录类型-重启
		 */
		public static final String DIC_DESIGN_REVIEW_OPERATE_TYPE_RESTART = "RESTART";

		/**
		 * 数据库类型-MySql
		 */
		public static final String DIC_DATABASE_TYPE_MYSQL = "MYSQL";

		/**
		 * 数据库类型-SqlServer
		 */
		public static final String DIC_DATABASE_TYPE_SQLSERVER = "SQLSERVER";

		/**
		 * 数据库类型-Oracle
		 */
		public static final String DIC_DATABASE_TYPE_ORACLE = "ORACLE";

		/**
		 * 编码前缀-设计任务
		 */
		public static final String NUMBER_DESIGN_TASK_PREFIX = "TASK";

	}

	/**
	 * 班次
	 */
	public class Grade{
		public static final String GRADE_A="A";
		public static final String GRADE_B="B";
		public static final String GRADE_NORMAL="normal";
	}


	public class SaleOperateType{
		/**
		 * 暂停
		 */
		public static final String  PAUSE="PAUSE";

		/**
		 * 取消
		 */
		public static final String CANCEL="CANCEL";

	   /**
	    * 继续
	    */
		public static final String CONTINUE="CONTINUE";

	   /**
		* 重新打开
		*/
		public static final String RELOAD="RELOAD";

		/**
		 * 设置发票金额
		 */
		public static final String SETINVOICE="SETINVOICE";

		/**
		 * 完成
		 */
		public static final String COMPLETED="COMPLETED";
	}

	/**
	 * 数据类型
	 */
	public class DataType{
		public static final String NUMBER="1";
		public static final String BOOL="2";
		public static final String ATTACH="3";
		public static final String SELECT_MYSELF="4";
	}

	/**
	 * 岗位
	 */
	public class Post{
		public static final String POST_PH="PH";//插针
		public static final String POST_WI="WI";//穿线
		public static final String POST_AS="AS";//组装
		public static final String POST_REFLOW="REFLOW";
	}

	/**
	 * 成品类型
	 */
	public class ProductType{
		public static final String PRODUCT_VM="VM";
		public static final String PRODUCT_VC="VC";
		public static final String PRODUCT_VS="VS";
		public static final String PRODUCT_VX="VX";
		public static final String PRODUCT_CANTILEVER="cantilever";
	}

	/**
	 * 订单类型
	 */
	public class orderType{
		/**
		 * 	普通生产
		 */
		public static final String GENERAL="01";
		/**
		 * 	维修
		 */
		public static final String MAINTENANCE="02";
		/**
		 * 	异常
		 */
		public static final String ANOMALY="03";
		/**
		 * 	设备生产
		 */
		public static final String DEVICE="04";
		/**
		 * 	普通生产PH
		 */
		public static final String GENERAL_PH="05";
		/**
		 * 	试验
		 */
		public static final String EXPERIMENT="m05";
	}
	/**
	 * 人员能力
	 */
	public class Skill{
		public static final String SKILL_VM="VM_PIN";
		public static final String SKILL_VC="VC_PIN";
		public static final String SKILL_VS="VS_PIN";
		public static final String SKILL_VX="VX_PIN";
		public static final String SKILL_THREADING="THREADING";
		public static final String SKILL_GOLD_PLATED="GOLD_PLATED";
		public static final String SKILL_REFLOW="REFLOW";
		public static final String SKILL_ASSEMBLY="ASSEMBLY";
	}

	/**
	 * 部门
	 */
	public class Department{
		public static final String DEPART_PRODUCTION="production";//生产部
		public static final String DEPART_DESIGN="design";//设计部
		public static final String DEPART_INSPECT="quality_inspect";//质检部
		public static final String DEPART_PLAN="plan";//计划部
		public static final String DEPART_CPC="CPC";//CPC
		public static final String DEPART_VPC="VPC";//VPC
	}

	/**
	 *      * 1生产/2结构销售设计/3硬件销售设计/4质检/5加工激光/6加工CNC
	 */
	public class INDICATOR{
		public static final String INDICATOR_CONFIG_TYPE1="1";//生产
		public static final String INDICATOR_CONFIG_TYPE2="2";//结构销售设计
		public static final String INDICATOR_CONFIG_TYPE3="3";//硬件销售设计
		public static final String INDICATOR_CONFIG_TYPE4="4";//质检
		public static final String INDICATOR_CONFIG_TYPE5="5";//加工激光
		public static final String INDICATOR_CONFIG_TYPE6="6";//加工CNC
	}
	/**
	 * 预销售订单常量
	 */
	public class MesPreSale{

		/**
		 * 针卡评估
		 */
		public static final String MES_PRESALE_REVIEW_ZCARD = "1";

		/**
		 * pcb评估
		 */
		public static final String MES_PRESALE_REVIEW_PCB = "2";



		/**
		 * 待评估
		 */
		public static final String MES_PRESALE_NO_REVIEW = "0";

		/**
		 * 评估中
		 */
		public static final String MES_PRESALE_REVIEWING = "1";

		/**
		 * 已完成
		 */
		public static final String MES_PRESALE_REVIEWED = "2";
		/**
		 * 无需评估
		 */
		public static final String MES_PRESALE_NODESIGN = "3";

		/**
		 * 待补全信息
		 */
		public static final String MES_PRESALE_BUDESIGN = "4";


		/**
		 * 驳回
		 */
		public static final String MES_PRESALE_REJECTED = "5";
		/**
		 * 待分配
		 */
		public static final String MES_PRESALE_NO_ASSIGN = "6";

		/**
		 * 审核完成
		 */
		public static final String MES_PRESALE_APPROVE_COMPLETE = "7";
		/**
		 * QA评审完成
		 */
		public static final String MES_PRESALE_LEADER_QA_APPROVE_COMPLETE = "8";

		/**
		 * QA 驳回
		 */
		public static final String MES_PRESALE_LEADER_QAREJECT = "9";

		/**
		 * 领导审批
		 */
		public static final String MES_PRESALE_LEADER_APPROVE= "10";

		/**
		 * 领导审批 驳回
		 */
		public static final String MES_PRESALE_LEADER_APPROVE_REJECT= "11";


		/**
		 * 待提交
		 */
		public static final String MES_PRESALE_COMMITING = "0";

		/**
		 * 已提交待评估
		 */
		public static final String MES_PRESALE_COMMITED_REVIEWING = "1";

		/**
		 * 待补全信息
		 */
		public static final String MES_PRESALE_WRITING = "2";

		/**
		 * 设计中
		 */
		public static final String MES_PRESALE_DESIGNING = "3";

		/**
		 * 评估完成
		 */
		public static final String MES_PRESALE_FINISHED = "4";

		/**
		 * 无需评估
		 */
		public static final String MES_PRESALE_NO_NEED = "5";

		/**
		 * 审核完成
		 */
		public static final String MES_PRESALE_APPROVE_COMPLATE = "6";
		/**
		 * QA审核通过
		 */
		public static final String MES_PRESALE_QA_APPROVE_COMPLETE = "7";

		/**
		 * 领导审批不通过
		 */
		public static final String MES_PRESALE_REVIEW_LEADER_APPROVE_REJECT = "8";





		/**
		 * 报价待审核
		 */
		public static final String MES_PRESALE_OFFER_REVIEWING = "1";

		/**
		 * 报价审核通过
		 */
		public static final String MES_PRESALE_OFFER_REVIEWED = "2";

		/**
		 * 报价驳回
		 */
		public static final String MES_PRESALE_OFFER_REJECTED = "3";

		/**
		 * 预销售订单附件类型-普通附件
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_COMMON = "COMMON";
		/**
		 * 预销售订单附件类型-普通附件
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_ANNEX = "annex";

		/**
		 * 预销售订单附件类型-回复邮件
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_RESPOND_EMAIL = "RESPOND_EMAIL";
		/**
		 * 预销售订单附件类型-回复邮件
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_RESPOND_CLIENT = "client";

		/**
		 * 评估检验附件类型
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_REVIEW = "review";

		/**
		 * 过程确认上传文件
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_EXCEPTION = "exception";

		/**
		 * 维修检验附件类型
		 */
		public static final String MES_PRESALE_ATTACH_TYPE_REPAIR = "repair";



	}
	/**
	 * 预销售订单评估分配表
	 * 状态：0 待分配、1 待评估、2 评估中、3 评估完成、4 驳回
	 */
	public class MesPreSaleReviewAssignStatus{
		/**
		 * 无需评估
		 */
		public static final String NO_ASSESS ="6";
		/**
		 * 待分配
		 */
		public static final String NO_ASSIGN ="0";
		/**
		 * 待评估
		 */
		public static final String ASSIGNED ="1";
		/**
		 * 评估中
		 */
		public static final String REVIEWING ="2";
		/**
		 * 评估完成
		 */
		public static final String REVIEW_COMPLETE ="3";
		/**
		 * 驳回
		 */
		public static final String REJECT ="4";
		/**
		 * 领导审批
		 */
		public static final String LEADER_APPROVE ="5";
	}
	/**
	 * 预销售订单评估分配 操作类型
	 * 状态：assign： 分配、reject：驳回、approve：审批
	 */
	public class MesPreSlaeReviewAssingOperateType{

		public static final String INITIATE="initiate"; // 发起评估申请
		public static final String START = "start"; // 开始评估
		public static final String ASSIGN="assign";//分配
		public static final String JG_ASSIGN="jg_assign";//结构评估分配

		public static final String YJ_ASSIGN="yj_assign";//硬件评估分配
		public static final String BINDING="binding";//方案
		public static final String YJ_BINDING="yj_binding";//硬件方案绑定
		public static final String JG_BINDING="jg_binding";//结构方案绑定
		public static final String APPLICATIONREJECT="reject";//申请驳回驳回

		public static final String LEADERAPPROVEREJECT="approve_reject";//申请驳回驳回
		public static final String APPROVE="approve";//审批
		public static final String REOPEN="reopen";//重新打开
	}

	public class DesignTaskType{
		public static final String TAKS_ASSESS="0";//评估任务
		public static final String TAKS_DESIGN="1";//设计任务
	}
	/**
	 * 厂内加工
	 */
	public class FactoryMes{
		/**
		 *加工状态 待提交计划 0  待提交加工 1  待绑定2   待加工3   加工中 4 加工完成 5  部分入库 6  全部入库 7   部分出库8  全部出库 9
		 */
		public static final String NO_COMMINT="0";//待提交计划
		public static final String COMMINTED="1";//待提交加工
		public static final String WAIT_AFFIRM="2";//待绑定
		public static final String WAIT_PROCESS="3";//待加工
		public static final String PROCESSING="4";//加工中
		public static final String PROCESSED="5";//加工完成
		public static final String STORAGE_SOME="6";//部分入库
		public static final String STORAGE_ALL="7";//全部入库
		public static final String OUTBOUND_SOME="8";// 部分出库
		public static final String OUTBOUN_ALL="9";//全部出库


		/**
		 *加工任务状态 待分派 0 已分派 1 进行中 2 已完成 3
		 */
		public static final String TASK_NO_COMMINT="0";//待分派
		public static final String TASK_COMMINTED="1";//已分派
		public static final String TASK_PROCESSING="2";//进行中
		public static final String TASK_PROCESSED="3";//已完成
		public static final String TASK_CONTINUE="4";//已暂停
		public static final String TASK_CANCEL="5";//已取消



	}
	/**
	 * 加工管理厂内加工操作类型
	 */
	public static class FactoryTaskLogOperationType{
		public static final String ASSIGN="1";//分派
		public static final String BEGIN="2";//开始
		public static final String EXECUTE="3";//执行
		public static final String SUSPEND="4";//暂停
		public static final String CONTINUE="5";//继续
		public static final String ONECOMPLETE="6";//一键完成
		public static final String AUTOECOMPLETE="7";//系统自动完成
		public static final String CANCEl="8";//取消

	}

	/**
	 * 加工类型
	 */
	public static class FactoryType{
		public static final String LASER="1";//激光加工
		public static final String CNC="2";//cnc加工
		public static final String LASER_CNC="3";//激光+cnc
		public static final String CPC_CNC="4";//CPC_CNC
	}

	/**
	 * 加工类型
	 */
	public static class FactoryrRole{
		public static final String CNC_LEADER="CNC主管";//激光加工
		public static final String LASER_LEADER="激光主管";//cnc加工
		public static final String LASER_CNC_LEADER="CPC_CNC主管";//激光+CNC
	}
	/**
	 * 生产订单
	 */
	public static class ProduceOrder{
		/**
		 * 订单类型正常
		 */
		public static final String MES_ORDER_TYPE_NORMAL = "1";
		/**
		 * 订单类型返修
		 */
		public static final String MES_ORDER_TYPE_REPAIR = "2";
		/**
		 * 订单状态 0:未开始1：待排产，2：已排产，3已暂停，4，已完成5：已关闭，6：已取消
		 */
		public static final String NO_START="0";//未开始
		public static final String WAIT_SCHEDU="1";//待排产
		public static final String SCHEDULED="2";//已排产
		public static final String PAUSED="3";//2已暂停
		public static final String COMPLETED="4";//已完成4
		public static final String CLOSED="5";//已关闭
		public static final String CANCEL="6";//已取消
		/**
		 * 订单类型试验单
		 */
		public static final String MES_ORDER_TYPE_TEST = "m05";

		/**
		 * 方案工序来源
		 */
		public static final String SCHEME="0";//方案产生
		public static final String NEW="1";//新增

		/**
		 * 工作时长
		 */

		public static final Integer NUMBER_FOUR=4;
		public static final Integer NUMBER_EIGHT=8;
		public static final Integer NUMBER_TWELVE=12;
		public static final Integer NUMBER_FOURTEEN=14;
		public static final Integer NUMBER_SIXTEEN=16;

		/**
		 * 每日完成数量
		 */
		public static final Integer PH_DAY_PINS_FOUR=400;
		public static final Integer PH_DAY_PINS_EIGHT=800;
		public static final Integer PH_DAY_PINS_TWELVE=1200;
		public static final Integer PH_DAY_PINS_FOURTEEN=1400;
		public static final Integer PH_DAY_PINS_SIXTEEN=1600;
		public static final Integer THREAD_DAY_PINS=251;

	}

	/**
	 * VPC工序类型
	 */
	public class ProcessVpcType{
		public static final String PH="PH";//PH组装
		public static final String REFLOW="REFLOW";//Reflow
		public static final String COMPONENT_WELD="COMPONENT_WELD";//元器件焊接
		public static final String PIN_ASSEMBLY="PIN_ASSEMBLY";//针卡组装
		public static final String THREAD_WELD="THREAD_WELD";//穿线焊接
	}
	public class TaskOperateType {
		public static final String TASK_OPERATE_TYPE_CHECK_NORMAL = "1";
		public static final String TASK_OPERATE_TYPE_CHECK_REPAIR = "2";
	}

	/**
	 * 生产任务
	 */
	public class Task {
		/**
		 * 任务状态未开始
		 */
		public static final String MES_TASK_STATUS_NO_START = "1";
		/**
		 * 任务状态进行中
		 */
		public static final String MES_TASK_STATUS_ING = "2";
		/**
		 * 任务状态正常暂停
		 */
		public static final String MES_TASK_STATUS_STOP = "3";
		/**
		 * 任务状态异常暂停
		 */
		public static final String MES_TASK_STATUS_ABNORMAL_STOP = "4";
		/**
		 * 任务状态完成
		 */
		public static final String MES_TASK_STATUS_FINISH = "5";
		/**
		 * 任务状态取消
		 */
		public static final String MES_TASK_STATUS_CANCEL = "0";
	}
	public class HeadType{
		public static final String head_type_produce = "MES_PAD_HEAD";
	}
	public class  CPCPost{
		public static final String cpc_post = "CPC_STATION";
	}
	public class TaskStatusApp{
		public static final String  MES_TASK_STATUS_DB= "db";
		public static final String  MES_TASK_STATUS_YB= "yb";
		public static final String  MES_TASK_STATUS_GD= "gd";
	}
	public class TaskType{
		/**
		 * 正常任务
		 */
		public static final String type_normal = "0";

		/**
		 * 异常任务
		 */
		public static final String type_arnormal = "5";
	}
	public class TaskSource{
		/**
		 * 正常任务
		 */
		public static final String type_create = "0";

		/**
		 * 其他工单任务
		 */
		public static final String other_create = "1";
	}
	public class TaskDetialSource{
		/**
		 * 正常任务
		 */
		public static final String source_history = "history";

		/**
		 * 异常任务
		 */
		public static final String source_current = "current";
	}
	public class TaskException{
		/**
		 * 异常类型字典id
		 */
		public static final String exception_type_id = "7de4a4015a96e4abdbb1d0205c5950a2";

		/**
		 * 异常类型字典code
		 */
		public static final String exception_type_code = "EXCEPTION_TYPE";
	}
	public class TaskEditStatusSource{
		/**
		 * reopen
		 */
		public static final String  edit_status_reopen= "reopen";

		/**
		 * 异常任务
		 */
		public static final String edit_status_noraml = "normal";
	}
	public class TaskFileType{
		/**
		 * 正常任务
		 */
		public static final String attach_checkout = "annexes";

		/**
		 * 异常任务
		 */
		public static final String attach_feedback = "attach_feedback";

		public static final String attach_exception = "exception";

		public static final String attach_mrb = "create";

		public static final String attach_confirm = "check_task_confirm";
	}
	public class CodeType{
		public static final String code_produce = "produce_code";
		public static final String code_exception = "exception_code";
	}
	public class CustomerCodeSwitch{
		public static final String switch_code= "CUSTOMER_CODE_ABLE";
	}
	public class Phase{
		/**
		 * 质检阶段
		 */
		public static final String PHASE_QUALITY_TESTING = "ZJ";
	}

	/**
	 * 	检验类型
	 */
	public class CheckType{
		/**
		 * 评估检验
		 */
		public static final String MES_CHECKTYPE_RATE = "1";
		/**
		 * 过程检验
		 */
		public static final String MES_CHECKTYPE_PROCESS = "2";
		/**
		 * 成品检验
		 */
		public static final String MES_CHECKTYPE_PRODUCT = "3";
		/**
		 * 出货检验
		 */
		public static final String MES_CHECKTYPE_OUT = "4";
		/**
		 * 返修检验
		 */
		public static final String MES_CHECKTYPE_REPAIR = "5";
		/**
		 * 维修卡检验
		 */
		public static final String MES_CHECKTYPE_MAINTENANCE = "6";
		/**
		 * 自购件检验
		 */
		public static final String MES_CHECKTYPE_PURCHASE = "7";
		/**
		 * 客供件检验
		 */
		public static final String MES_CHECKTYPE_SUPPLY = "8";

		/**
		 * 加工件检验
		 */
		public static final String MES_CHECKTYPE_FACTORY = "9";
		/**
		 * 委外件检验
		 */
		public static final String MES_CHECKTYPE_OUTSOURCING_PURCHASE = "10";
		/**
		 * Rebuild检验单
		 */
		public static final String MES_REBUILD_INSPECT_ORDE = "11";
	}
	/**
	 * 	检验类型
	 */
	public class CheckTaskStatus{

		/**
		 * 待分派
		 */
		public static final String MES_TASK_WAITING = "0";
		/**
		 * 已分派
		 */
		public static final String MES_TASK_WAITED = "1";
		/**
		 * 进行中
		 */
		public static final String MES_TASK_RUNING = "2";
		/**
		 * 已完成
		 */
		public static final String MES_TASK_FINISHED = "3";

		/**
		 * 暂停
		 */
		public static final String MES_TASK_PAUSE = "4";

		/**
		 * 异常暂停
		 */
		public static final String MES_TASK_EXCEPTION_PAUSE = "5";

		/**
		 * 取消
		 */
		public static final String MES_TASK_CANCEL="6";
		
		/**
		 * 逻辑变更[Byron]---开始--来料检验单默认执行人字典编码
		 */
		/**
		 * 检验任务默认执行人配置编码
		 */
		public static final String MES_CHECK_TASK_OPER_USER ="MES_CHECK_TASK_OPER_USER";
		/**
		 * 逻辑变更[Byron]---结束
		 */
	}

	/**
	 * 数据同步
	 * <AUTHOR>
	 *
	 */
	public class DataSync {

		/**
		 * 同步结果-成功
		 */
		public static final int RESULT_SUCCESS = 200;

		/**
		 * 同步结果-失败
		 */
		public static final int RESULT_FAIL = 500;

		/**
		 * 表结构同步结果-无数据
		 */
		public static final int TABLE_RESULT_FAIL_NO_DATA = 501;

		/**
		 * 表结构同步结果-数据太多
		 */
		public static final int TABLE_RESULT_FAIL_DATA_TOO_MANY = 502;

		/**
		 * 返回结果-表已存在
		 */
		public static final int TABLE_RESULT_TABLE_EXIST = 503;

		/**
		 * 返回结果-源表已被占用
		 */
		public static final int TABLE_RESULT_SOURCE_TABLE_OCCUPIED = 504;

		/**
		 * 返回结果-定时任务创建失败
		 */
		public static final int TABLE_RESULT_JOB_CREATE_ERROR = 505;

		/**
		 * 返回结果-定时任务编辑失败
		 */
		public static final int TABLE_RESULT_JOB_UPDATE_ERROR = 506;

		/**
		 * 返回结果-定时任务启动失败
		 */
		public static final int TABLE_RESULT_JOB_START_ERROR = 507;
		/**
		 * 返回结果-定时任务暂停失败
		 */
		public static final int TABLE_RESULT_JOB_STOP_ERROR = 508;
		/**
		 * 返回结果-定时任务删除失败
		 */
		public static final int TABLE_RESULT_JOB_REMOVE_ERROR = 509;

		/**
		 * 数据同步任务-编码前缀
		 */
		public static final String DATA_SYNC_TASK_NO_PREFIX = "data-sync-task:";

		/**
		 * 数据同步任务-定时任务描述
		 */
		public static final String DATA_SYNC_JOB_DESC = "U8业务数据同步";

		/**
		 * 同步任务执行状态-就绪
		 */
		public static final String TASK_RECORD_STATUS_READY = "0";
		/**
		 * 同步任务执行状态-执行中
		 */
		public static final String TASK_RECORD_STATUS_RUNNING = "1";
		/**
		 * 同步任务执行状态-成功
		 */
		public static final String TASK_RECORD_STATUS_SUCCESS = "2";
		/**
		 * 同步任务执行状态-失败
		 */
		public static final String TASK_RECORD_STATUS_FAILED = "3";

		/**
		 * 数据同步任务默认Cron
		 */
		public static final String TASK_DEFAULT_CRON = "0 0 0 * * ?";
	}
	/**
	 * 异常来源
	 */
	public class ExceptionSource{
		public static final String SOURCE_PRODUCE="produce";//生产异常
		public static final String SOURCE_PROCESS="process";//过程检异常
		public static final String SOURCE_COMMODITY="commodity";//成品检异常
		public static final String SOURCE_ORDERCHANGE="change";//场外需求变更
		public static final String SOURCE_ORDERREPAIR="repair";//当月返厂

	}
	/**
	 * 异常状态
	 */
	public class ExceptionStatus{
		public static final String STATUS_NO="1";//未开始
		public static final String STATUS_AFOOT="2";//进行中
		public static final String STATUS_END="5";//已完成
		public static final String STATUS_CLOSE="6";//已关闭
		public static final String STATUS_STOP="3";//已暂停

	}
	public class ExceptionTypeCode{
		public static final String PID="7de4a4015a96e4abdbb1d0205c5950a2";//异常类型获取
	}
	/**
	 * 返工返修类型
	 */
	public class ExceptionHandleType{
		public static final String EXCEPTION_HANDLE_TYPE_CHANGE="5";//场外需求变更
		public static final String EXCEPTION_HANDLE_TYPE_REPAIR="m6";//场外需求变更
	}

	/**
	 * 产品数据来源
	 * <AUTHOR>
	 *
	 */
	public class ProductSource{

		public static final String MES_Product_Source_MES="MES";
		public static final String MES_Product_Source_U8="U8";
	}

	/**
	 * MRB评审单状态
	 */
	public class MrbStatus{

		public static final String MRB_STATUS_WAIT="0";//待处理
		public static final String MRB_STATUS_WAIT_REVIEW="1";//待评审
		public static final String MRB_STATUS_REVIEWING="2";//评审中
		public static final String MRB_STATUS_COMPLATE="3";//已完成

	}

	/**
	 * MRB评审类型
	 */
	public class MrbReviewType{

		public static final String MRB_REVIRE_TYPE_PRODUCT="0";//物料不合格
		public static final String MRB_REVIRE_TYPE_PROCESS="1";//制程内不合格
		public static final String MRB_REVIRE_TYPE_RETURN="2";//客户退回不合格
		public static final String MRB_REVIRE_TYPE_REPAIR="3"; //维修单不合格
		public static final String MRB_REVIRE_TYPE_REVIEW="4"; //评估单不合格
	}

	/**
	 * MRB处理方案
	 */
	public class MrbSolution{
		public static final String MRB_SOLUTION_SPECIAL="0";//特采
		public static final String MRB_SOLUTION_REWORK="1";//返工/返修
		public static final String MRB_SOLUTION_SCRAP="2";//报废重制
		public static final String MRB_SOLUTION_RETURN="3";//退货
		public static final String MRB_SOLUTION_EXCHANGE="4";//换货
		public static final String MRB_SOLUTION_SCRAP_MATERIAL="5";//换货
	}

	public class MrbReviewTaskStatus{
		public static final String MRB_TASK_STATUS_0="0";//待审批
		public static final String MRB_TASK_STATUS_1="1";//已审批
	}

	/**
	 * 销售订单任务状态
	 * <AUTHOR>
	 *
	 */
	public class SaleOrderStatus{
		/**
		 * 任务状态未开始
		 */
		public static final String MES_SALE_STATUS_NO_START = "0";
		/**
		 * 任务状态进行中
		 */
		public static final String MES_SALE_STATUS_ING = "1";
		/**
		 * 任务状态正常暂停
		 */
		public static final String MES_SALE_STATUS_STOP = "2";
		/**
		 * 任务状态取消
		 */
		public static final String MES_SALE_STATUS_CANCEL = "3";
		/**
		 * 任务状态完成
		 */
		public static final String MES_SALE_STATUS_FINISH = "4";
		/**
		 * 任务状态未发货
		 */
		public static final String MES_SALE_STATUS_SENDNO = "5";
		/**
		 * 任务状态已发货
		 */
		public static final String MES_SALE_STATUS_SEND = "6";
		/**
		 * 任务状态驳回
		 */
		public static final String MES_SALE_STATUS_RESEND = "7";

	}
	/**
	 * 销售订单任务发货状态
	 * <AUTHOR>
	 *
	 */
	public class SaleOrderSendStatus{
		/**
		 * 待通知发货
		 */
		public static final String MES_SALE_STATUS_SENDNO = "0";
		/**
		 * 已通知发货
		 */
		public static final String MES_SALE_STATUS_SEND = "1";
		/**
		 * 已发货
		 */
		public static final String MES_SALE_STATUS_SENDALL = "2";
		/**
		 * 部分发货
		 */
		public static final String MES_SALE_STATUS_SENDSOME = "3";

		/**
		 * 任务状态驳回
		 */
		public static final String MES_SALE_STATUS_RESEND = "4";

		/**
		 * 任务状态驳回
		 */
		public static final String MES_SALE_STATUS_BACKRESEND = "5";
	}

	/**
	 * 销售订单需求变更状态
	 */
	public class SaleOrderDemandStatus{

		/**
		 * 设计待确认
		 */
		public static final String SALE_DEMAND_STATUS_DESINGER_WAIT = "1";
		/**
		 * 计划待确认
		 */
		public static final String SALE_DEMAND_STATUS_PLANER_WAIT = "2";
		/**
		 * 变更完成
		 */
		public static final String SALE_DEMAND_STATUS_DEMAND_COMPLATE = "3";

		/**
		 * 撤销变更
		 */
		public static final String SALE_DEMAND_STATUS_DEMAND_CANCEL = "4";
		/**
		 * 结构变更待确认
		 */
		public static final String SALE_DEMAND_STATUS_STRUCTURE_WAIT = "5";

		/**
		 * 硬件变更待确认
		 */
		public static final String SALE_DEMAND_STATUS_HARDWARE_WAIT = "6";

	}

	/**
	 * 需求变更操作类型
	 */
	public class DemandChangeOperate{

		public static final String DEMAND_CHANGE_OPERATE_ADD="0";

		public static final String DEMAND_CHANGE_OPERATE_STRUCTURE_CONFIRM="1";
		public static final String DEMAND_CHANGE_OPERATE_HARDWARE_CONFIRM="2";
		public static final String DEMAND_CHANGE_OPERATE_PLAN_CONFIRM="3";
		public static final String DEMAND_CHANGE_OPERATE_CANCEL="4";
	}

	/**
	 * 生产阶段
	 * <AUTHOR>
	 *
	 */
	public class ProductPhase{

		/**
		 * 未开始
		 */
		public static final String MES_PHASE_WKS = "WKS";

		/**
		 * 设计
		 */
		public static final String MES_PHASE_SJ = "SJ";

		/**
		 * 生产
		 */
		public static final String MES_PHASE_SC = "SC";

		/**
		 * 质检
		 */
		public static final String MES_PHASE_ZJ = "ZJ";

		/**
		 * 入库
		 */
		public static final String MES_PHASE_RK = "RK";

		/**
		 * 发货
		 */
		public static final String MES_PHASE_FH = "FH";

		/**
		 * 验收
		 */
		public static final String MES_PHASE_YS = "YS";

		/**
		 * 完成
		 */
		public static final String MES_PHASE_OK = "OK";

	}

	public class MesUser{

		/**
		 * 默认密码
		 */
		public static final String DEFAULT_PASSWORD = "maxOne@365";
	}

	/**
	 * 来料检验单类型
	 */
	public class MesInspectionType{

		/**
		 * 自购件检验
		 */
		public static final String HOMEMADE = "1";

		/**
		 * 客供件检验
		 */
		public static final String GUEST = "2";
	}

	/**
	 * 来料检验单状态
	 */
	public class MesMaterialInspectionState{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "1";

		/**
		 * 进行中
		 */
		public static final String PROGRESS = "2";

		/**
		 * 已完成
		 */
		public static final String COMPLETED = "3";
	}

	/**
	 * 评估检验单状态
	 */
	public class MesAssessmentState{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "1";

		/**
		 * 进行中
		 */
		public static final String PROGRESS = "2";

		/**
		 * 已完成
		 */
		public static final String COMPLETED = "3";

		/**
		 * 待确认
		 */
		public static final String CONFIRMING = "4";

		/**
		 * 暂停
		 */
		public static final String STOP  = "5";


		/**
		 * 取消
		 */
		public static final String CANCEL  = "6";
	}

	/**
	 * 维修卡检验单状态
	 */
	public class MesMaintenanceState{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "1";

		/**
		 * 进行中
		 */
		public static final String PROGRESS = "2";

		/**
		 * 待确认
		 */
		public static final String CONFIRMATION = "3";

		/**
		 * 已完成
		 */
		public static final String COMPLETED = "4";

		/**
		 * 暂停
		 */
		public static final String STOP  = "5";

		/**
		 * 取消
		 */
		public static final String CANCEL  = "6";
	}

	/**
	 * Rebuild检验单状态
	 */
	public class MesRebuildCheckState{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "1";

		/**
		 * 进行中
		 */
		public static final String PROGRESS = "2";

		/**
		 * 待确认
		 */
		public static final String CONFIRMATION = "3";

		/**
		 * 已完成
		 */
		public static final String COMPLETED = "4";

		/**
		 * 暂停
		 */
		public static final String STOP  = "5";

		/**
		 * 取消
		 */
		public static final String CANCEL  = "6";
	}

	/**
	 * 来料检验单是否紧急
	 */
	public class MesIsUrgent{

		/**
		 * 否
		 */
		public static final String NO = "1";

		/**
		 * 是
		 */
		public static final String YES = "2";
	}

	/**
	 * 是否需要来料检
	 */
	public class MesIfNeedCheck{

		/**
		 * 否
		 */
		public static final String NO = "0";

		/**
		 * 是
		 */
		public static final String YES = "1";
	}

	/**
	 * 是否需要客供件
	 */
	public class MesIfNeedGuest{

		/**
		 * 否
		 */
		public static final String NO = "0";

		/**
		 * 是
		 */
		public static final String YES = "1";
	}

	/**
	 * 来料检验单检验类型
	 */
	public class MesIncomingInspectionType{

		/**
		 * 全检
		 */
		public static final String FULL_CHECK = "1";

		/**
		 * 抽检
		 */
		public static final String SAMPLING  = "2";
		/**
		 * 抽检
		 */
		public static final String INTERVAL  = "3";
	}

	/**
	 * 安全库存管理物料状态
	 */
	public class MesMaterialState{

		/**
		 * 正常
		 */
		public static final String NORMAL = "0";

		/**
		 * 预警
		 */
		public static final String WARNING  = "1";
	}

	/**
	 * 是否开启
	 */
	public class IfOpen{

		/**
		 * 否
		 */
		public static final String NO = "0";

		/**
		 * 是
		 */
		public static final String YES  = "1";
	}

	/**
	 * 物料异常状态
	 */
	public class MesMaterialAnomaly{

		/**
		 * 待反馈
		 */
		public static final String FEEDBACK = "1";

		/**
		 * 待确认
		 */
		public static final String CONFIRMATION  = "2";

		/**
		 * 待退货
		 */
		public static final String RETURN  = "3";

		/**
		 * 待收货
		 */
		public static final String RECEIPT  = "4";

		/**
		 * 已完成
		 */
		public static final String COMPLETED  = "5";

		/**
		 * 待工程确认
		 */
		public static final String STANDBY_CONFIRMATION  = "6";

		/**
		 * 待评审
		 */
		public static final String PENDING_REVIEW  = "7";
		
		/**
		 * 已确认
		 */
		public static final String CONFIRMED  = "8";
	}

	/**
	 * 过程检状态
	 */
	public class MesCheckTaskStates{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "0";

		/**
		 * 进行中
		 */
		public static final String PROGRESS  = "1";

		/**
		 * 已完成
		 */
		public static final String COMPLETED  = "2";

		/**
		 * 待确认
		 */
		public static final String CONFIRMATION  = "3";

		/**
		 * 非完成状态
		 */
		public static final String NOCOMPLETED = "4";
		/**
		 * 暂停
		 */
		public static final String STOP  = "5";

		/**
		 * 取消
		 */
		public static final String CANCEL  = "6";
	}

	/**
	 * 出货检状态
	 */
	public class MesShippingCheckTaskStates{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "0";

		/**
		 * 进行中
		 */
		public static final String PROGRESS  = "1";

		/**
		 * 已完成
		 */
		public static final String COMPLETED  = "2";

		/**
		 * 待确认
		 */
		public static final String CONFIRMATION  = "3";

		/**
		 * 暂停
		 */
		public static final String STOP  = "4";

		/**
		 * 取消
		 */
		public static final String CANCEL  = "5";
	}

	/**
	 * 成品检验状态
	 */
	public class MesProductCheckTaskStates{

		/**
		 * 待处理
		 */
		public static final String REASONABLE = "0";

		/**
		 * 进行中
		 */
		public static final String PROGRESS  = "1";

		/**
		 * 待确认
		 */
		public static final String CONFIRMATION  = "2";

		/**
		 * 已完成
		 */
		public static final String COMPLETED  = "3";

		/**
		 * 驳回
		 */
		public static final String REJECT  = "4";

		/**
		 * 暂停
		 */
		public static final String STOP  = "5";

		/**
		 * 取消
		 */
		public static final String CANCEL  = "6";
	}

	public class QyStorageProductShippingState{
		/**
		 * 待通知发货
		 */
		public static final String  WAIT_NOTICE_SHIPPING="0";
		/**
		 * 已通知发货
		 */
		public static final String  NOTICED_SHIPPING="1";
		/**
		 * 已发货
		 */
		public static final String  SHIPPED="2";
		/**
		 * 部分发货
		 */
		public static final String  PART_SHIPPING="3";
	}

	/**
	 * 物料异常处理方案
	 */
	public class MesMaterialExceptionScheme{

		/**
		 * 退货
		 */
		public static final String RETURNED = "1";

		/**
		 * 换货
		 */
		public static final String TRANSFER  = "2";

		/**
		 * MRB评审
		 */
		public static final String MRB_REVIEW  = "3";

		/**
		 * 2.5D研发二次处理
		 */
		public static final String REPROCESSING  = "4";

		/**
		 * 场内处理重新测试
		 */
		public static final String FIELD_TREATMENT_RETESTING  = "5";

		/**
		 * 合格
		 */
		public static final String QUALIFY  = "6";

		/**
		 * 让步
		 */
		public static final String CONCESSION  = "7";


	}

	/**
	 * 公用是否
	 */
	public class If{

		/**
		 * 否
		 */
		public static final String NO = "0";

		/**
		 * 是
		 */
		public static final String YES  = "1";
	}

	/**
	 * 是否通过
	 */
	public class IfPassThrough{

		/**
		 * 不通过
		 */
		public static final String NO_PASS = "0";

		/**
		 * 通过
		 */
		public static final String PASS  = "1";
	}

	/**
	 * 过程检验单处理方案
	 */
	public class mesProcessInspection{

		/**
		 * 返工返修
		 */
		public static final String REWORK = "1";

		/**
		 * 重新质检
		 */
		public static final String REINSPECTION  = "2";
	}

	/**
	 * 成品检验单处理方案
	 */
	public class mesInspectionTest{

		/**
		 * 返工返修
		 */
		public static final String REWORK = "1";

		/**
		 * 重新质检
		 */
		public static final String REINSPECTION  = "2";
	}

	/**
	 * 每日来料到货状态
	 */
	public class mesArrivalState{

		/**
		 * 部分到货
		 */
		public static final String PARTIAL_ARRIVAL = "1";

		/**
		 * 全部到货
		 */
		public static final String FULL_ARRIVAL  = "2";
	}

	/**
	 * 客供物料来料类型
	 */
	public class mesIncomingType{

		/**
		 * 自制件
		 */
		public static final String HOMEMADE_PIECE = "1";

		/**
		 * 客供件
		 */
		public static final String GUEST_FEED  = "2";

		/**
		 * 2.5D
		 */
		public static final String TWO_FIVE  = "3";
	}

	/**
	 * 客供物料到货状态
	 */
	public class mesSupplyArrivalCondition{

		/**
		 * 未到货
		 */
		public static final String UNARRIVED = "1";

		/**
		 * 部分到货
		 */
		public static final String PARTIAL_ARRIVAL  = "2";

		/**
		 * 全部到货
		 */
		public static final String FULL_ARRIVAL  = "3";
	}

	public class schedulType{
		public  static  final  String VPC="1";
		public  static  final String CPC="2";
	}

	/**
	 * 标准工时类型
	 */
	public class mesStandardTimeType{

		/**
		 * 自购物料
		 */
		public static final String MATERIAL = "1";

		/**
		 * 客供物料
		 */
		public static final String GUEST  = "2";

		/**
		 * 新卡
		 */
		public static final String NEW_CARD  = "3";

		/**
		 * 2.5处理基板&盖板
		 */
		public static final String PROCESSING  = "4";

		/**
		 * 针卡Evaluation（维修&评估）
		 */
		public static final String EVALUATION  = "5";
	}
	/**
	 * 是否需要评估
	 */
	public class ReviewFlag{
		/**
		 * 需要
		 */
		public static final String NEED="1";
		/**
		 * 不需要
		 */
		public static final String NOTNEED="0";
	}

	/**
	 * 仓库操作类型
	 * <AUTHOR>
	 *
	 */
	public class warehosingType{
		/**
		 * 入库
		 */
		public static final String INPUT = "1";

		/**
		 * 出库
		 */
		public static final String OUTPUT = "2";

	}


	/**
	 * 销售单pcb供货方式
	 * <AUTHOR>
	 *
	 */
	public class pcbSupplierType {

		/**
		 * 客供
		 */
		public static final String GUEST  = "客供";

		/**
		 * 公版
		 */
		public static final String EDITION = "公版";

		/**
		 * 专版
		 */
		public static final String SPECIAL = "专版";

	}
	public  class ProjectWorking{
		/**
		 * 待处理
		 */
		public static final String REASONABLE = "0";

		/**
		 * 进行中
		 */
		public static final String PROGRESS  = "1";

		/**
		 * 已完成
		 */
		public static final String COMPLETED  = "2";

		/**
		 * 暂停
		 */
		public static final String STOP  = "2";
	}

	/**
	 * 文件业务模块
	 * <AUTHOR>
	 *
	 */
	public class businessType{


		/**
		 * 预销售订单
		 */
		public static final String PRE_SALE_ORDER = "preSaleOrder";

		/**
		 * 现场维修单
		 */
		public static final String REPAIR_ORDER = "repairOrder";

		/**
		 * 维修记录
		 */
		public static final String REPAIR_ORDER_DETAIL = "repairOrderDetail";
		/**
		 * 返厂异常单附件
		 */
		public static final String RETURN_EXCEPTION = "returnExceptionOrder";

		/**
		 * 异常附件导出
		 */
		public static final String RETURN_EXCEPTION_EXPORT = "returnExceptionOrderExport";

		/**
		 * 返厂异常单基础附件
		 */
		public static final String RETURN_EXCEPTION_BESE = "returnExceptionOrderBase";
		/**
		 * 返厂异常单FAE现场处理其他附件
		 */
		public static final String RETURN_EXCEPTION_FAE_OTHER = "returnExceptionOrderFaeOther";
		/**
		 * 返厂异常单FAE现场处理testlog附件
		 */
		public static final String RETURN_EXCEPTION_FAE_TEST_LOG = "returnExceptionOrderFaeTestLog";
		/**
		 * 返厂异常单FAE异常指示附件
		 */
		public static final String RETURN_EXCEPTION_FAE_EXCEPTION_INDICATION = "returnExceptionFaeExceptionIndication";
		/**
		 * 返厂异常单设计资料
		 */
		public static final String RETURN_EXCEPTION_DESIGN = "returnExceptionDesign";
		/**
		 * 返厂异常单生产驳回
		 */
		public static final String RETURN_EXCEPTION_REJECT = "returnExceptionReject";
		/**
		 * 返厂异常单客户验证log
		 */
		public static final String RETURN_EXCEPTION_CLENT_LOG = "returnExceptionClentLog";
		/**
		 * 返厂异常单客户验证8d
		 */
		public static final String RETURN_EXCEPTION_CLENT_8D = "returnExceptionClent8d";

		/**
		 * CSR确认附件
		 */
		public static final String RETURN_EXCEPTION_CSR = "returnExceptionCSR";


		/**
		 * 维修记录
		 */
		public static final String REPAIR_CHECK_DETAIL = "repairCustomerCheckDetail";
		/**
		 * 费用依据记录
		 */
		public static final String REPAIR_RECORD_FEE = "repairRecordFee";
		/**
		 * 异常现象附件列表 abnormalDTOFileList
		 */
		public static final String REPAIR_RECORD_ABNORMAL = "repairRecordAbnormal";
		/**
		 * testLog附件列表
		 */
		public static final String REPAIR_RECORD_TESRLOG = "repairRecordTestLog";
		/**
		 * 维修内容附件列表
		 */
		public static final String REPAIR_RECORD_REPAIR = "repairRecordRepair";
		/**
		 * 其他报告附件
		 */
		public static final String REPAIR_RECORD_OTHER_REPORT = "repairRecordOtherReport";
		/**
		 * 纠正或工变附件
		 */
		public static final String REPAIR_RECORD_CORRECT_CHANGE = "repairRecordCorrectChange";

		/**
		 * 异常重置附件
		 */
		public static final String REPAIR_RECORD_ABNORMAL_RESET = "repairRecordAbnormalReset";
		/**
		 * 物料报废附件
		 */
		public static final String REPAIR_RECORD_MATERIAL_SCRAP = "repairRecordMaterialScrap";

		/**
		 * 工变附件
		 */
		public static final String REPAIR_RECORD_WORK_CHANGE = "repairRecordWorkChange";
		/**
		 * 销售订单设计
		 */
		public static final String SALE_ORDER_DESIGN = "saleOrderDesign";

		/**
		 * 预销售订单评估
		 */
		public static final String PRE_SALE_ORDER_DESIGN = "preSaleOrderDesign";

		/**
		 * 设计/评估任务
		 */
		public static final String SALE_ORDER_MISSION = "saleOrderMission";

		/**
		 * 设计/评估看板
		 */
		public static final String SALE_ORDER_KANBAN = "saleOrderKanban";

		/**
		 * 试验单管理
		 */
		public static final String TEST_TEST = "testTest";

		/**
		 * 生产任务
		 */
		public static final String PRODUCE_TASK = "produceTask";

		/**
		 * 返工返修
		 */
		public static final String EXCEPTION_ORDER = "exceptionOrder";

		/**
		 * mrb
		 */
		public static final String MRB = "mrb";

		/**
		 * 质检任务
		 */
		public static final String CHECK_TASK = "checkTask";

		/**
		 * 物料异常
		 */
		public static final String MATERIAL_A_B = "materialAB";

		/**
		 * 厂内加工
		 */
		public static final String IN_PLANT = "inPlant";

		/**
		 * 激光任务
		 */
		public static final String LASER_TASK = "laserTask";

		/**
		 * cnc任务
		 */
		public static final String CNC_TASK = "cncTask";

		/**
		 * 成品库存/发货单
		 */
		public static final String PRODUCT = "product";

		/**
		 * 邮件
		 */
		public static final String EMAIL = "email";

		public static final String SALE_DEMAND_CHANGE = "demandChange";
		/**
		 * 发货单记录
		 */
		public static final String PRODUCTRECORD = "productRecord";


	}

	public class ConfirmType{
		/**
		 * fae新建
		 */
		public static final String ADD = "add";
		/**
		 * CSR确认
		 */
		public static final String CSR = "csr";

		/**
		 * 设计
		 */
		public static final String DESIGN = "design";
		/**
		 * 驳回
		 */
		public static final String REJECT = "reject";
		/**
		 * 重新提交
		 */
		public static final String SUBMIT = "submit";

		/**
		 * 完成
		 */
		public static final String COMPLETE = "complete";

		/**
		 * 计划
		 */
		public static final String PLAN = "plan";

		/**
		 * 修改
		 */
		public static final String EDIT = "edit";
		/**
		 * 入库
		 */
		public static final String IN_WAREHOUSE = "in_warehouse";

		/**
		 * 质检
		 */
		public static final String QUALITY_INSPECTION = "quality_inspection";

		/**
		 * 通知发货
		 */
		public static final String SHIPMENT_NOTIFIED = "shipment_notified";

		/**
		 * 通知发货驳回
		 */
		public static final String SHIPMENT_NOTIFIED_NO = "shipment_notified_no";
		/**
		 * 已发货
		 */
		public static final String SHIPPED = "shipped";

		/**
		 * 退货
		 */
		public static final String REFUND = "refund";

	}

	/**
	 * 文件所属
	 * <AUTHOR>
	 *
	 */
	public class affiliation{

		/**
		 * 客供资料
		 */
		public static final String GUEST = "guest";

		/**
		 * 设计资料
		 */
		public static final String DESIGN = "design";

		/**
		 * 生产资料
		 */
		public static final String PRODUCTION = "production";

		/**
		 * 质检资料
		 */
		public static final String QUALITY = "quality";

	}

	/**
	 * MRB评审单状态
	 */
	public class ExceptionType{

		public static final String type_change="change";//需求变更
		public static final String type_normal="normal";//普通
		public static final String type_repair="repair";//当月返厂

	}

	/**
	 * 报工管理状态
	 */
	public class MesWorkingState{

		public static final String ROUGH_DRAFT="1";//草稿
		public static final String APPROVAL="2";//待审批
		public static final String PASS="3";//审批通过
		public static final String REJECT="4";//驳回

	}

	public class ProcessClassfication{
		public static final String GCJY="GCJY_GCJ";//过程检
	}

	public class ReturnType{
		public static  final  String RETURN_TYPE_QC="1";
		public static  final  String RETURN_TYPE_OQC="2";
	}

	public class EXCEPTION_WARNING{
		public static final String MES_EXCEPTION_WARNING  = "MES_EXCEPTION_WARNING";
	}

	public class FILE_TYPE{
		public static final String SHIPPING_RECORD  = "shippingRecord";

	}

	/**
	 * 返厂异常单状态
	 */
	public class REUTN_EXCEPTION_STATUS{
		public static final String CREATE="CREATE";//新增待CSR确认
		public static final String CSR_CONFIRM="CSR_CONFIRM";//csr确认后带工程确认
		public static final String DESIGN_CONFIRM="DESIGN_CONFIRM";//工程确认后就是待计划确认
		public static final String REJECT="REJECT";//驳回
		public static final String SUBMIT="SUBMIT";//重新提交
		public static final String COMPLETE="COMPLETE";//fae客户验证ok
		public static final String PLAN_CONFIRM="PLAN_CONFIRM";//计划确认后就是生产中

		public static final String IN_WAREHOUSE="IN_WAREHOUSE";//出货检验后为入库
		public static final String QUALITY_INSPECTION="QUALITY_INSPECTION";//成品检验单生成后为入库
		public static final String PLAN_REJECT="PLAN_REJECT";//计划驳回状态为计划驳回待CSR确认
		public static final String SHIPMENT_NOTIFIED="SHIPMENT_NOTIFIED";//已通知发货

		/**
		 * 已通知发货驳回
		 */
		public static final String SHIPMENT_NOTIFIED_NO = "SHIPMENT_NOTIFIED_NO";
		/**
		 * 已发货
		 */
		public static final String SHIPPED = "SHIPPED";

		/**
		 * 退货
		 */
		public static final String REFUND = "REFUND";

	}

	public class RETURN_CLIENT_RESULT{
		public static final String OK="1";//OK
		public static final String NG="2";//NG
		public static final String WAIT="3";//待验证


	}
}


