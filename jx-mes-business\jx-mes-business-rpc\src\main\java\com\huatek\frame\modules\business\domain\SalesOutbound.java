package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;

import lombok.Getter;
import lombok.Setter;

/**
* @description 销售出库
* <AUTHOR>
* @date 2025-08-06
**/
@Setter
@Getter
@TableName("sales_outbound")
public class SalesOutbound implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    //备注
    @TableField(value="remark")
    private String remark;
    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    /**
     * 产品入库id
     **/
    @TableField(value = "product_inventory_id"
    )
    private String productInventoryId;


    /**
	 * 客户收件人
     **/
    @TableField(value = "recipient_customer"
    )
    private String recipientCustomer;

    
    /**
	 * 收件电话
     **/
    @TableField(value = "recipient_phone"
    )
    private String recipientPhone;

    
    /**
	 * 收件人地址
     **/
    @TableField(value = "recipient_address"
    )
    private String recipientAddress;

    
    /**
	 * 快递公司
     **/
    @TableField(value = "express_company"
    )
    private String expressCompany;

    
    /**
	 * 快递单号
     **/
    @TableField(value = "express_waybill_number"
    )
    private String expressWaybillNumber;

    
    /**
	 * 发货日期
     **/
    @TableField(value = "shipping_date"
    )
    private String shippingDate;


    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}