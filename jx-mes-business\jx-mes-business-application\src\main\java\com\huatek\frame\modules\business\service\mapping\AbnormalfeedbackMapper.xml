<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.AbnormalfeedbackMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.abnormal_number as abnormalNumber,
		t.submitter as submitter,
		t.abnormal_type as abnormalType,
		t.department_of_feedback as departmentOfFeedback,
		t.processor as processor,
<!--		t.handling_department as handlingDepartment,-->
		t.to_notify as toNotify,
		t.abnormal_description as abnormalDescription,
		t.abnormal_date as abnormalDate,
		t.status as status,
		t.resolution_date as resolutionDate,
		t.`comment` as `comment`,
		t.solution_measures as solutionMeasures,
		t.upload_attachment as uploadAttachment,
		t.attachment as attachment,
        t.manufacturer as manufacturer,
		t.whether_to_enable as whetherToEnable,
		t.process_name2 as processName2,
		t.production_work_order as productionWorkOrder,
		t.orders as orders,
		t.entrusted_unit as entrustedUnit,
		t.product_model as productModel,
		t.product_name as productName,
		t.batch_number as batchNumber,
		t.quantity as quantity,
		t.CODEX_TORCH_CREATOR_ID as codexTorchCreatorId,
		t.CODEX_TORCH_UPDATER as codexTorchUpdater,
		t.CODEX_TORCH_GROUP_ID as codexTorchGroupId,
		t.CODEX_TORCH_CREATE_DATETIME as codexTorchCreateDatetime,
		t.CODEX_TORCH_UPDATE_DATETIME as codexTorchUpdateDatetime,
		t.CODEX_TORCH_DELETED as codexTorchDeleted
	</sql>
	<select id="selectAbnormalfeedbackPage" parameterType="com.huatek.frame.modules.business.service.dto.AbnormalfeedbackDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO">
		select
		<include refid="Base_Column_List" />, GROUP_CONCAT(u.user_name) as userNames,
        p.user_name as submitterName,
        g.user_name as processorName,
        o.group_name as handlingDepartment
			from abnormalfeedback t
            LEFT JOIN sys_user u ON
            FIND_IN_SET(u.id, t.to_notify) > 0
            LEFT JOIN sys_user p ON p.id = t.submitter
            LEFT JOIN sys_user g ON g.id = t.processor
            LEFT JOIN sys_group o ON o.id = t.handling_department
            <where>
                and 1=1
                <if test="abnormalNumber != null and abnormalNumber != ''">
                    and t.abnormal_number  like concat('%', #{abnormalNumber} ,'%')
                </if>
                <if test="abnormalType != null and abnormalType != ''">
                    and t.abnormal_type  = #{abnormalType}
                </if>
                <if test="departmentOfFeedback != null and departmentOfFeedback != ''">
                    and t.department_of_feedback  like concat('%', #{departmentOfFeedback} ,'%')
                </if>
<!--                <if test="submitter != null and submitter != ''">-->
<!--                    and t.submitter  like concat('%', #{submitter} ,'%')-->
<!--                </if>-->
<!--                <if test="currentUserId != null and currentUserId != ''">-->
<!--                    and FIND_IN_SET(#{currentUserId}, t.to_notify)-->
<!--                </if>-->
                <if test="(submitter != null and submitter != '') and (currentUserId != null and currentUserId != '')">
                    and (t.submitter like concat('%', #{submitter} ,'%') OR FIND_IN_SET(#{currentUserId}, t.to_notify))
                </if>
                <if test="processor != null and processor != ''">
                    and t.processor  like concat('%', #{processor} ,'%')
                </if>
                <if test="handlingDepartment != null and handlingDepartment != ''">
                    and t.handling_department  like concat('%', #{handlingDepartment} ,'%')
                </if>
                <if test="toNotify != null and toNotify != ''">
                    and t.to_notify REGEXP #{toNotify}
                </if>
                <if test="abnormalDescription != null and abnormalDescription != ''">
                    and t.abnormal_description  like concat('%', #{abnormalDescription}, '%')
                </if>
                <if test="abnormalDate != null">
                    and t.abnormal_date  = #{abnormalDate}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="resolutionDate != null">
                    and t.resolution_date  = #{resolutionDate}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="solutionMeasures != null and solutionMeasures != ''">
                    and t.solution_measures  = #{solutionMeasures}
                </if>
                <if test="uploadAttachment != null and uploadAttachment != ''">
                    and t.upload_attachment  = #{uploadAttachment}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="whetherToEnable != null and whetherToEnable != ''">
                    and t.whether_to_enable  = #{whetherToEnable}
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="productionWorkOrder != null and productionWorkOrder != ''">
                    and t.production_work_order  like concat('%', #{productionWorkOrder} ,'%')
                </if>
                <if test="orders != null and orders != ''">
                    and t.orders  like concat('%', #{orders} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  like concat('%', #{quantity} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
        group by t.id
        order by t.CODEX_TORCH_CREATE_DATETIME DESC
	</select>
     <select id="selectOptionsBySettlementUnit" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.settlement_unit label,
        	t.customer_id0 value
        from customer_information_management t
        WHERE t.customer_id0 != ''
     </select>
     <select id="selectOptionsByToNotify" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.name label,
        	t.id value
        from sys_user t
        WHERE t.name != ''
     </select>

    <select id="selectAbnormalfeedbackList" parameterType="com.huatek.frame.modules.business.service.dto.AbnormalfeedbackDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO">
		select
		<include refid="Base_Column_List" />, GROUP_CONCAT(DISTINCT u.user_name) as userNames, p.user_name as submitterName, g.user_name as processorName
        from abnormalfeedback t
        LEFT JOIN sys_user u ON
        FIND_IN_SET(u.id, t.to_notify) > 0
        LEFT JOIN sys_user p ON p.id = t.submitter
        LEFT JOIN sys_user g ON g.id = t.processor


            <where>
                and 1=1
                <if test="abnormalNumber != null and abnormalNumber != ''">
                    and t.abnormal_number  like concat('%', #{abnormalNumber} ,'%')
                </if>
                <if test="submitter != null and submitter != ''">
                    and t.submitter  like concat('%', #{submitter} ,'%')
                </if>
                <if test="abnormalType != null and abnormalType != ''">
                    and t.abnormal_type  = #{abnormalType}
                </if>
                <if test="departmentOfFeedback != null and departmentOfFeedback != ''">
                    and t.department_of_feedback  like concat('%', #{departmentOfFeedback} ,'%')
                </if>
                <if test="processor != null and processor != ''">
                    and t.processor  like concat('%', #{processor} ,'%')
                </if>
                <if test="handlingDepartment != null and handlingDepartment != ''">
                    and t.handling_department  like concat('%', #{handlingDepartment} ,'%')
                </if>
                <if test="toNotify != null and toNotify != ''">
                    and t.to_notify REGEXP #{toNotify}
                </if>
                <if test="abnormalDescription != null and abnormalDescription != ''">
                    and t.abnormal_description  = #{abnormalDescription}
                </if>
                <if test="abnormalDate != null">
                    and t.abnormal_date  = #{abnormalDate}
                </if>
                <if test="status != null and status != ''">
                    and t.status  = #{status}
                </if>
                <if test="resolutionDate != null">
                    and t.resolution_date  = #{resolutionDate}
                </if>
                <if test="comment != null and comment != ''">
                    and t.comment  like concat('%', #{comment} ,'%')
                </if>
                <if test="solutionMeasures != null and solutionMeasures != ''">
                    and t.solution_measures  = #{solutionMeasures}
                </if>
                <if test="uploadAttachment != null and uploadAttachment != ''">
                    and t.upload_attachment  = #{uploadAttachment}
                </if>
                <if test="attachment != null and attachment != ''">
                    and t.attachment  = #{attachment}
                </if>
                <if test="whetherToEnable != null and whetherToEnable != ''">
                    and t.whether_to_enable  = #{whetherToEnable}
                </if>
                <if test="processName2 != null and processName2 != ''">
                    and t.process_name2  like concat('%', #{processName2} ,'%')
                </if>
                <if test="productionWorkOrder != null and productionWorkOrder != ''">
                    and t.production_work_order  like concat('%', #{productionWorkOrder} ,'%')
                </if>
                <if test="orders != null and orders != ''">
                    and t.orders  like concat('%', #{orders} ,'%')
                </if>
                <if test="entrustedUnit != null and entrustedUnit != ''">
                    and t.entrusted_unit  like concat('%', #{entrustedUnit} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and t.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="batchNumber != null and batchNumber != ''">
                    and t.batch_number  like concat('%', #{batchNumber} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  like concat('%', #{quantity} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.CODEX_TORCH_CREATOR_ID  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.CODEX_TORCH_UPDATER  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.CODEX_TORCH_GROUP_ID  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.CODEX_TORCH_CREATE_DATETIME  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.CODEX_TORCH_UPDATE_DATETIME  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.CODEX_TORCH_DELETED  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
            GROUP BY t.id, p.user_name, g.user_name
	</select>

    <select id="selectAbnormalfeedbackListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO">
		select
		<include refid="Base_Column_List" />
			from abnormalfeedback t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    <select id="getAbnormalsOfWorkOrderNumberORProcessId"
            resultType="com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO">
        select
        <include refid="Base_Column_List"/>
        from abnormalfeedback t
        where t.production_work_order = #{productionWorkOrder} and t.customer_process_id = #{customerProcessId}
    </select>
    <select id="getAbnormalDetailsBySourceId"
            resultType="com.huatek.frame.modules.business.domain.vo.AbnormalfeedbackVO">
        select
        <include refid="Base_Column_List" />, GROUP_CONCAT(u.user_name) as userNames,
        p.user_name as submitterName,
        g.user_name as processorName,
        o.group_name as handlingDepartment
        from abnormalfeedback t
        LEFT JOIN sys_user u ON
        FIND_IN_SET(u.id, t.to_notify) > 0
        LEFT JOIN sys_user p ON p.id = t.submitter
        LEFT JOIN sys_user g ON g.id = t.processor
        LEFT JOIN sys_group o ON o.id = t.handling_department
        where t.source_id = #{sourceId}
    </select>
</mapper>