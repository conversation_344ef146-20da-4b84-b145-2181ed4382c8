package com.huatek.frame.insite.message.config;

import com.huatek.frame.insite.message.handler.ChatRoomSessionIdWebsocketHandler;
import com.huatek.frame.insite.message.handler.ChatRoomUserIdWebsocketHandler;
import com.huatek.frame.insite.message.handler.ConnectWebsocketHandler;
import com.huatek.frame.insite.message.handler.PushMessageWebsocketHandler;
import com.huatek.frame.insite.message.interceptor.WebSocketInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;


@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {


    @Autowired
    private PushMessageWebsocketHandler pushMessageWebsocketHandler;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        //连接websocket测试
        registry.addHandler(new ConnectWebsocketHandler(), "/websocket")
                .setAllowedOrigins("*"); // 允许跨域

        //聊天室 -- sessionId版
        registry.addHandler(new ChatRoomSessionIdWebsocketHandler(), "/websocket/chatRoomSessionId")
                .setAllowedOrigins("*"); // 允许跨域

        //聊天室 -- UserId版
        registry.addHandler(new ChatRoomUserIdWebsocketHandler(), "/websocket/chatRoomUserId")
                .addInterceptors(new WebSocketInterceptor())//拦截器用来获取前端传递过来的userid
                .setAllowedOrigins("*"); // 允许跨域

        //消息推送
        registry.addHandler(pushMessageWebsocketHandler, "/websocket/pushMessage")
                .addInterceptors(new WebSocketInterceptor())//拦截器用来获取前端传递过来的userid
                .setAllowedOrigins("*"); // 允许跨域

    }

    @Bean
    public PushMessageWebsocketHandler webSocketMessageSender() {
        return new PushMessageWebsocketHandler();
    }

}
